# ADR 004: Multi-Currency Database Design

## Status
Accepted

## Context

VibeLaunch Genesis requires a database design that can efficiently handle five different currency types (₥◈⧗☆◊) with their unique properties:

- **Economic (₥)**: Traditional monetary value, high-frequency transactions
- **Quality (◈)**: Multiplicative effects, range 0-2.0, affects other currencies
- **Temporal (⧗)**: Time-decay properties, requires continuous updates
- **Reliability (☆)**: Non-transferable, yield-generating, range 0-1.0
- **Innovation (◊)**: Network-effect appreciation, governance-gated

Key requirements:
- Support 50,000+ transactions per second across all currencies
- Maintain ACID properties for multi-currency atomic operations
- Enable efficient querying and reporting across currency dimensions
- Enforce economic laws at the database level
- Support real-time balance calculations with currency-specific mechanics

## Decision

We will implement a **hybrid normalized-denormalized database design** with:

1. **Separate currency-specific tables** for optimized operations
2. **Unified wallet table** for cross-currency operations
3. **PostgreSQL with custom extensions** for economic law enforcement
4. **TimescaleDB integration** for temporal currency decay tracking
5. **Materialized views** for real-time multi-currency aggregations

### Database Schema Structure

```sql
-- Core wallet table
CREATE TABLE wallets (
    id UUID PRIMARY KEY,
    owner_id UUID NOT NULL,
    owner_type TEXT CHECK (owner_type IN ('user', 'agent', 'contract', 'protocol')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0
);

-- Currency-specific balance tables
CREATE TABLE economic_balances (
    wallet_id UUID PRIMARY KEY REFERENCES wallets(id),
    balance DECIMAL(20,6) NOT NULL DEFAULT 0,
    locked_balance DECIMAL(20,6) NOT NULL DEFAULT 0,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0
);

CREATE TABLE quality_balances (
    wallet_id UUID PRIMARY KEY REFERENCES wallets(id),
    score DECIMAL(5,3) NOT NULL DEFAULT 1.000,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0,
    CONSTRAINT quality_range CHECK (score BETWEEN 0 AND 2)
);

CREATE TABLE temporal_balances (
    wallet_id UUID PRIMARY KEY REFERENCES wallets(id),
    amount DECIMAL(15,3) NOT NULL DEFAULT 0,
    last_decay_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0
);

CREATE TABLE reliability_scores (
    wallet_id UUID PRIMARY KEY REFERENCES wallets(id),
    score DECIMAL(5,5) NOT NULL DEFAULT 0.50000,
    last_yield_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0,
    CONSTRAINT reliability_range CHECK (score BETWEEN 0 AND 1)
);

CREATE TABLE innovation_balances (
    wallet_id UUID PRIMARY KEY REFERENCES wallets(id),
    amount DECIMAL(15,3) NOT NULL DEFAULT 0,
    last_appreciation_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0
);
```

## Alternatives Considered

### Alternative 1: Single Multi-Currency Table
```sql
CREATE TABLE wallets (
    id UUID PRIMARY KEY,
    economic_balance DECIMAL(20,6) DEFAULT 0,
    quality_score DECIMAL(5,3) DEFAULT 1.000,
    temporal_amount DECIMAL(15,3) DEFAULT 0,
    reliability_score DECIMAL(5,5) DEFAULT 0.50000,
    innovation_amount DECIMAL(15,3) DEFAULT 0,
    -- ... other fields
);
```

**Pros:**
- Simpler schema
- Easier atomic operations across currencies
- Single table queries for wallet state

**Cons:**
- Poor performance for currency-specific operations
- Difficult to optimize indexes for different access patterns
- Complex constraints for currency-specific rules
- Harder to partition for scale

**Rejected because:** Performance and scalability concerns outweigh simplicity benefits.

### Alternative 2: Document Database (MongoDB)
```javascript
{
  walletId: "uuid",
  currencies: {
    economic: { balance: 1000.50, locked: 0 },
    quality: { score: 1.25 },
    temporal: { amount: 500, lastDecay: "2024-01-01T00:00:00Z" },
    reliability: { score: 0.85, lastYield: "2024-01-01T00:00:00Z" },
    innovation: { amount: 100, lastAppreciation: "2024-01-01T00:00:00Z" }
  }
}
```

**Pros:**
- Flexible schema for evolving currency properties
- Natural fit for multi-currency data
- Easy to add new currency types

**Cons:**
- No ACID guarantees for financial operations
- Difficult to enforce economic law constraints
- Poor performance for high-frequency trading
- Limited analytical query capabilities

**Rejected because:** Financial systems require ACID guarantees and strong consistency.

### Alternative 3: Event Sourcing Only
```sql
CREATE TABLE currency_events (
    id UUID PRIMARY KEY,
    wallet_id UUID NOT NULL,
    currency_type TEXT NOT NULL,
    event_type TEXT NOT NULL,
    amount DECIMAL(20,6),
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB
);
```

**Pros:**
- Perfect audit trail
- Temporal queries possible
- Immutable event history

**Cons:**
- Complex balance calculations
- Poor query performance for current state
- Difficult real-time operations
- Complex currency-specific mechanics

**Rejected because:** Real-time balance queries are critical for trading operations.

## Consequences

### Positive

1. **Performance Optimization**
   - Currency-specific tables allow optimized indexes
   - Separate tables reduce lock contention
   - Partitioning strategies per currency type

2. **Scalability**
   - Independent scaling of currency operations
   - Targeted performance tuning per currency
   - Efficient sharding strategies

3. **Data Integrity**
   - Currency-specific constraints enforced at DB level
   - ACID properties maintained across operations
   - Custom functions for economic law validation

4. **Operational Efficiency**
   - Optimized queries for each currency type
   - Efficient bulk operations
   - Clear separation of concerns

### Negative

1. **Complexity**
   - More complex schema to maintain
   - Multiple tables to keep in sync
   - Complex migration procedures

2. **Development Overhead**
   - More complex ORM mappings
   - Multiple service interfaces
   - Cross-table consistency challenges

3. **Storage Overhead**
   - Potential data duplication
   - Multiple indexes to maintain
   - Higher storage requirements

### Mitigation Strategies

1. **Complexity Management**
   - Comprehensive database migration tools
   - Automated schema validation
   - Clear documentation and examples

2. **Consistency Assurance**
   - Database-level triggers for cross-table validation
   - Application-level transaction management
   - Regular consistency checks

3. **Performance Monitoring**
   - Currency-specific performance metrics
   - Automated query optimization
   - Proactive scaling based on usage patterns

## Implementation Plan

### Phase 1: Core Schema (Week 1-2)
- Implement basic wallet and currency tables
- Add essential constraints and indexes
- Create basic CRUD operations

### Phase 2: Economic Law Enforcement (Week 3-4)
- Implement custom PostgreSQL functions
- Add value conservation triggers
- Create currency-specific validation

### Phase 3: Performance Optimization (Week 5-6)
- Add partitioning strategies
- Optimize indexes based on usage patterns
- Implement caching layers

### Phase 4: Advanced Features (Week 7-8)
- TimescaleDB integration for temporal currency
- Materialized views for reporting
- Advanced monitoring and alerting

## Monitoring and Success Metrics

```yaml
database_metrics:
  performance:
    - transaction_throughput: "> 50,000 TPS"
    - query_latency_p99: "< 10ms"
    - connection_utilization: "< 80%"
    
  consistency:
    - value_conservation_violations: "0"
    - cross_table_inconsistencies: "0"
    - constraint_violations: "0"
    
  scalability:
    - storage_growth_rate: "linear with transactions"
    - query_performance_degradation: "< 5% per 10x data growth"
    - partition_efficiency: "> 95%"
```

## Related Decisions

- [ADR 001: Choice of Rust for Market Engine](./001-choice-of-rust-for-market-engine.md)
- [ADR 002: Adoption of Event Sourcing with Redis Streams](./002-adoption-of-event-sourcing-with-redis-streams.md)
- [ADR 003: Recommendation for Data Reconciliation Service](./003-recommendation-for-data-reconciliation-service.md)

## References

- PostgreSQL Documentation: https://www.postgresql.org/docs/
- TimescaleDB Documentation: https://docs.timescale.com/
- Financial Database Design Patterns: https://martinfowler.com/articles/patterns-of-enterprise-application-architecture.html
- Multi-Currency System Design: Internal VibeLaunch Economics Team Analysis
