# VibeLaunch Genesis Local Development Setup

## Overview

This guide provides comprehensive instructions for setting up a complete VibeLaunch Genesis development environment on your local machine. The setup includes all five currency types, economic law enforcement, and the full microservices architecture with hot reloading and debugging capabilities.

## Prerequisites

### System Requirements
- **OS**: macOS 12+, Ubuntu 20.04+, or Windows 11 with WSL2
- **CPU**: 8+ cores (16+ recommended for full stack)
- **RAM**: 32GB minimum (64GB recommended)
- **Storage**: 100GB+ free space (SSD recommended)
- **Network**: Stable internet connection for container downloads

### Required Software
```bash
# Core development tools
- Docker Desktop 4.20+
- Docker Compose v2.20+
- Node.js 20+ with npm/yarn
- Rust 1.70+ with Cargo
- Go 1.21+
- Python 3.11+
- kubectl 1.28+
- Helm 3.12+

# Development tools
- VS Code with recommended extensions
- Git 2.40+
- jq for JSON processing
- curl for API testing
```

## Quick Start (5-Minute Setup)

### 1. Clone and Initialize
```bash
# Clone the repository
git clone https://github.com/vibelaunch/genesis.git
cd genesis

# Run the quick setup script
./scripts/dev-setup.sh

# Start the development environment
docker-compose -f docker-compose.dev.yml up -d

# Verify all services are running
./scripts/verify-dev-environment.sh
```

### 2. Access Development Services
```yaml
development_endpoints:
  api_gateway: "http://localhost:3000"
  market_engine: "http://localhost:50051"
  currency_service: "http://localhost:50052"
  contract_manager: "http://localhost:50053"
  
  databases:
    postgresql: "localhost:5432"
    redis: "localhost:6379"
    
  monitoring:
    grafana: "http://localhost:3001"
    prometheus: "http://localhost:9090"
    jaeger: "http://localhost:16686"
```

## Detailed Setup

### 1. Environment Configuration

```bash
# Create development environment file
cat > .env.development << EOF
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=vibelaunch_dev
POSTGRES_USER=dev_user
POSTGRES_PASSWORD=dev_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=dev_redis_password

# Service Configuration
MARKET_ENGINE_PORT=50051
CURRENCY_SERVICE_PORT=50052
CONTRACT_MANAGER_PORT=50053
AGENT_COORDINATOR_PORT=50054

# Economic Configuration
TARGET_EFFICIENCY=0.95
MAX_SYNERGY_FACTOR=1.944
VALUE_CONSERVATION_TOLERANCE=0.000001

# Development Features
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_LOGGING=true
ENABLE_METRICS=true
ENABLE_TRACING=true
EOF
```

### 2. Database Setup

```bash
#!/bin/bash
# setup-databases.sh

echo "Setting up development databases..."

# Start PostgreSQL with TimescaleDB
docker run -d \
  --name vibelaunch-postgres \
  -e POSTGRES_DB=vibelaunch_dev \
  -e POSTGRES_USER=dev_user \
  -e POSTGRES_PASSWORD=dev_password \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  timescale/timescaledb:latest-pg15

# Wait for PostgreSQL to be ready
until docker exec vibelaunch-postgres pg_isready -U dev_user; do
  echo "Waiting for PostgreSQL..."
  sleep 2
done

# Run database migrations
docker exec -i vibelaunch-postgres psql -U dev_user -d vibelaunch_dev << EOF
-- Create extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create economic laws extension (custom)
\i /docker-entrypoint-initdb.d/economic_laws_extension.sql

-- Run migrations
\i /migrations/001_initial_schema.sql
\i /migrations/002_currency_tables.sql
\i /migrations/003_economic_constraints.sql
\i /migrations/004_dev_seed_data.sql
EOF

echo "Database setup complete"
```

### 3. Service Development Setup

#### Market Engine (Rust)
```bash
# Navigate to market engine
cd services/market-engine

# Install dependencies
cargo build

# Set up development configuration
cat > config/development.toml << EOF
[server]
host = "0.0.0.0"
port = 50051

[database]
url = "postgresql://dev_user:dev_password@localhost:5432/vibelaunch_dev"
max_connections = 10

[redis]
url = "redis://localhost:6379"

[performance]
max_orders_per_second = 10000  # Reduced for development
latency_target_ms = 50         # Relaxed for development

[logging]
level = "debug"
format = "pretty"
EOF

# Run with hot reload
cargo watch -x 'run --bin market-engine'
```

#### Currency Service (Go)
```bash
# Navigate to currency service
cd services/currency-service

# Install dependencies
go mod download

# Set up development configuration
cat > config/development.yaml << EOF
server:
  host: "0.0.0.0"
  port: 50052

database:
  host: "localhost"
  port: 5432
  name: "vibelaunch_dev"
  user: "dev_user"
  password: "dev_password"
  max_connections: 10

redis:
  host: "localhost"
  port: 6379
  password: "dev_redis_password"

currencies:
  economic:
    precision: 6
    min_transfer: 0.01
    transfer_fee: 0.001
  quality:
    precision: 3
    min_score: 0.0
    max_score: 2.0

logging:
  level: "debug"
  format: "json"
EOF

# Run with hot reload
air  # Requires 'go install github.com/cosmtrek/air@latest'
```

#### Contract Manager (Node.js)
```bash
# Navigate to contract manager
cd services/contract-manager

# Install dependencies
npm install

# Set up development configuration
cat > config/development.json << EOF
{
  "server": {
    "host": "0.0.0.0",
    "port": 50053
  },
  "database": {
    "host": "localhost",
    "port": 5432,
    "database": "vibelaunch_dev",
    "username": "dev_user",
    "password": "dev_password"
  },
  "redis": {
    "host": "localhost",
    "port": 6379,
    "password": "dev_redis_password"
  },
  "logging": {
    "level": "debug",
    "format": "pretty"
  }
}
EOF

# Run with hot reload
npm run dev  # Uses nodemon for hot reloading
```

### 4. Development Tools Setup

#### VS Code Configuration
```json
// .vscode/settings.json
{
  "rust-analyzer.cargo.target": "x86_64-unknown-linux-gnu",
  "go.toolsManagement.autoUpdate": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  
  "files.associations": {
    "*.toml": "toml",
    "*.yaml": "yaml",
    "*.yml": "yaml"
  },
  
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  }
}

// .vscode/extensions.json
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "golang.go",
    "ms-vscode.vscode-typescript-next",
    "ms-python.python",
    "ms-kubernetes-tools.vscode-kubernetes-tools",
    "ms-vscode.docker",
    "redhat.vscode-yaml"
  ]
}
```

#### Debug Configurations
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Market Engine",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/services/market-engine/target/debug/market-engine",
      "args": [],
      "cwd": "${workspaceFolder}/services/market-engine",
      "env": {
        "RUST_LOG": "debug"
      }
    },
    {
      "name": "Debug Currency Service",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/services/currency-service/cmd/main.go",
      "cwd": "${workspaceFolder}/services/currency-service"
    },
    {
      "name": "Debug Contract Manager",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/services/contract-manager/src/index.ts",
      "outFiles": ["${workspaceFolder}/services/contract-manager/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"]
    }
  ]
}
```

## Development Workflows

### 1. Multi-Currency Testing
```typescript
// Development test script
import { CurrencyService } from './services/currency-service';
import { MarketEngine } from './services/market-engine';

async function testMultiCurrencyFlow() {
  const currencyService = new CurrencyService();
  const marketEngine = new MarketEngine();
  
  // Create test wallets
  const wallet1 = await currencyService.createWallet({
    ownerId: 'test-user-1',
    ownerType: 'user',
    initialBalances: {
      economic: 10000,
      quality: 1.0,
      temporal: 0,
      reliability: 0.5,
      innovation: 0
    }
  });
  
  const wallet2 = await currencyService.createWallet({
    ownerId: 'test-user-2', 
    ownerType: 'user',
    initialBalances: {
      economic: 5000,
      quality: 1.2,
      temporal: 100,
      reliability: 0.8,
      innovation: 50
    }
  });
  
  // Test multi-currency transfer
  const transferResult = await currencyService.atomicTransfer({
    from: wallet1.id,
    to: wallet2.id,
    amounts: {
      economic: 1000,
      quality: 0.1,
      temporal: 50
    },
    metadata: { test: 'multi-currency-transfer' }
  });
  
  console.log('Transfer result:', transferResult);
  
  // Test market order
  const orderResult = await marketEngine.submitOrder({
    traderId: wallet1.ownerId,
    orderType: 'limit',
    side: 'buy',
    currencies: {
      economic: 500,
      quality: 0.05
    },
    price: 1.0
  });
  
  console.log('Order result:', orderResult);
}

// Run test
testMultiCurrencyFlow().catch(console.error);
```

### 2. Economic Law Validation Testing
```bash
#!/bin/bash
# test-economic-laws.sh

echo "Testing economic law enforcement..."

# Test value conservation
curl -X POST http://localhost:50052/test/value-conservation \
  -H "Content-Type: application/json" \
  -d '{
    "transfers": [
      {"from": "wallet1", "to": "wallet2", "amount": 100},
      {"from": "wallet2", "to": "wallet3", "amount": 50}
    ]
  }'

# Test synergy detection
curl -X POST http://localhost:50054/test/synergy-detection \
  -H "Content-Type: application/json" \
  -d '{
    "agents": ["agent1", "agent2", "agent3"],
    "task": "content-creation",
    "expectedSynergy": 1.5
  }'

echo "Economic law tests completed"
```

### 3. Performance Testing
```bash
#!/bin/bash
# performance-test.sh

echo "Running performance tests..."

# Market engine throughput test
echo "Testing market engine throughput..."
hey -z 30s -q 1000 -c 50 \
  -H "Content-Type: application/json" \
  -d '{"orderType":"market","side":"buy","amount":100}' \
  http://localhost:50051/orders

# Currency service throughput test  
echo "Testing currency service throughput..."
hey -z 30s -q 500 -c 25 \
  -H "Content-Type: application/json" \
  -d '{"from":"wallet1","to":"wallet2","amount":10}' \
  http://localhost:50052/transfers

echo "Performance tests completed"
```

## Debugging and Troubleshooting

### 1. Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker exec vibelaunch-postgres pg_isready -U dev_user

# View PostgreSQL logs
docker logs vibelaunch-postgres

# Reset database
docker exec vibelaunch-postgres psql -U dev_user -d vibelaunch_dev -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
./scripts/run-migrations.sh
```

#### Service Communication Issues
```bash
# Check service health
curl http://localhost:50051/health  # Market Engine
curl http://localhost:50052/health  # Currency Service
curl http://localhost:50053/health  # Contract Manager

# View service logs
docker-compose logs market-engine
docker-compose logs currency-service
docker-compose logs contract-manager
```

### 2. Development Utilities

```bash
# Reset development environment
./scripts/reset-dev-environment.sh

# Seed test data
./scripts/seed-test-data.sh

# Run integration tests
./scripts/run-integration-tests.sh

# Generate test reports
./scripts/generate-test-reports.sh
```

## Contributing Guidelines

### 1. Code Standards
- **Rust**: Use `cargo fmt` and `cargo clippy`
- **Go**: Use `gofmt` and `golangci-lint`
- **TypeScript**: Use Prettier and ESLint
- **Python**: Use Black and Flake8

### 2. Testing Requirements
- Unit tests: 90%+ coverage
- Integration tests for all API endpoints
- Economic law validation tests
- Performance benchmarks

### 3. Pull Request Process
1. Create feature branch from `develop`
2. Implement changes with tests
3. Run full test suite locally
4. Submit PR with detailed description
5. Address review feedback
6. Merge after approval

This development setup provides a complete, production-like environment for building and testing VibeLaunch Genesis features while maintaining the highest standards of code quality and economic law enforcement.
