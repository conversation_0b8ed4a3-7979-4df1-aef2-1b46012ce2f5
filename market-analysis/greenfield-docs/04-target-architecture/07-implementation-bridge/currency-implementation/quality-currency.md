# Quality Currency (◈) Implementation Guide

## Overview

The Quality Currency (◈) represents the multiplicative quality factor in VibeLaunch Genesis, ranging from 0.0 to 2.0 with a default of 1.0. Unlike traditional currencies, Quality Currency acts as a multiplier for other currencies, creating exponential value increases for high-quality work while penalizing poor quality.

## Currency Properties

```typescript
interface QualityCurrencyProperties {
    symbol: '◈';
    name: 'Quality Currency';
    type: 'multiplicative';
    
    // Core characteristics
    transferable: false;        // Cannot be directly transferred
    divisible: true;           // Supports decimal precision
    yields: false;             // No passive yield
    decays: true;              // Decays without maintenance
    
    // Value range and precision
    minValue: 0.0;             // Minimum quality score
    maxValue: 2.0;             // Maximum quality score (200%)
    defaultValue: 1.0;         // Neutral quality (100%)
    precision: 3;              // 3 decimal places (0.001)
    
    // Decay properties
    decayRate: 0.001;          // 0.1% per day without activity
    decayThreshold: 0.5;       // Minimum decay level
    maintenanceRequired: true; // Requires ongoing quality work
    
    // Multiplicative effects
    appliesTo: ['economic', 'temporal', 'innovation'];
    multiplicativeFormula: 'base_amount × quality_score';
}
```

## Database Schema

```sql
-- Quality scores table
CREATE TABLE quality_scores (
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    current_score DECIMAL(5,3) NOT NULL DEFAULT 1.000,
    peak_score DECIMAL(5,3) NOT NULL DEFAULT 1.000,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    last_decay_applied TIMESTAMPTZ DEFAULT NOW(),
    
    -- Quality maintenance tracking
    maintenance_streak INTEGER DEFAULT 0,
    total_quality_work INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT quality_range CHECK (current_score BETWEEN 0.000 AND 2.000),
    CONSTRAINT peak_range CHECK (peak_score BETWEEN 0.000 AND 2.000),
    CONSTRAINT peak_above_current CHECK (peak_score >= current_score),
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0,
    
    PRIMARY KEY (wallet_id)
);

-- Quality work history (for score calculation)
CREATE TABLE quality_work_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    
    work_type TEXT NOT NULL CHECK (
        work_type IN ('contract_delivery', 'peer_review', 'innovation', 'mentoring', 'governance')
    ),
    
    quality_assessment DECIMAL(5,3) NOT NULL,
    assessor_id UUID REFERENCES wallets(id),
    assessment_method TEXT NOT NULL CHECK (
        assessment_method IN ('peer_review', 'client_feedback', 'automated_analysis', 'governance_vote')
    ),
    
    -- Impact on quality score
    score_impact DECIMAL(5,3) NOT NULL,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Work metadata
    contract_id UUID,
    deliverable_hash TEXT,
    assessment_data JSONB,
    
    -- Timestamps
    work_completed_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for performance
CREATE TABLE quality_work_history_y2024m01 PARTITION OF quality_work_history
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Indexes for performance
CREATE INDEX idx_quality_scores_decay 
    ON quality_scores (last_decay_applied) 
    WHERE current_score > 0.5;

CREATE INDEX idx_quality_work_wallet_time 
    ON quality_work_history (wallet_id, work_completed_at DESC);
```

## Core Implementation

### 1. Quality Score Management (Go)

```go
package quality

import (
    "context"
    "database/sql"
    "time"
    "github.com/shopspring/decimal"
    "github.com/google/uuid"
)

type QualityService struct {
    db     *sql.DB
    events EventPublisher
    config QualityConfig
}

type QualityConfig struct {
    DecayRate        decimal.Decimal // Daily decay rate
    DecayThreshold   decimal.Decimal // Minimum score before decay stops
    MaxScore         decimal.Decimal // Maximum achievable score (2.0)
    DefaultScore     decimal.Decimal // Starting score (1.0)
    MaintenanceBonus decimal.Decimal // Bonus for consistent quality work
}

type QualityScore struct {
    WalletID         uuid.UUID
    CurrentScore     decimal.Decimal
    PeakScore        decimal.Decimal
    LastUpdated      time.Time
    LastDecayApplied time.Time
    MaintenanceStreak int
}

// Get current quality score with automatic decay application
func (s *QualityService) GetQualityScore(
    ctx context.Context,
    walletID uuid.UUID,
) (*QualityScore, error) {
    // Get current score from database
    score, err := s.getStoredQualityScore(ctx, walletID)
    if err != nil {
        return nil, err
    }
    
    // Apply decay if needed
    if s.shouldApplyDecay(score) {
        score, err = s.applyDecay(ctx, score)
        if err != nil {
            return nil, err
        }
    }
    
    return score, nil
}

// Update quality score based on work assessment
func (s *QualityService) UpdateQualityScore(
    ctx context.Context,
    req UpdateQualityRequest,
) (*QualityUpdateResult, error) {
    // Validate assessment
    if err := s.validateQualityAssessment(req); err != nil {
        return nil, err
    }
    
    // Begin transaction
    tx, err := s.db.BeginTx(ctx, &sql.TxOptions{
        Isolation: sql.LevelSerializable,
    })
    if err != nil {
        return nil, err
    }
    defer tx.Rollback()
    
    // Get current score with lock
    currentScore, err := s.getQualityScoreForUpdate(ctx, tx, req.WalletID)
    if err != nil {
        return nil, err
    }
    
    // Calculate score change
    scoreChange := s.calculateScoreChange(req, currentScore)
    
    // Apply score change
    newScore := currentScore.CurrentScore.Add(scoreChange)
    newScore = s.clampScore(newScore)
    
    // Update peak score if necessary
    peakScore := decimal.Max(currentScore.PeakScore, newScore)
    
    // Update database
    _, err = tx.ExecContext(ctx, `
        UPDATE quality_scores 
        SET current_score = $1,
            peak_score = $2,
            last_updated = NOW(),
            maintenance_streak = CASE 
                WHEN $3 > 0 THEN maintenance_streak + 1 
                ELSE 0 
            END,
            total_quality_work = total_quality_work + 1,
            version = version + 1
        WHERE wallet_id = $4`,
        newScore,
        peakScore,
        scoreChange,
        req.WalletID,
    )
    if err != nil {
        return nil, err
    }
    
    // Record quality work history
    _, err = tx.ExecContext(ctx, `
        INSERT INTO quality_work_history (
            wallet_id, work_type, quality_assessment, assessor_id,
            assessment_method, score_impact, contract_id, work_completed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        req.WalletID,
        req.WorkType,
        req.QualityAssessment,
        req.AssessorID,
        req.AssessmentMethod,
        scoreChange,
        req.ContractID,
        req.WorkCompletedAt,
    )
    if err != nil {
        return nil, err
    }
    
    // Commit transaction
    if err := tx.Commit(); err != nil {
        return nil, err
    }
    
    // Publish quality update event
    if err := s.publishQualityUpdateEvent(ctx, req, scoreChange, newScore); err != nil {
        // Log error but don't fail - update already committed
        s.logger.Error("Failed to publish quality update event", "error", err)
    }
    
    return &QualityUpdateResult{
        PreviousScore: currentScore.CurrentScore,
        NewScore:      newScore,
        ScoreChange:   scoreChange,
        PeakScore:     peakScore,
        UpdatedAt:     time.Now(),
    }, nil
}

// Apply daily decay to quality scores
func (s *QualityService) applyDecay(
    ctx context.Context,
    score *QualityScore,
) (*QualityScore, error) {
    // Calculate days since last decay
    daysSinceDecay := time.Since(score.LastDecayApplied).Hours() / 24
    
    if daysSinceDecay < 1.0 {
        return score, nil // No decay needed yet
    }
    
    // Calculate decay amount
    decayAmount := s.config.DecayRate.Mul(decimal.NewFromFloat(daysSinceDecay))
    newScore := score.CurrentScore.Sub(decayAmount)
    
    // Don't decay below threshold
    if newScore.LessThan(s.config.DecayThreshold) {
        newScore = s.config.DecayThreshold
    }
    
    // Update database
    _, err := s.db.ExecContext(ctx, `
        UPDATE quality_scores 
        SET current_score = $1,
            last_decay_applied = NOW(),
            maintenance_streak = 0,
            version = version + 1
        WHERE wallet_id = $2`,
        newScore,
        score.WalletID,
    )
    if err != nil {
        return nil, err
    }
    
    // Update score object
    score.CurrentScore = newScore
    score.LastDecayApplied = time.Now()
    score.MaintenanceStreak = 0
    
    return score, nil
}

func (s *QualityService) calculateScoreChange(
    req UpdateQualityRequest,
    currentScore *QualityScore,
) decimal.Decimal {
    // Base score change from assessment
    baseChange := req.QualityAssessment.Sub(decimal.NewFromFloat(1.0))
    
    // Scale based on work type importance
    workTypeMultiplier := s.getWorkTypeMultiplier(req.WorkType)
    scaledChange := baseChange.Mul(workTypeMultiplier)
    
    // Apply maintenance streak bonus
    streakBonus := decimal.NewFromInt(int64(currentScore.MaintenanceStreak)).
        Mul(s.config.MaintenanceBonus)
    
    // Diminishing returns for high scores
    diminishingFactor := s.calculateDiminishingFactor(currentScore.CurrentScore)
    
    finalChange := scaledChange.Add(streakBonus).Mul(diminishingFactor)
    
    return finalChange
}

func (s *QualityService) calculateDiminishingFactor(currentScore decimal.Decimal) decimal.Decimal {
    // Diminishing returns as score approaches maximum
    // Factor ranges from 1.0 (at score 1.0) to 0.1 (at score 2.0)
    if currentScore.LessThanOrEqual(decimal.NewFromFloat(1.0)) {
        return decimal.NewFromFloat(1.0)
    }
    
    excessScore := currentScore.Sub(decimal.NewFromFloat(1.0))
    maxExcess := decimal.NewFromFloat(1.0) // Max score 2.0 - default 1.0
    
    // Linear diminishing: 1.0 - 0.9 * (excess / max_excess)
    diminishing := decimal.NewFromFloat(0.9).Mul(excessScore.Div(maxExcess))
    return decimal.NewFromFloat(1.0).Sub(diminishing)
}
```

### 2. Quality Multiplication Engine

```typescript
// TypeScript implementation for quality multiplication
export class QualityMultiplicationEngine {
    constructor(
        private qualityService: QualityService,
        private currencyService: CurrencyService
    ) {}
    
    async applyQualityMultiplier(
        walletId: string,
        baseAmounts: MultiCurrencyAmount
    ): Promise<QualityMultipliedAmount> {
        // Get current quality score
        const qualityScore = await this.qualityService.getQualityScore(walletId);
        
        // Apply quality multiplier to applicable currencies
        const multipliedAmounts = {
            // Economic currency affected by quality
            economic: baseAmounts.economic?.mul(qualityScore.currentScore) || new Decimal(0),
            
            // Quality score itself (not multiplied)
            quality: qualityScore.currentScore,
            
            // Temporal currency affected by quality (better quality = more efficient time use)
            temporal: baseAmounts.temporal?.mul(qualityScore.currentScore) || new Decimal(0),
            
            // Reliability not directly affected by quality
            reliability: baseAmounts.reliability || new Decimal(0),
            
            // Innovation enhanced by quality
            innovation: baseAmounts.innovation?.mul(qualityScore.currentScore.pow(1.5)) || new Decimal(0)
        };
        
        return {
            baseAmounts,
            qualityScore: qualityScore.currentScore,
            multipliedAmounts,
            totalMultiplier: this.calculateTotalMultiplier(qualityScore.currentScore),
            effectiveValue: this.calculateEffectiveValue(multipliedAmounts)
        };
    }
    
    async calculateQualityBonus(
        contractValue: MultiCurrencyAmount,
        achievedQuality: Decimal,
        targetQuality: Decimal
    ): Promise<QualityBonusResult> {
        // Calculate quality bonus for exceeding target
        const qualityExcess = achievedQuality.minus(targetQuality);
        
        if (qualityExcess.lte(0)) {
            return {
                bonusEarned: false,
                bonusAmount: { economic: new Decimal(0) },
                qualityExcess: new Decimal(0)
            };
        }
        
        // Bonus scales with quality excess (up to 50% bonus)
        const maxBonus = new Decimal(0.5);
        const maxQualityExcess = new Decimal(0.5); // Quality 1.5 vs target 1.0
        
        const bonusPercentage = Decimal.min(
            qualityExcess.div(maxQualityExcess).mul(maxBonus),
            maxBonus
        );
        
        const bonusAmount = {
            economic: contractValue.economic?.mul(bonusPercentage) || new Decimal(0),
            innovation: contractValue.innovation?.mul(bonusPercentage.mul(2)) || new Decimal(0)
        };
        
        return {
            bonusEarned: true,
            bonusAmount,
            qualityExcess,
            bonusPercentage
        };
    }
}
```

### 3. Quality Assessment System

```python
# Python implementation for automated quality assessment
import asyncio
import numpy as np
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class QualityAssessment:
    score: float  # 0.0 to 2.0
    confidence: float  # 0.0 to 1.0
    criteria_scores: Dict[str, float]
    assessment_method: str
    assessor_id: Optional[str]
    evidence: List[str]

class AutomatedQualityAssessor:
    def __init__(self, ml_models, quality_criteria):
        self.ml_models = ml_models
        self.quality_criteria = quality_criteria
        
    async def assess_deliverable(
        self, 
        deliverable: Dict,
        contract_requirements: Dict
    ) -> QualityAssessment:
        """Assess quality of work deliverable"""
        
        # Multi-dimensional quality assessment
        criteria_scores = {}
        
        # Technical quality assessment
        if 'code' in deliverable:
            criteria_scores['technical'] = await self.assess_technical_quality(
                deliverable['code']
            )
            
        # Content quality assessment
        if 'content' in deliverable:
            criteria_scores['content'] = await self.assess_content_quality(
                deliverable['content'],
                contract_requirements.get('content_requirements', {})
            )
            
        # Design quality assessment
        if 'design' in deliverable:
            criteria_scores['design'] = await self.assess_design_quality(
                deliverable['design']
            )
            
        # Completeness assessment
        criteria_scores['completeness'] = await self.assess_completeness(
            deliverable,
            contract_requirements
        )
        
        # Innovation assessment
        criteria_scores['innovation'] = await self.assess_innovation(
            deliverable,
            contract_requirements.get('innovation_requirements', {})
        )
        
        # Calculate overall score (weighted average)
        weights = {
            'technical': 0.25,
            'content': 0.25,
            'design': 0.20,
            'completeness': 0.20,
            'innovation': 0.10
        }
        
        overall_score = sum(
            criteria_scores.get(criterion, 1.0) * weight
            for criterion, weight in weights.items()
        )
        
        # Calculate confidence based on assessment coverage
        confidence = len(criteria_scores) / len(weights)
        
        return QualityAssessment(
            score=min(overall_score, 2.0),  # Cap at maximum
            confidence=confidence,
            criteria_scores=criteria_scores,
            assessment_method='automated_analysis',
            assessor_id=None,
            evidence=self.generate_evidence(criteria_scores, deliverable)
        )
        
    async def assess_technical_quality(self, code: Dict) -> float:
        """Assess technical quality of code deliverables"""
        scores = []
        
        # Code complexity analysis
        complexity_score = await self.ml_models['complexity'].predict(code)
        scores.append(complexity_score)
        
        # Security analysis
        security_score = await self.ml_models['security'].predict(code)
        scores.append(security_score)
        
        # Performance analysis
        performance_score = await self.ml_models['performance'].predict(code)
        scores.append(performance_score)
        
        # Maintainability analysis
        maintainability_score = await self.ml_models['maintainability'].predict(code)
        scores.append(maintainability_score)
        
        return np.mean(scores)
        
    async def assess_content_quality(self, content: Dict, requirements: Dict) -> float:
        """Assess quality of content deliverables"""
        scores = []
        
        # Readability analysis
        readability_score = await self.ml_models['readability'].predict(content['text'])
        scores.append(readability_score)
        
        # Accuracy analysis (fact-checking)
        accuracy_score = await self.ml_models['accuracy'].predict(content['text'])
        scores.append(accuracy_score)
        
        # Relevance to requirements
        relevance_score = await self.ml_models['relevance'].predict(
            content['text'], 
            requirements.get('topics', [])
        )
        scores.append(relevance_score)
        
        # Originality analysis
        originality_score = await self.ml_models['originality'].predict(content['text'])
        scores.append(originality_score)
        
        return np.mean(scores)
```

### 4. Integration with Contract System

```typescript
// Integration with contract execution for quality tracking
export class QualityContractIntegration {
    constructor(
        private qualityService: QualityService,
        private contractService: ContractService,
        private eventBus: EventBus
    ) {
        this.setupEventHandlers();
    }
    
    private setupEventHandlers(): void {
        // Track quality throughout contract lifecycle
        this.eventBus.on('contract.work_submitted', async (event) => {
            await this.assessSubmittedWork(event);
        });
        
        this.eventBus.on('contract.completed', async (event) => {
            await this.updateFinalQualityScores(event);
        });
        
        this.eventBus.on('quality.score_updated', async (event) => {
            await this.recalculateContractValues(event);
        });
    }
    
    private async assessSubmittedWork(event: ContractWorkSubmittedEvent): Promise<void> {
        const assessment = await this.qualityService.assessWork({
            workId: event.workId,
            contractId: event.contractId,
            submitterId: event.submitterId,
            deliverables: event.deliverables,
            requirements: event.requirements
        });
        
        // Update quality score based on assessment
        await this.qualityService.updateQualityScore({
            walletId: event.submitterId,
            workType: 'contract_delivery',
            qualityAssessment: assessment.score,
            assessmentMethod: assessment.method,
            contractId: event.contractId,
            workCompletedAt: event.submittedAt
        });
        
        // Publish quality assessment event
        await this.eventBus.publish('quality.work_assessed', {
            workId: event.workId,
            contractId: event.contractId,
            submitterId: event.submitterId,
            qualityScore: assessment.score,
            qualityChange: assessment.scoreChange,
            timestamp: Date.now()
        });
    }
}
```

## Performance Optimizations

### 1. Quality Score Caching

```go
// Redis caching for frequently accessed quality scores
type CachedQualityService struct {
    service *QualityService
    cache   *redis.Client
    ttl     time.Duration
}

func (c *CachedQualityService) GetQualityScore(
    ctx context.Context,
    walletID uuid.UUID,
) (*QualityScore, error) {
    cacheKey := fmt.Sprintf("quality:score:%s", walletID)
    
    // Try cache first
    cached, err := c.cache.Get(ctx, cacheKey).Result()
    if err == nil {
        var score QualityScore
        if err := json.Unmarshal([]byte(cached), &score); err == nil {
            // Check if decay needs to be applied
            if !c.shouldApplyDecay(&score) {
                return &score, nil
            }
        }
    }
    
    // Cache miss or decay needed - get from service
    score, err := c.service.GetQualityScore(ctx, walletID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    scoreJSON, _ := json.Marshal(score)
    c.cache.Set(ctx, cacheKey, scoreJSON, c.ttl)
    
    return score, nil
}
```

## Success Criteria

Quality Currency implementation is successful when:

1. **Accurate Multiplication**: Quality scores correctly multiply other currency values
2. **Fair Assessment**: Quality assessments are accurate and unbiased
3. **Proper Decay**: Quality scores decay appropriately without maintenance
4. **Performance**: Quality calculations add <1ms to transaction processing
5. **Integration**: Seamless integration with all other currency types

The Quality Currency serves as the critical multiplier in VibeLaunch Genesis, ensuring that high-quality work is exponentially rewarded while maintaining fairness and preventing gaming of the system.
