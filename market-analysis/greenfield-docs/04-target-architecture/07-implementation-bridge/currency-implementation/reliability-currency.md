# Reliability Currency (☆) Implementation Guide

## Overview

The Reliability Currency (☆) represents trustworthiness and consistency in VibeLaunch Genesis. Unlike other currencies, Reliability is non-transferable, yield-generating, and serves as a reputation score that affects an agent's ability to participate in high-value contracts and team formations.

## Currency Properties

```typescript
interface ReliabilityCurrencyProperties {
    symbol: '☆';
    name: 'Reliability Currency';
    type: 'reputation-based';
    
    // Core characteristics
    transferable: false;        // Cannot be transferred between agents
    divisible: true;           // Supports decimal precision
    yields: true;              // Generates passive yield
    decays: true;              // Decays without maintenance
    
    // Value range and precision
    minValue: 0.0;             // Minimum reliability score
    maxValue: 1.0;             // Maximum reliability score (100%)
    defaultValue: 0.5;         // Starting reliability (50%)
    precision: 5;              // 5 decimal places (0.00001)
    
    // Yield properties
    yieldRate: 0.0001;         // 0.01% daily yield for high performers
    yieldThreshold: 0.8;       // Minimum score for yield generation
    compoundingPeriod: 'daily'; // Daily compounding
    
    // Decay properties
    decayRate: 0.0005;         // 0.05% daily decay without activity
    decayThreshold: 0.1;       // Minimum decay level
    maintenanceRequired: true; // Requires ongoing reliable performance
    
    // Reputation mechanics
    contractEligibility: true; // Affects contract access
    teamFormationWeight: 0.3;  // 30% weight in team selection
    governanceVoting: true;    // Affects governance participation
}
```

## Database Schema

```sql
-- Reliability scores table
CREATE TABLE reliability_scores (
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    current_score DECIMAL(7,5) NOT NULL DEFAULT 0.50000,
    peak_score DECIMAL(7,5) NOT NULL DEFAULT 0.50000,
    
    -- Performance tracking
    total_contracts INTEGER DEFAULT 0,
    successful_contracts INTEGER DEFAULT 0,
    failed_contracts INTEGER DEFAULT 0,
    success_rate DECIMAL(5,3) GENERATED ALWAYS AS (
        CASE 
            WHEN total_contracts = 0 THEN 0.000
            ELSE ROUND(successful_contracts::DECIMAL / total_contracts, 3)
        END
    ) STORED,
    
    -- Yield tracking
    last_yield_at TIMESTAMPTZ DEFAULT NOW(),
    total_yield_earned DECIMAL(10,5) DEFAULT 0,
    yield_eligible BOOLEAN GENERATED ALWAYS AS (
        current_score >= 0.80000
    ) STORED,
    
    -- Decay tracking
    last_decay_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    maintenance_streak INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT reliability_range CHECK (current_score BETWEEN 0.00000 AND 1.00000),
    CONSTRAINT peak_range CHECK (peak_score BETWEEN 0.00000 AND 1.00000),
    CONSTRAINT peak_above_current CHECK (peak_score >= current_score),
    CONSTRAINT contracts_consistency CHECK (
        total_contracts = successful_contracts + failed_contracts
    ),
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    version INTEGER DEFAULT 0,
    
    PRIMARY KEY (wallet_id)
);

-- Reliability events table (for score calculation)
CREATE TABLE reliability_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES wallets(id),
    
    event_type TEXT NOT NULL CHECK (
        event_type IN ('contract_success', 'contract_failure', 'deadline_met', 
                      'deadline_missed', 'quality_delivered', 'quality_failed',
                      'team_collaboration', 'governance_participation', 'yield_earned')
    ),
    
    -- Event impact
    score_impact DECIMAL(6,5) NOT NULL,
    previous_score DECIMAL(7,5) NOT NULL,
    new_score DECIMAL(7,5) NOT NULL,
    
    -- Event context
    contract_id UUID,
    team_id UUID,
    governance_proposal_id UUID,
    
    -- Event metadata
    description TEXT,
    evidence JSONB,
    assessor_id UUID REFERENCES wallets(id),
    
    -- Timestamps
    event_occurred_at TIMESTAMPTZ NOT NULL,
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_reliability_events_wallet_time (wallet_id, event_occurred_at DESC),
    INDEX idx_reliability_events_type (event_type, event_occurred_at)
) PARTITION BY RANGE (event_occurred_at);

-- Create monthly partitions
CREATE TABLE reliability_events_y2024m01 PARTITION OF reliability_events
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Contract eligibility view
CREATE VIEW contract_eligibility AS
SELECT 
    r.wallet_id,
    r.current_score,
    CASE 
        WHEN r.current_score >= 0.9 THEN 'premium'
        WHEN r.current_score >= 0.8 THEN 'high'
        WHEN r.current_score >= 0.6 THEN 'standard'
        WHEN r.current_score >= 0.4 THEN 'limited'
        ELSE 'restricted'
    END as eligibility_tier,
    CASE 
        WHEN r.current_score >= 0.9 THEN 1000000  -- No limit
        WHEN r.current_score >= 0.8 THEN 100000   -- 100k ₥ max
        WHEN r.current_score >= 0.6 THEN 50000    -- 50k ₥ max
        WHEN r.current_score >= 0.4 THEN 10000    -- 10k ₥ max
        ELSE 1000                                  -- 1k ₥ max
    END as max_contract_value
FROM reliability_scores r;
```

## Core Implementation

### 1. Reliability Service (Rust)

```rust
use rust_decimal::Decimal;
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;

pub struct ReliabilityService {
    db: DatabasePool,
    config: ReliabilityConfig,
    event_publisher: EventPublisher,
}

#[derive(Clone)]
pub struct ReliabilityConfig {
    pub yield_rate: Decimal,           // Daily yield rate for high performers
    pub yield_threshold: Decimal,      // Minimum score for yield eligibility
    pub decay_rate: Decimal,           // Daily decay rate without activity
    pub decay_threshold: Decimal,      // Minimum score before decay stops
    pub max_score: Decimal,            // Maximum achievable score (1.0)
    pub default_score: Decimal,        // Starting score (0.5)
}

#[derive(Debug, Clone)]
pub struct ReliabilityScore {
    pub wallet_id: Uuid,
    pub current_score: Decimal,
    pub peak_score: Decimal,
    pub total_contracts: i32,
    pub successful_contracts: i32,
    pub success_rate: Decimal,
    pub yield_eligible: bool,
    pub last_activity_at: DateTime<Utc>,
}

impl ReliabilityService {
    pub fn new(db: DatabasePool, config: ReliabilityConfig) -> Self {
        Self {
            db,
            config,
            event_publisher: EventPublisher::new(),
        }
    }
    
    /// Update reliability score based on contract performance
    pub async fn update_reliability_score(
        &self,
        request: UpdateReliabilityRequest,
    ) -> Result<ReliabilityUpdateResult, ReliabilityError> {
        let mut tx = self.db.begin().await?;
        
        // Get current reliability score
        let current_score = self.get_reliability_score_for_update(&mut tx, request.wallet_id).await?;
        
        // Calculate score change based on event
        let score_change = self.calculate_score_change(&request, &current_score)?;
        
        // Apply score change with bounds checking
        let new_score = self.apply_score_change(current_score.current_score, score_change)?;
        
        // Update peak score if necessary
        let new_peak = if new_score > current_score.peak_score {
            new_score
        } else {
            current_score.peak_score
        };
        
        // Update contract counters
        let (total_contracts, successful_contracts, failed_contracts) = 
            self.update_contract_counters(&request, &current_score);
        
        // Update reliability score in database
        sqlx::query!(
            r#"
            UPDATE reliability_scores 
            SET current_score = $1,
                peak_score = $2,
                total_contracts = $3,
                successful_contracts = $4,
                failed_contracts = $5,
                last_activity_at = NOW(),
                maintenance_streak = CASE 
                    WHEN $6 > 0 THEN maintenance_streak + 1 
                    ELSE 0 
                END,
                updated_at = NOW(),
                version = version + 1
            WHERE wallet_id = $7
            "#,
            new_score,
            new_peak,
            total_contracts,
            successful_contracts,
            failed_contracts,
            score_change,
            request.wallet_id
        )
        .execute(&mut *tx)
        .await?;
        
        // Record reliability event
        sqlx::query!(
            r#"
            INSERT INTO reliability_events (
                wallet_id, event_type, score_impact, previous_score, new_score,
                contract_id, description, evidence, event_occurred_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            "#,
            request.wallet_id,
            request.event_type.to_string(),
            score_change,
            current_score.current_score,
            new_score,
            request.contract_id,
            request.description,
            request.evidence,
            request.event_occurred_at
        )
        .execute(&mut *tx)
        .await?;
        
        tx.commit().await?;
        
        // Publish reliability update event
        self.publish_reliability_update_event(&request, score_change, new_score).await?;
        
        Ok(ReliabilityUpdateResult {
            previous_score: current_score.current_score,
            new_score,
            score_change,
            peak_score: new_peak,
            success_rate: Decimal::from(successful_contracts) / Decimal::from(total_contracts.max(1)),
        })
    }
    
    /// Calculate score change based on reliability event
    fn calculate_score_change(
        &self,
        request: &UpdateReliabilityRequest,
        current_score: &ReliabilityScore,
    ) -> Result<Decimal, ReliabilityError> {
        let base_change = match request.event_type {
            ReliabilityEventType::ContractSuccess => {
                // Positive impact, diminishing returns for high scores
                let base_impact = Decimal::from_str("0.02").unwrap(); // 2% base increase
                self.apply_diminishing_returns(base_impact, current_score.current_score)
            },
            
            ReliabilityEventType::ContractFailure => {
                // Negative impact, more severe for high-reliability agents
                let base_impact = Decimal::from_str("-0.05").unwrap(); // 5% base decrease
                if current_score.current_score > Decimal::from_str("0.8").unwrap() {
                    base_impact * Decimal::from_str("1.5").unwrap() // 50% more severe
                } else {
                    base_impact
                }
            },
            
            ReliabilityEventType::DeadlineMet => {
                let base_impact = Decimal::from_str("0.01").unwrap(); // 1% increase
                self.apply_diminishing_returns(base_impact, current_score.current_score)
            },
            
            ReliabilityEventType::DeadlineMissed => {
                Decimal::from_str("-0.03").unwrap() // 3% decrease
            },
            
            ReliabilityEventType::QualityDelivered => {
                let quality_score = request.quality_score.unwrap_or(Decimal::from_str("0.8").unwrap());
                if quality_score > Decimal::from_str("0.9").unwrap() {
                    Decimal::from_str("0.015").unwrap() // 1.5% for excellent quality
                } else {
                    Decimal::from_str("0.005").unwrap() // 0.5% for good quality
                }
            },
            
            ReliabilityEventType::QualityFailed => {
                Decimal::from_str("-0.04").unwrap() // 4% decrease for poor quality
            },
            
            ReliabilityEventType::TeamCollaboration => {
                let collaboration_score = request.collaboration_score.unwrap_or(Decimal::from_str("0.8").unwrap());
                collaboration_score * Decimal::from_str("0.01").unwrap() // Up to 1% for excellent collaboration
            },
            
            ReliabilityEventType::GovernanceParticipation => {
                Decimal::from_str("0.002").unwrap() // 0.2% for governance participation
            },
        };
        
        // Apply consistency bonus for agents with high maintenance streaks
        let consistency_bonus = if current_score.maintenance_streak > 10 {
            base_change * Decimal::from_str("0.1").unwrap() // 10% bonus
        } else {
            Decimal::ZERO
        };
        
        Ok(base_change + consistency_bonus)
    }
    
    /// Apply diminishing returns for score increases
    fn apply_diminishing_returns(&self, base_change: Decimal, current_score: Decimal) -> Decimal {
        if current_score <= Decimal::from_str("0.5").unwrap() {
            base_change // Full impact for low scores
        } else if current_score <= Decimal::from_str("0.8").unwrap() {
            base_change * Decimal::from_str("0.8").unwrap() // 80% impact for medium scores
        } else {
            base_change * Decimal::from_str("0.5").unwrap() // 50% impact for high scores
        }
    }
    
    /// Apply score change with bounds checking
    fn apply_score_change(&self, current_score: Decimal, change: Decimal) -> Result<Decimal, ReliabilityError> {
        let new_score = current_score + change;
        
        // Clamp to valid range
        if new_score < Decimal::ZERO {
            Ok(Decimal::ZERO)
        } else if new_score > self.config.max_score {
            Ok(self.config.max_score)
        } else {
            Ok(new_score)
        }
    }
    
    /// Generate daily yield for high-reliability agents
    pub async fn generate_daily_yield(&self) -> Result<YieldGenerationResult, ReliabilityError> {
        // Get all agents eligible for yield
        let eligible_agents = sqlx::query_as!(
            ReliabilityScore,
            r#"
            SELECT wallet_id, current_score, peak_score, total_contracts, 
                   successful_contracts, success_rate, yield_eligible, last_activity_at
            FROM reliability_scores
            WHERE yield_eligible = true
            AND last_yield_at < NOW() - INTERVAL '1 day'
            "#
        )
        .fetch_all(&self.db)
        .await?;
        
        let mut yield_results = Vec::new();
        
        for agent in eligible_agents {
            let yield_amount = self.calculate_yield_amount(&agent)?;
            
            if yield_amount > Decimal::ZERO {
                // Apply yield to reliability score
                let new_score = std::cmp::min(
                    agent.current_score + yield_amount,
                    self.config.max_score
                );
                
                // Update database
                sqlx::query!(
                    r#"
                    UPDATE reliability_scores 
                    SET current_score = $1,
                        total_yield_earned = total_yield_earned + $2,
                        last_yield_at = NOW(),
                        updated_at = NOW(),
                        version = version + 1
                    WHERE wallet_id = $3
                    "#,
                    new_score,
                    yield_amount,
                    agent.wallet_id
                )
                .execute(&self.db)
                .await?;
                
                // Record yield event
                sqlx::query!(
                    r#"
                    INSERT INTO reliability_events (
                        wallet_id, event_type, score_impact, previous_score, new_score,
                        description, event_occurred_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
                    "#,
                    agent.wallet_id,
                    "yield_earned",
                    yield_amount,
                    agent.current_score,
                    new_score,
                    format!("Daily yield: {:.5}", yield_amount)
                )
                .execute(&self.db)
                .await?;
                
                yield_results.push(AgentYieldResult {
                    wallet_id: agent.wallet_id,
                    previous_score: agent.current_score,
                    yield_amount,
                    new_score,
                });
            }
        }
        
        Ok(YieldGenerationResult {
            agents_processed: eligible_agents.len(),
            yield_distributed: yield_results.len(),
            total_yield: yield_results.iter().map(|r| r.yield_amount).sum(),
            results: yield_results,
        })
    }
    
    /// Calculate yield amount for an agent
    fn calculate_yield_amount(&self, agent: &ReliabilityScore) -> Result<Decimal, ReliabilityError> {
        // Base yield rate
        let base_yield = self.config.yield_rate;
        
        // Score multiplier (higher scores get more yield)
        let score_multiplier = agent.current_score;
        
        // Activity multiplier (recent activity gets bonus)
        let days_since_activity = (Utc::now() - agent.last_activity_at).num_days();
        let activity_multiplier = if days_since_activity <= 7 {
            Decimal::from_str("1.2").unwrap() // 20% bonus for recent activity
        } else if days_since_activity <= 30 {
            Decimal::ONE
        } else {
            Decimal::from_str("0.5").unwrap() // 50% penalty for inactivity
        };
        
        // Success rate multiplier
        let success_multiplier = if agent.success_rate > Decimal::from_str("0.95").unwrap() {
            Decimal::from_str("1.5").unwrap() // 50% bonus for excellent success rate
        } else if agent.success_rate > Decimal::from_str("0.9").unwrap() {
            Decimal::from_str("1.2").unwrap() // 20% bonus for good success rate
        } else {
            Decimal::ONE
        };
        
        let yield_amount = base_yield * score_multiplier * activity_multiplier * success_multiplier;
        
        Ok(yield_amount)
    }
}

#[derive(Debug)]
pub enum ReliabilityEventType {
    ContractSuccess,
    ContractFailure,
    DeadlineMet,
    DeadlineMissed,
    QualityDelivered,
    QualityFailed,
    TeamCollaboration,
    GovernanceParticipation,
}
```

### 2. Contract Eligibility Engine

```typescript
// TypeScript implementation for contract eligibility based on reliability
export class ContractEligibilityEngine {
    constructor(
        private reliabilityService: ReliabilityService,
        private contractService: ContractService
    ) {}
    
    async checkContractEligibility(
        agentId: string,
        contractValue: Decimal,
        contractRisk: 'low' | 'medium' | 'high' | 'critical'
    ): Promise<EligibilityResult> {
        // Get agent's reliability score
        const reliabilityScore = await this.reliabilityService.getReliabilityScore(agentId);
        
        // Determine eligibility tier
        const eligibilityTier = this.determineEligibilityTier(reliabilityScore.currentScore);
        
        // Check value limits
        const maxContractValue = this.getMaxContractValue(eligibilityTier);
        const valueEligible = contractValue.lte(maxContractValue);
        
        // Check risk eligibility
        const riskEligible = this.checkRiskEligibility(eligibilityTier, contractRisk);
        
        // Calculate eligibility score
        const eligibilityScore = this.calculateEligibilityScore(
            reliabilityScore,
            contractValue,
            contractRisk
        );
        
        return {
            eligible: valueEligible && riskEligible,
            eligibilityTier,
            eligibilityScore,
            maxContractValue,
            restrictions: this.getRestrictions(eligibilityTier),
            recommendations: this.getRecommendations(reliabilityScore, eligibilityTier)
        };
    }
    
    private determineEligibilityTier(reliabilityScore: Decimal): EligibilityTier {
        if (reliabilityScore.gte(0.9)) return 'premium';
        if (reliabilityScore.gte(0.8)) return 'high';
        if (reliabilityScore.gte(0.6)) return 'standard';
        if (reliabilityScore.gte(0.4)) return 'limited';
        return 'restricted';
    }
    
    private getMaxContractValue(tier: EligibilityTier): Decimal {
        const limits = {
            premium: new Decimal(1000000),    // No practical limit
            high: new Decimal(100000),        // 100k ₥
            standard: new Decimal(50000),     // 50k ₥
            limited: new Decimal(10000),      // 10k ₥
            restricted: new Decimal(1000)     // 1k ₥
        };
        
        return limits[tier];
    }
    
    private checkRiskEligibility(tier: EligibilityTier, risk: string): boolean {
        const riskMatrix = {
            premium: ['low', 'medium', 'high', 'critical'],
            high: ['low', 'medium', 'high'],
            standard: ['low', 'medium'],
            limited: ['low'],
            restricted: []
        };
        
        return riskMatrix[tier].includes(risk);
    }
}
```

### 3. Reliability Decay Engine

```python
# Python implementation for reliability decay processing
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict

class ReliabilityDecayEngine:
    def __init__(self, reliability_service, config):
        self.reliability_service = reliability_service
        self.config = config
        self.is_running = False
        
    async def start_decay_processor(self):
        """Start the daily decay processing"""
        self.is_running = True
        
        while self.is_running:
            try:
                await self.process_daily_decay()
                await asyncio.sleep(86400)  # Process daily
            except Exception as e:
                print(f"Error in reliability decay processing: {e}")
                await asyncio.sleep(3600)  # Retry after 1 hour
                
    async def process_daily_decay(self):
        """Process daily decay for inactive agents"""
        # Get agents that need decay (inactive for > 7 days)
        inactive_agents = await self.get_inactive_agents()
        
        if not inactive_agents:
            return
            
        print(f"Processing decay for {len(inactive_agents)} inactive agents")
        
        decay_updates = []
        for agent in inactive_agents:
            decay_update = self.calculate_decay(agent)
            if decay_update:
                decay_updates.append(decay_update)
                
        if decay_updates:
            await self.apply_decay_updates(decay_updates)
            
    async def get_inactive_agents(self) -> List[Dict]:
        """Get agents that haven't been active recently"""
        query = """
        SELECT wallet_id, current_score, last_activity_at, last_decay_at
        FROM reliability_scores
        WHERE current_score > %s
        AND last_activity_at < NOW() - INTERVAL '7 days'
        AND last_decay_at < NOW() - INTERVAL '1 day'
        """
        
        return await self.reliability_service.query(
            query, [self.config.decay_threshold]
        )
        
    def calculate_decay(self, agent: Dict) -> Dict:
        """Calculate decay for an inactive agent"""
        current_score = Decimal(str(agent['current_score']))
        last_activity = agent['last_activity_at']
        
        # Calculate days of inactivity
        days_inactive = (datetime.now() - last_activity).days
        
        # Progressive decay - more severe for longer inactivity
        if days_inactive <= 7:
            return None  # No decay for first week
        elif days_inactive <= 30:
            decay_rate = self.config.decay_rate  # Normal decay
        elif days_inactive <= 90:
            decay_rate = self.config.decay_rate * 2  # Double decay
        else:
            decay_rate = self.config.decay_rate * 3  # Triple decay
            
        # Calculate new score
        decay_amount = current_score * decay_rate
        new_score = current_score - decay_amount
        
        # Don't decay below threshold
        if new_score < self.config.decay_threshold:
            new_score = self.config.decay_threshold
            decay_amount = current_score - new_score
            
        if decay_amount <= 0:
            return None
            
        return {
            'wallet_id': agent['wallet_id'],
            'old_score': current_score,
            'new_score': new_score,
            'decay_amount': decay_amount,
            'days_inactive': days_inactive
        }
```

## Success Criteria

Reliability Currency implementation is successful when:

1. **Accurate Reputation Tracking**: Reliability scores accurately reflect agent performance
2. **Fair Yield Distribution**: High-performing agents receive appropriate yield rewards
3. **Proper Decay**: Inactive agents experience appropriate score decay
4. **Contract Eligibility**: Reliable agents gain access to higher-value contracts
5. **Team Formation Impact**: Reliability scores properly influence team selection

The Reliability Currency creates a robust reputation system that rewards consistent performance and reliability while protecting the ecosystem from unreliable participants.
