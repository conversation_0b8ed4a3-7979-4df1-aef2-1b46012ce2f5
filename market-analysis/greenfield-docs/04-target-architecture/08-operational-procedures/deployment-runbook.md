# VibeLaunch Genesis Deployment Runbook

## Overview

This runbook provides step-by-step procedures for deploying VibeLaunch Genesis to production environments. It covers initial deployment, updates, rollbacks, and emergency procedures while maintaining 99.99% availability and 95%+ economic efficiency.

## Prerequisites

### Infrastructure Requirements
- Kubernetes cluster (v1.28+) with 3+ regions
- PostgreSQL cluster with TimescaleDB extension
- Redis cluster with Streams support
- Istio service mesh configured
- Prometheus/Grafana monitoring stack
- HashiCorp Vault for secrets management

### Access Requirements
- kubectl access to production clusters
- Terraform state access (S3 + DynamoDB)
- Container registry push/pull permissions
- Vault admin access for secrets
- Monitoring dashboard access

### Pre-Deployment Checklist
```yaml
infrastructure_ready:
  - [ ] Kubernetes clusters healthy in all regions
  - [ ] Database clusters operational with replication
  - [ ] Redis clusters operational with persistence
  - [ ] Service mesh control plane healthy
  - [ ] Monitoring stack operational
  - [ ] Secrets management configured

application_ready:
  - [ ] All container images built and pushed
  - [ ] Database migrations tested
  - [ ] Configuration validated
  - [ ] Integration tests passing
  - [ ] Performance benchmarks met
  - [ ] Security scans passed

team_ready:
  - [ ] Deployment team assembled
  - [ ] Rollback procedures reviewed
  - [ ] Incident response team on standby
  - [ ] Communication channels active
  - [ ] Stakeholders notified
```

## Deployment Procedures

### 1. Initial Production Deployment

#### 1.1 Infrastructure Deployment

```bash
#!/bin/bash
# Deploy infrastructure using Terraform

# Set environment variables
export TF_VAR_environment="production"
export TF_VAR_target_efficiency="0.95"
export AWS_REGION="us-east-1"

# Initialize Terraform
cd infrastructure/terraform
terraform init -backend-config="key=production/terraform.tfstate"

# Plan deployment
terraform plan -var-file="production.tfvars" -out=production.plan

# Apply infrastructure (requires approval)
terraform apply production.plan

# Verify infrastructure health
./scripts/verify-infrastructure.sh
```

#### 1.2 Database Setup

```bash
#!/bin/bash
# Set up production databases

# Deploy PostgreSQL cluster
kubectl apply -f k8s/databases/postgresql-cluster.yaml

# Wait for cluster to be ready
kubectl wait --for=condition=ready pod -l app=postgresql --timeout=600s

# Run database migrations
kubectl exec -it postgresql-primary-0 -- psql -U postgres -d vibelaunch << EOF
-- Create extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS economic_laws;

-- Run migrations
\i /migrations/001_initial_schema.sql
\i /migrations/002_currency_tables.sql
\i /migrations/003_economic_constraints.sql
EOF

# Verify database health
./scripts/verify-database-health.sh
```

#### 1.3 Service Deployment

```bash
#!/bin/bash
# Deploy services in dependency order

# 1. Deploy foundational services
kubectl apply -f k8s/services/currency-service/
kubectl apply -f k8s/services/market-engine/

# Wait for foundational services
kubectl wait --for=condition=available deployment/currency-service --timeout=300s
kubectl wait --for=condition=available deployment/market-engine --timeout=300s

# 2. Deploy business logic services
kubectl apply -f k8s/services/contract-manager/
kubectl apply -f k8s/services/agent-coordinator/

# 3. Deploy supporting services
kubectl apply -f k8s/services/governance-engine/
kubectl apply -f k8s/services/analytics-engine/

# 4. Deploy API gateway
kubectl apply -f k8s/services/api-gateway/

# Verify all services are healthy
./scripts/verify-service-health.sh
```

### 2. Rolling Updates

#### 2.1 Canary Deployment Process

```yaml
# Flagger canary configuration
apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: market-engine-canary
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: market-engine
  
  # Progressive traffic shifting
  analysis:
    interval: 30s
    threshold: 10
    maxWeight: 50
    stepWeight: 5
    
    # Economic health metrics
    metrics:
    - name: economic-efficiency
      thresholdRange:
        min: 0.95  # Must maintain 95%+ efficiency
    - name: value-conservation
      thresholdRange:
        min: 0.9999  # 99.99% value conservation
    - name: latency-p99
      thresholdRange:
        max: 10  # <10ms latency
        
    # Load testing during rollout
    webhooks:
    - name: load-test
      url: http://flagger-loadtester/
      metadata:
        cmd: "hey -z 5m -q 1000 -c 100 http://market-engine-canary:50051/health"
```

#### 2.2 Update Execution

```bash
#!/bin/bash
# Execute rolling update

SERVICE_NAME=$1
NEW_IMAGE_TAG=$2

# Validate inputs
if [[ -z "$SERVICE_NAME" || -z "$NEW_IMAGE_TAG" ]]; then
    echo "Usage: $0 <service-name> <image-tag>"
    exit 1
fi

# Pre-update health check
./scripts/pre-update-health-check.sh $SERVICE_NAME

# Update deployment
kubectl set image deployment/$SERVICE_NAME \
    $SERVICE_NAME=vibelaunch/$SERVICE_NAME:$NEW_IMAGE_TAG

# Monitor rollout
kubectl rollout status deployment/$SERVICE_NAME --timeout=600s

# Verify economic metrics during rollout
./scripts/monitor-economic-metrics.sh $SERVICE_NAME

# Post-update validation
./scripts/post-update-validation.sh $SERVICE_NAME
```

### 3. Emergency Procedures

#### 3.1 Immediate Rollback

```bash
#!/bin/bash
# Emergency rollback procedure

SERVICE_NAME=$1

echo "EMERGENCY ROLLBACK: $SERVICE_NAME"
echo "Timestamp: $(date)"

# Immediate rollback
kubectl rollout undo deployment/$SERVICE_NAME

# Wait for rollback completion
kubectl rollout status deployment/$SERVICE_NAME --timeout=300s

# Verify system health
./scripts/emergency-health-check.sh

# Alert stakeholders
./scripts/send-emergency-alert.sh "Emergency rollback completed for $SERVICE_NAME"
```

#### 3.2 Circuit Breaker Activation

```bash
#!/bin/bash
# Activate circuit breakers for failing services

SERVICE_NAME=$1
FAILURE_THRESHOLD=${2:-50}  # Default 50% failure rate

# Configure Istio circuit breaker
kubectl apply -f - <<EOF
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: ${SERVICE_NAME}-circuit-breaker
spec:
  host: ${SERVICE_NAME}
  trafficPolicy:
    outlierDetection:
      consecutiveErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
    circuitBreaker:
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 10
          maxRequestsPerConnection: 2
      outlierDetection:
        consecutiveErrors: 3
EOF

echo "Circuit breaker activated for $SERVICE_NAME"
```

## Monitoring and Validation

### 1. Health Check Scripts

```bash
#!/bin/bash
# verify-service-health.sh

SERVICES=(
    "currency-service"
    "market-engine" 
    "contract-manager"
    "agent-coordinator"
    "governance-engine"
    "analytics-engine"
)

echo "Verifying service health..."

for service in "${SERVICES[@]}"; do
    echo "Checking $service..."
    
    # Check deployment status
    if ! kubectl get deployment $service &>/dev/null; then
        echo "ERROR: Deployment $service not found"
        exit 1
    fi
    
    # Check pod readiness
    ready_pods=$(kubectl get deployment $service -o jsonpath='{.status.readyReplicas}')
    desired_pods=$(kubectl get deployment $service -o jsonpath='{.spec.replicas}')
    
    if [[ "$ready_pods" != "$desired_pods" ]]; then
        echo "ERROR: $service not fully ready ($ready_pods/$desired_pods)"
        exit 1
    fi
    
    # Check service endpoint
    if ! kubectl get service $service &>/dev/null; then
        echo "ERROR: Service $service not found"
        exit 1
    fi
    
    echo "✓ $service healthy"
done

echo "All services healthy"
```

### 2. Economic Metrics Validation

```bash
#!/bin/bash
# monitor-economic-metrics.sh

SERVICE_NAME=$1
DURATION=${2:-300}  # Monitor for 5 minutes

echo "Monitoring economic metrics for $SERVICE_NAME..."

# Query Prometheus for key metrics
PROMETHEUS_URL="http://prometheus.monitoring.svc.cluster.local:9090"

# Check system efficiency
efficiency=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=vibelaunch_system_efficiency" | \
    jq -r '.data.result[0].value[1]')

if (( $(echo "$efficiency < 0.95" | bc -l) )); then
    echo "ERROR: System efficiency below threshold: $efficiency"
    exit 1
fi

# Check value conservation
conservation=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=vibelaunch_value_conservation_rate" | \
    jq -r '.data.result[0].value[1]')

if (( $(echo "$conservation < 0.9999" | bc -l) )); then
    echo "ERROR: Value conservation below threshold: $conservation"
    exit 1
fi

# Check transaction throughput
throughput=$(curl -s "$PROMETHEUS_URL/api/v1/query?query=rate(vibelaunch_transactions_total[5m])" | \
    jq -r '.data.result[0].value[1]')

echo "Current metrics:"
echo "  Efficiency: $efficiency"
echo "  Conservation: $conservation" 
echo "  Throughput: $throughput TPS"
```

## Performance Tuning

### 1. Database Optimization

```sql
-- Production database tuning
-- Execute on PostgreSQL primary

-- Connection pooling
ALTER SYSTEM SET max_connections = 1000;
ALTER SYSTEM SET shared_buffers = '32GB';
ALTER SYSTEM SET effective_cache_size = '96GB';
ALTER SYSTEM SET work_mem = '256MB';

-- WAL configuration for high throughput
ALTER SYSTEM SET wal_buffers = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET max_wal_size = '16GB';

-- Query optimization
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Reload configuration
SELECT pg_reload_conf();

-- Verify settings
SELECT name, setting, unit FROM pg_settings 
WHERE name IN ('max_connections', 'shared_buffers', 'effective_cache_size');
```

### 2. Service Optimization

```yaml
# Resource optimization for high-performance services
apiVersion: v1
kind: ConfigMap
metadata:
  name: performance-tuning
data:
  market-engine-config: |
    # Rust market engine optimization
    RUST_LOG=warn
    TOKIO_WORKER_THREADS=16
    MAX_BLOCKING_THREADS=512
    STACK_SIZE=2097152
    
  currency-service-config: |
    # Go currency service optimization  
    GOMAXPROCS=32
    GOGC=100
    GOMEMLIMIT=16GiB
    
  node-services-config: |
    # Node.js services optimization
    NODE_ENV=production
    UV_THREADPOOL_SIZE=128
    NODE_OPTIONS="--max-old-space-size=8192"
```

## Disaster Recovery

### 1. Backup Procedures

```bash
#!/bin/bash
# backup-production.sh

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_BUCKET="vibelaunch-production-backups"

echo "Starting production backup: $BACKUP_DATE"

# Database backup
kubectl exec postgresql-primary-0 -- pg_dump -U postgres vibelaunch | \
    gzip > "db_backup_$BACKUP_DATE.sql.gz"

# Upload to S3
aws s3 cp "db_backup_$BACKUP_DATE.sql.gz" \
    "s3://$BACKUP_BUCKET/database/"

# Kubernetes state backup
kubectl get all --all-namespaces -o yaml > "k8s_state_$BACKUP_DATE.yaml"
aws s3 cp "k8s_state_$BACKUP_DATE.yaml" \
    "s3://$BACKUP_BUCKET/kubernetes/"

# Configuration backup
kubectl get configmaps --all-namespaces -o yaml > "configs_$BACKUP_DATE.yaml"
kubectl get secrets --all-namespaces -o yaml > "secrets_$BACKUP_DATE.yaml"

aws s3 cp "configs_$BACKUP_DATE.yaml" "s3://$BACKUP_BUCKET/configs/"

echo "Backup completed: $BACKUP_DATE"
```

### 2. Recovery Procedures

```bash
#!/bin/bash
# disaster-recovery.sh

BACKUP_DATE=$1

if [[ -z "$BACKUP_DATE" ]]; then
    echo "Usage: $0 <backup-date>"
    echo "Available backups:"
    aws s3 ls s3://vibelaunch-production-backups/database/
    exit 1
fi

echo "Starting disaster recovery from backup: $BACKUP_DATE"

# Restore database
aws s3 cp "s3://vibelaunch-production-backups/database/db_backup_$BACKUP_DATE.sql.gz" .
gunzip "db_backup_$BACKUP_DATE.sql.gz"

kubectl exec -i postgresql-primary-0 -- psql -U postgres vibelaunch < "db_backup_$BACKUP_DATE.sql"

# Restore Kubernetes state
aws s3 cp "s3://vibelaunch-production-backups/kubernetes/k8s_state_$BACKUP_DATE.yaml" .
kubectl apply -f "k8s_state_$BACKUP_DATE.yaml"

# Verify recovery
./scripts/verify-service-health.sh
./scripts/monitor-economic-metrics.sh all 600

echo "Disaster recovery completed"
```

## Success Criteria

Deployment is considered successful when:

```yaml
success_metrics:
  availability:
    target: 99.99%
    measurement: "uptime over 30 days"
    
  performance:
    latency_p99: "< 10ms"
    throughput: "> 100,000 orders/sec"
    efficiency: "> 95%"
    
  consistency:
    value_conservation: "> 99.99%"
    data_integrity: "100%"
    
  operational:
    deployment_time: "< 30 minutes"
    rollback_time: "< 5 minutes"
    mttr: "< 15 minutes"
```

This runbook ensures reliable, efficient deployment and operation of VibeLaunch Genesis while maintaining the highest standards of availability and economic performance.
