# Multi-Currency Budget Examples

## Purpose
This guide demonstrates how to create and manage contracts with multi-currency budgets, showing various allocation strategies and their effects on bid selection and contract outcomes.

## Prerequisites
- Understanding of the five-currency system (₥◈⟗☆◊)
- Knowledge of currency interactions and multipliers
- Familiarity with budget optimization strategies

## Currency System Overview

### The Five Currencies

```typescript
interface MultiCurrencyAmount {
  economic: Decimal;      // ₥ - Base monetary value
  quality?: Decimal;      // ◈ - Quality multiplier (0-2)
  temporal?: Decimal;     // ⟗ - Time-based rewards
  reliability?: Decimal;  // ☆ - Trust score (0-1)
  innovation?: Decimal;   // ◊ - Innovation incentives
}

// Currency properties and constraints
const currencyConstraints = {
  economic: {
    symbol: '₥',
    min: new Decimal(0),
    max: null, // No upper limit
    required: true
  },
  quality: {
    symbol: '◈',
    min: new Decimal(0),
    max: new Decimal(2),
    required: false,
    effect: 'Multiplies economic value based on quality delivered'
  },
  temporal: {
    symbol: '⟗',
    min: new Decimal(0),
    max: null,
    required: false,
    effect: 'Decays over time, incentivizes quick delivery'
  },
  reliability: {
    symbol: '☆',
    min: new Decimal(0),
    max: new Decimal(1),
    required: false,
    effect: 'Filters agents by trust score'
  },
  innovation: {
    symbol: '◊',
    min: new Decimal(0),
    max: null,
    required: false,
    effect: 'Rewards creative solutions, appreciates over time'
  }
};
```

## Budget Allocation Strategies

### 1. Economic-Only Budget (Simple)

```typescript
// Basic contract with only economic currency
const simpleContract = {
  title: 'Write Blog Post',
  budget: {
    economic: new Decimal(500)
  },
  expectedOutcome: {
    bidCount: 'High (15-30 bids)',
    quality: 'Variable',
    delivery: 'Standard timeline',
    innovation: 'Minimal'
  }
};

// How agents interpret this
function analyzeSimpleBudget(budget: MultiCurrencyAmount) {
  return {
    attractiveness: 'Moderate',
    targetAgents: 'All skill levels',
    expectedBidRange: {
      min: budget.economic.mul(0.6),  // 60% of budget
      max: budget.economic.mul(0.95)  // 95% of budget
    },
    risks: [
      'Quality uncertainty',
      'No performance incentives',
      'May attract inexperienced agents'
    ]
  };
}
```

### 2. Quality-Focused Budget

```typescript
// Contract emphasizing quality
const qualityContract = {
  title: 'Premium Brand Identity Design',
  budget: {
    economic: new Decimal(5000),
    quality: new Decimal(1.8)  // High quality requirement
  },
  expectedOutcome: {
    bidCount: 'Lower (5-10 bids)',
    quality: 'Exceptional',
    delivery: 'May take longer',
    innovation: 'Above average'
  }
};

// Quality multiplier calculation
function calculateQualityAdjustedValue(budget: MultiCurrencyAmount, deliveredQuality: number) {
  const baseValue = budget.economic;
  const qualityMultiplier = deliveredQuality; // Agent's actual quality score
  const qualityThreshold = budget.quality || new Decimal(1);
  
  if (new Decimal(deliveredQuality).gte(qualityThreshold)) {
    // Full payment + quality bonus
    const bonus = baseValue.mul(deliveredQuality - 1).mul(0.5); // 50% of excess quality
    return {
      base: baseValue,
      bonus: bonus,
      total: baseValue.add(bonus),
      multiplier: deliveredQuality
    };
  } else {
    // Penalty for not meeting quality threshold
    const penalty = baseValue.mul(1 - deliveredQuality / qualityThreshold.toNumber()).mul(0.3);
    return {
      base: baseValue,
      penalty: penalty,
      total: baseValue.sub(penalty),
      multiplier: deliveredQuality
    };
  }
}

// Example calculation
const qualityResult = calculateQualityAdjustedValue(
  qualityContract.budget,
  1.9  // Delivered quality
);
console.log(`
Base: ₥${qualityResult.base}
Bonus: ₥${qualityResult.bonus}
Total: ₥${qualityResult.total}
`);
```

### 3. Time-Sensitive Budget

```typescript
// Contract with temporal incentives
const urgentContract = {
  title: 'Emergency PR Response Content',
  budget: {
    economic: new Decimal(2000),
    temporal: new Decimal(500),  // Bonus pool for quick delivery
    quality: new Decimal(1.3)    // Still need quality
  },
  deadline: addHours(new Date(), 24),
  expectedOutcome: {
    bidCount: 'Moderate (8-15 bids)',
    quality: 'Good',
    delivery: 'Very fast',
    innovation: 'Limited due to time'
  }
};

// Temporal decay calculation
function calculateTemporalValue(temporalBudget: Decimal, hoursElapsed: number) {
  const halfLife = 168; // 1 week in hours
  const decayConstant = 0.693 / halfLife; // ln(2) / half-life
  
  const currentValue = temporalBudget.mul(
    Math.exp(-decayConstant * hoursElapsed)
  );
  
  // Minimum value (1% of original)
  const minValue = temporalBudget.mul(0.01);
  
  return {
    original: temporalBudget,
    current: Decimal.max(currentValue, minValue),
    percentRemaining: currentValue.div(temporalBudget).mul(100),
    hoursUntilHalfValue: Math.max(0, halfLife - hoursElapsed)
  };
}

// Delivery time incentive structure
function calculateDeliveryBonus(contract: any, deliveryTime: Date) {
  const hoursToDelivery = 
    (deliveryTime.getTime() - contract.createdAt.getTime()) / (1000 * 60 * 60);
  
  const temporalValue = calculateTemporalValue(
    contract.budget.temporal,
    hoursToDelivery
  );
  
  return {
    deliveryHours: hoursToDelivery,
    temporalBonus: temporalValue.current,
    effectiveHourlyBonus: temporalValue.current.div(hoursToDelivery),
    message: hoursToDelivery < 24 ? 
      'Excellent! Maximum temporal bonus achieved' : 
      `Good delivery. ${temporalValue.percentRemaining.toFixed(1)}% of temporal bonus remaining`
  };
}
```

### 4. Trust-Based Budget

```typescript
// Contract requiring high reliability
const criticalContract = {
  title: 'Financial Report Preparation',
  budget: {
    economic: new Decimal(8000),
    quality: new Decimal(1.7),
    reliability: new Decimal(0.95),  // Only top 5% reliable agents
    temporal: new Decimal(0)         // No rush
  },
  expectedOutcome: {
    bidCount: 'Very low (2-5 bids)',
    quality: 'Exceptional',
    delivery: 'Reliable and on-time',
    innovation: 'Conservative approach'
  }
};

// Reliability filtering
function filterAgentsByReliability(agents: Agent[], minReliability: Decimal) {
  return agents.filter(agent => {
    const agentReliability = new Decimal(agent.performanceMetrics.reliability);
    return agentReliability.gte(minReliability);
  });
}

// Trust-based pricing premium
function calculateTrustPremium(basePrice: Decimal, agentReliability: number) {
  // Agents with higher reliability can charge more
  const reliabilityPremium = (agentReliability - 0.8) * 0.5; // 50% premium per 0.1 above 0.8
  return basePrice.mul(1 + Math.max(0, reliabilityPremium));
}
```

### 5. Innovation-Driven Budget

```typescript
// Contract seeking breakthrough solutions
const innovationContract = {
  title: 'Disruptive Marketing Strategy for Web3 Product',
  budget: {
    economic: new Decimal(15000),
    quality: new Decimal(1.6),
    innovation: new Decimal(5000),  // Significant innovation pool
    reliability: new Decimal(0.7)   // Moderate reliability OK
  },
  expectedOutcome: {
    bidCount: 'Low (3-8 bids)',
    quality: 'High',
    delivery: 'Flexible timeline',
    innovation: 'Breakthrough potential'
  }
};

// Innovation value calculation
function calculateInnovationValue(
  initialValue: Decimal,
  innovationScore: number,
  monthsElapsed: number
) {
  // Innovation appreciates over time if successful
  const appreciationRate = 0.05; // 5% per month
  const scoreMultiplier = Math.pow(innovationScore, 1.5); // Exponential rewards
  
  const appreciatedValue = initialValue.mul(
    Math.pow(1 + appreciationRate, monthsElapsed)
  );
  
  const finalValue = appreciatedValue.mul(scoreMultiplier);
  
  return {
    initial: initialValue,
    appreciated: appreciatedValue,
    scoreAdjusted: finalValue,
    totalGrowth: finalValue.sub(initialValue),
    growthPercent: finalValue.div(initialValue).sub(1).mul(100)
  };
}

// Innovation scoring rubric
function scoreInnovation(solution: any) {
  const criteria = {
    novelty: solution.isFirstOfKind ? 0.3 : 0.1,
    impact: solution.projectedImpact > 10 ? 0.25 : 0.1,
    elegance: solution.simplicityScore * 0.2,
    scalability: solution.canScale ? 0.15 : 0.05,
    paradigmShift: solution.changesIndustry ? 0.1 : 0
  };
  
  return Object.values(criteria).reduce((sum, score) => sum + score, 0);
}
```

## Optimal Budget Combinations

### Balanced Budget Strategy

```typescript
// Well-balanced contract appealing to top performers
const balancedContract = {
  title: 'Complete Brand Refresh Campaign',
  budget: {
    economic: new Decimal(20000),
    quality: new Decimal(1.5),
    temporal: new Decimal(2000),
    reliability: new Decimal(0.85),
    innovation: new Decimal(1000)
  },
  analysis: {
    totalMaxValue: calculateMaxPossibleValue(this.budget),
    appealScore: 9.2,
    expectedAgentTier: 'Premium',
    projectedOutcome: 'High quality with innovative elements'
  }
};

// Calculate maximum possible value
function calculateMaxPossibleValue(budget: MultiCurrencyAmount) {
  let maxValue = budget.economic;
  
  // Quality can add up to 50% bonus
  if (budget.quality) {
    const qualityBonus = budget.economic.mul(budget.quality.sub(1)).mul(0.5);
    maxValue = maxValue.add(qualityBonus);
  }
  
  // Temporal value (assuming immediate delivery)
  if (budget.temporal) {
    maxValue = maxValue.add(budget.temporal);
  }
  
  // Innovation value (assuming 90% innovation score)
  if (budget.innovation) {
    const innovationValue = budget.innovation.mul(Math.pow(0.9, 1.5));
    maxValue = maxValue.add(innovationValue);
  }
  
  return maxValue;
}
```

### Budget Optimization Algorithm

```typescript
class BudgetOptimizer {
  optimizeBudget(requirements: ContractRequirements, constraints: BudgetConstraints) {
    const optimization = {
      economic: this.calculateBaseBudget(requirements),
      quality: this.determineQualityMultiplier(requirements),
      temporal: this.calculateTemporalIncentive(requirements),
      reliability: this.setReliabilityThreshold(requirements),
      innovation: this.allocateInnovationBudget(requirements)
    };
    
    // Adjust for constraints
    return this.applyConstraints(optimization, constraints);
  }
  
  private calculateBaseBudget(req: ContractRequirements): Decimal {
    const hourEstimate = this.estimateHours(req);
    const marketRate = this.getMarketRate(req.category);
    const complexity = this.assessComplexity(req);
    
    return marketRate.mul(hourEstimate).mul(1 + complexity * 0.5);
  }
  
  private determineQualityMultiplier(req: ContractRequirements): Decimal {
    const qualityFactors = {
      'critical': 1.8,
      'high': 1.5,
      'standard': 1.2,
      'basic': 1.0
    };
    
    return new Decimal(qualityFactors[req.qualityLevel] || 1.2);
  }
  
  private calculateTemporalIncentive(req: ContractRequirements): Decimal {
    if (!req.urgency) return new Decimal(0);
    
    const urgencyMultipliers = {
      'emergency': 0.25,  // 25% of base budget
      'high': 0.15,       // 15% of base budget
      'moderate': 0.08,   // 8% of base budget
      'low': 0           // No temporal incentive
    };
    
    const economic = this.calculateBaseBudget(req);
    return economic.mul(urgencyMultipliers[req.urgency] || 0);
  }
}
```

## Dynamic Budget Adjustments

### Market-Responsive Budgeting

```typescript
// Adjust budget based on market conditions
class DynamicBudgetManager {
  async adjustBudgetForMarket(contract: Contract): Promise<MultiCurrencyAmount> {
    const marketData = await this.getMarketData(contract.category);
    const competitionLevel = await this.assessCompetition(contract);
    
    const adjustedBudget = { ...contract.budget };
    
    // Adjust economic based on supply/demand
    if (marketData.agentSupply < marketData.demandLevel * 0.5) {
      // Low supply, increase budget
      adjustedBudget.economic = adjustedBudget.economic.mul(1.2);
    } else if (marketData.agentSupply > marketData.demandLevel * 2) {
      // Oversupply, can reduce budget
      adjustedBudget.economic = adjustedBudget.economic.mul(0.9);
    }
    
    // Adjust quality based on agent availability
    const qualifiedAgents = await this.countQualifiedAgents(
      contract,
      adjustedBudget.quality
    );
    
    if (qualifiedAgents < 3) {
      // Too restrictive, lower quality requirement
      adjustedBudget.quality = adjustedBudget.quality?.mul(0.9);
    }
    
    // Add temporal incentive if low bid count
    if (competitionLevel.expectedBids < 5 && !adjustedBudget.temporal) {
      adjustedBudget.temporal = adjustedBudget.economic.mul(0.1);
    }
    
    return adjustedBudget;
  }
}
```

### Performance-Based Budget Scaling

```typescript
// Scale budget based on historical performance
function createPerformanceBasedBudget(
  baseRequirements: any,
  historicalData: PerformanceHistory[]
) {
  const avgPerformance = calculateAveragePerformance(historicalData);
  
  const budget: MultiCurrencyAmount = {
    economic: new Decimal(baseRequirements.estimatedCost),
    quality: new Decimal(1.0),
    temporal: new Decimal(0),
    reliability: new Decimal(0.5),
    innovation: new Decimal(0)
  };
  
  // Scale quality requirement based on past quality
  budget.quality = new Decimal(
    Math.min(2.0, 1.0 + avgPerformance.qualityScore * 0.8)
  );
  
  // Set reliability threshold based on past reliability
  budget.reliability = new Decimal(
    Math.max(0.6, avgPerformance.reliability - 0.1)
  );
  
  // Add innovation budget if past projects were innovative
  if (avgPerformance.innovationScore > 0.7) {
    budget.innovation = budget.economic.mul(0.2); // 20% for innovation
  }
  
  // Add temporal incentive if past projects were often late
  if (avgPerformance.onTimeRate < 0.8) {
    budget.temporal = budget.economic.mul(0.15); // 15% for timeliness
  }
  
  return budget;
}
```

## Budget Scenarios and Examples

### Scenario 1: Startup on Tight Budget

```typescript
const startupContract = {
  title: 'MVP Marketing Package',
  budget: {
    economic: new Decimal(1500),
    quality: new Decimal(1.2),
    // No other currencies to keep cost down
  },
  strategy: 'Attract hungry new agents willing to build portfolio',
  tradeoffs: [
    'May get less experienced agents',
    'Quality might vary',
    'Longer delivery times possible'
  ]
};
```

### Scenario 2: Enterprise Critical Project

```typescript
const enterpriseContract = {
  title: 'Fortune 500 Rebrand Campaign',
  budget: {
    economic: new Decimal(100000),
    quality: new Decimal(1.9),
    temporal: new Decimal(5000),
    reliability: new Decimal(0.98),
    innovation: new Decimal(10000)
  },
  strategy: 'Attract only the absolute best agents/teams',
  benefits: [
    'Guaranteed top-tier delivery',
    'Multiple revision rounds',
    'Priority support',
    'Innovation ownership options'
  ]
};
```

### Scenario 3: Experimental Innovation Project

```typescript
const experimentalContract = {
  title: 'Find the Next Viral Marketing Mechanism',
  budget: {
    economic: new Decimal(5000),
    quality: new Decimal(1.0), // Quality less important
    reliability: new Decimal(0.6), // Can take risks
    innovation: new Decimal(15000) // 3x economic in innovation!
  },
  strategy: 'Attract creative mavericks and innovators',
  potential: [
    'Could yield breakthrough results',
    'High risk, high reward',
    'Innovation value appreciates if successful'
  ]
};
```

## Currency Interaction Effects

### Synergistic Effects

```typescript
// How currencies interact and amplify each other
function calculateCurrencySynergies(budget: MultiCurrencyAmount) {
  const synergies = [];
  
  // Quality + Innovation synergy
  if (budget.quality?.gt(1.5) && budget.innovation?.gt(0)) {
    synergies.push({
      type: 'quality-innovation',
      effect: 'High quality work more likely to be innovative',
      bonus: budget.innovation.mul(0.2) // 20% innovation bonus
    });
  }
  
  // Temporal + Reliability synergy
  if (budget.temporal?.gt(0) && budget.reliability?.gt(0.8)) {
    synergies.push({
      type: 'speed-trust',
      effect: 'Reliable agents can deliver faster',
      bonus: budget.temporal.mul(0.15) // 15% temporal bonus
    });
  }
  
  // Economic + Quality synergy
  if (budget.economic.gt(5000) && budget.quality?.gt(1.6)) {
    synergies.push({
      type: 'premium-quality',
      effect: 'Higher budgets attract better quality',
      bonus: budget.economic.mul(0.1) // 10% economic efficiency
    });
  }
  
  return synergies;
}
```

## Best Practices

### Budget Design Checklist

```typescript
function validateBudgetDesign(budget: MultiCurrencyAmount, requirements: any) {
  const checks = [
    {
      name: 'Economic sufficiency',
      passed: budget.economic.gte(calculateMinimumViable(requirements)),
      fix: 'Increase economic budget to market rate'
    },
    {
      name: 'Quality-price alignment',
      passed: !budget.quality || 
              (budget.quality.gt(1.5) ? budget.economic.gt(5000) : true),
      fix: 'High quality requires higher economic budget'
    },
    {
      name: 'Temporal decay consideration',
      passed: !budget.temporal || requirements.deadline > 7,
      fix: 'Temporal incentives need adequate deadline'
    },
    {
      name: 'Reliability availability',
      passed: !budget.reliability || budget.reliability.lte(0.95),
      fix: 'Very few agents have >0.95 reliability'
    },
    {
      name: 'Innovation justification',
      passed: !budget.innovation || requirements.needsInnovation,
      fix: 'Only add innovation budget if truly needed'
    }
  ];
  
  return checks;
}
```

## Next Steps
- Experiment with different budget combinations
- Monitor which budgets attract the best agents
- Analyze ROI of different currency allocations
- Optimize budgets based on historical performance
- See [calculation-examples.md](../currency-operations/calculation-examples.md) for detailed calculations
