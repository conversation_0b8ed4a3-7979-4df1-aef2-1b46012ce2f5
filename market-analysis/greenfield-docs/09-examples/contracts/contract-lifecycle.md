# Contract Lifecycle Examples

## Purpose
This guide demonstrates the complete lifecycle of contracts in the VibeLaunch marketplace, from creation through completion, including state transitions, event handling, and payment flows.

## Prerequisites
- Understanding of contract states and transitions
- Knowledge of the event-driven architecture
- Familiarity with multi-currency escrow system

## Contract States and Transitions

### State Diagram

```typescript
enum ContractState {
  DRAFT = 'draft',           // Being created, not yet published
  OPEN = 'open',             // Published and accepting bids
  BIDDING = 'bidding',       // Active bidding period
  AWARDED = 'awarded',       // Bid selected, awaiting acceptance
  IN_PROGRESS = 'in_progress', // Work has begun
  IN_REVIEW = 'in_review',   // Deliverables submitted for review
  COMPLETED = 'completed',   // Successfully completed
  CANCELLED = 'cancelled',   // Cancelled by client
  DISPUTED = 'disputed',     // Under dispute resolution
  CLOSED = 'closed'          // Final state, archived
}

// Valid state transitions
const validTransitions: Record<ContractState, ContractState[]> = {
  [ContractState.DRAFT]: [ContractState.OPEN, ContractState.CANCELLED],
  [ContractState.OPEN]: [ContractState.BIDDING, ContractState.CANCELLED],
  [ContractState.BIDDING]: [ContractState.AWARDED, ContractState.CANCELLED],
  [ContractState.AWARDED]: [ContractState.IN_PROGRESS, ContractState.OPEN, ContractState.CANCELLED],
  [ContractState.IN_PROGRESS]: [ContractState.IN_REVIEW, ContractState.DISPUTED, ContractState.CANCELLED],
  [ContractState.IN_REVIEW]: [ContractState.COMPLETED, ContractState.IN_PROGRESS, ContractState.DISPUTED],
  [ContractState.COMPLETED]: [ContractState.CLOSED],
  [ContractState.CANCELLED]: [ContractState.CLOSED],
  [ContractState.DISPUTED]: [ContractState.IN_PROGRESS, ContractState.COMPLETED, ContractState.CANCELLED],
  [ContractState.CLOSED]: [] // Terminal state
};
```

### State Transition Handler

```typescript
class ContractLifecycleManager {
  private eventEmitter: EventEmitter;
  private escrowService: EscrowService;
  private notificationService: NotificationService;

  async transitionState(
    contractId: string, 
    newState: ContractState, 
    context?: any
  ): Promise<Contract> {
    // Get current contract
    const contract = await this.getContract(contractId);
    
    // Validate transition
    if (!this.isValidTransition(contract.state, newState)) {
      throw new Error(
        `Invalid transition from ${contract.state} to ${newState}`
      );
    }

    // Pre-transition actions
    await this.preTransitionActions(contract, newState, context);

    // Update state
    const updatedContract = await this.updateContractState(contractId, newState);

    // Post-transition actions
    await this.postTransitionActions(updatedContract, contract.state, context);

    // Emit event
    this.emitStateChangeEvent(updatedContract, contract.state);

    return updatedContract;
  }

  private async preTransitionActions(
    contract: Contract, 
    newState: ContractState, 
    context?: any
  ): Promise<void> {
    switch (newState) {
      case ContractState.OPEN:
        // Validate contract is complete
        this.validateContractCompleteness(contract);
        break;

      case ContractState.AWARDED:
        // Create escrow
        await this.escrowService.createEscrow({
          contractId: contract.id,
          amount: contract.budget,
          fromWallet: contract.organizationWalletId,
          toWallet: context.winningBid.agentWalletId
        });
        break;

      case ContractState.IN_PROGRESS:
        // Lock escrow funds
        await this.escrowService.lockFunds(contract.escrowId);
        // Start performance tracking
        await this.startPerformanceTracking(contract.id);
        break;

      case ContractState.COMPLETED:
        // Verify all deliverables
        await this.verifyDeliverables(contract);
        break;
    }
  }

  private async postTransitionActions(
    contract: Contract, 
    previousState: ContractState, 
    context?: any
  ): Promise<void> {
    switch (contract.state) {
      case ContractState.OPEN:
        // Notify potential bidders
        await this.notifyPotentialBidders(contract);
        // Start bid timer
        await this.startBidTimer(contract);
        break;

      case ContractState.AWARDED:
        // Notify winner and losers
        await this.notifyBidResults(contract, context.winningBid);
        // Set acceptance deadline
        await this.setAcceptanceDeadline(contract);
        break;

      case ContractState.IN_PROGRESS:
        // Initialize milestone tracking
        await this.initializeMilestones(contract);
        // Set up progress monitoring
        await this.setupProgressMonitoring(contract);
        break;

      case ContractState.COMPLETED:
        // Release escrow
        await this.escrowService.releaseFunds(contract.escrowId);
        // Update agent performance metrics
        await this.updatePerformanceMetrics(contract);
        // Request feedback
        await this.requestFeedback(contract);
        break;

      case ContractState.CANCELLED:
        // Refund escrow if exists
        if (contract.escrowId) {
          await this.escrowService.refundFunds(contract.escrowId);
        }
        // Notify all parties
        await this.notifyCancellation(contract, context?.reason);
        break;
    }
  }
}
```

## Event-Driven Updates

### Event System Implementation

```typescript
// Contract events
interface ContractEvent {
  id: string;
  contractId: string;
  type: ContractEventType;
  timestamp: Date;
  data: any;
  metadata?: any;
}

enum ContractEventType {
  // Lifecycle events
  CREATED = 'contract.created',
  PUBLISHED = 'contract.published',
  BID_RECEIVED = 'contract.bid_received',
  BID_SELECTED = 'contract.bid_selected',
  WORK_STARTED = 'contract.work_started',
  MILESTONE_COMPLETED = 'contract.milestone_completed',
  DELIVERABLE_SUBMITTED = 'contract.deliverable_submitted',
  REVIEW_REQUESTED = 'contract.review_requested',
  REVISION_REQUESTED = 'contract.revision_requested',
  COMPLETED = 'contract.completed',
  CANCELLED = 'contract.cancelled',
  DISPUTED = 'contract.disputed',
  
  // Financial events
  ESCROW_CREATED = 'contract.escrow_created',
  ESCROW_LOCKED = 'contract.escrow_locked',
  PAYMENT_RELEASED = 'contract.payment_released',
  REFUND_ISSUED = 'contract.refund_issued',
  
  // Communication events
  MESSAGE_SENT = 'contract.message_sent',
  FILE_UPLOADED = 'contract.file_uploaded',
  FEEDBACK_PROVIDED = 'contract.feedback_provided'
}

// Event handler
class ContractEventHandler {
  private subscribers: Map<ContractEventType, Set<EventSubscriber>> = new Map();
  
  async handleEvent(event: ContractEvent): Promise<void> {
    console.log(`Processing event: ${event.type} for contract ${event.contractId}`);
    
    // Get subscribers for this event type
    const subscribers = this.subscribers.get(event.type) || new Set();
    
    // Process event with each subscriber
    const promises = Array.from(subscribers).map(subscriber => 
      this.processWithSubscriber(subscriber, event)
    );
    
    await Promise.all(promises);
    
    // Store event for audit trail
    await this.storeEvent(event);
    
    // Emit real-time updates
    await this.emitRealtimeUpdate(event);
  }
  
  subscribe(eventType: ContractEventType, subscriber: EventSubscriber): void {
    if (!this.subscribers.has(eventType)) {
      this.subscribers.set(eventType, new Set());
    }
    this.subscribers.get(eventType)!.add(subscriber);
  }
  
  private async processWithSubscriber(
    subscriber: EventSubscriber, 
    event: ContractEvent
  ): Promise<void> {
    try {
      await subscriber.process(event);
    } catch (error) {
      console.error(`Subscriber ${subscriber.name} failed:`, error);
      // Implement retry logic or dead letter queue
    }
  }
}

// Example event subscribers
class NotificationSubscriber implements EventSubscriber {
  name = 'NotificationSubscriber';
  
  async process(event: ContractEvent): Promise<void> {
    switch (event.type) {
      case ContractEventType.BID_RECEIVED:
        await this.notifyClient(
          event.contractId, 
          'New bid received', 
          event.data
        );
        break;
        
      case ContractEventType.MILESTONE_COMPLETED:
        await this.notifyClient(
          event.contractId, 
          'Milestone completed', 
          event.data
        );
        break;
        
      case ContractEventType.COMPLETED:
        await this.sendCompletionNotifications(event);
        break;
    }
  }
}

class AnalyticsSubscriber implements EventSubscriber {
  name = 'AnalyticsSubscriber';
  
  async process(event: ContractEvent): Promise<void> {
    // Track metrics
    await this.trackMetric({
      event: event.type,
      contractId: event.contractId,
      timestamp: event.timestamp,
      data: event.data
    });
    
    // Update dashboards
    if (this.isSignificantEvent(event.type)) {
      await this.updateDashboards(event);
    }
  }
}
```

## Milestone Management

### Milestone Tracking System

```typescript
interface Milestone {
  id: string;
  contractId: string;
  name: string;
  description: string;
  deliverables: string[];
  budget: MultiCurrencyAmount;
  deadline: Date;
  status: MilestoneStatus;
  completedAt?: Date;
  evidence?: MilestoneEvidence[];
}

enum MilestoneStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PAID = 'paid'
}

class MilestoneManager {
  async createMilestones(contract: Contract): Promise<Milestone[]> {
    const milestones: Milestone[] = [];
    
    // Auto-generate milestones based on contract
    if (contract.milestoneConfig) {
      milestones.push(...this.createFromConfig(contract));
    } else {
      milestones.push(...this.createDefaultMilestones(contract));
    }
    
    // Set up milestone tracking
    for (const milestone of milestones) {
      await this.initializeMilestoneTracking(milestone);
    }
    
    return milestones;
  }
  
  async submitMilestone(
    milestoneId: string, 
    evidence: MilestoneEvidence[]
  ): Promise<Milestone> {
    const milestone = await this.getMilestone(milestoneId);
    
    // Validate submission
    this.validateMilestoneSubmission(milestone, evidence);
    
    // Update status
    milestone.status = MilestoneStatus.SUBMITTED;
    milestone.evidence = evidence;
    
    // Notify client for review
    await this.notifyClientForReview(milestone);
    
    // Start review timer
    await this.startReviewTimer(milestone);
    
    return await this.updateMilestone(milestone);
  }
  
  async approveMilestone(milestoneId: string, feedback?: string): Promise<void> {
    const milestone = await this.getMilestone(milestoneId);
    
    // Update status
    milestone.status = MilestoneStatus.APPROVED;
    milestone.completedAt = new Date();
    
    // Process payment
    await this.processMilestonePayment(milestone);
    
    // Update contract progress
    await this.updateContractProgress(milestone.contractId);
    
    // Notify agent
    await this.notifyAgent(milestone, 'approved', feedback);
  }
  
  async rejectMilestone(
    milestoneId: string, 
    reason: string, 
    requiredChanges: string[]
  ): Promise<void> {
    const milestone = await this.getMilestone(milestoneId);
    
    // Update status
    milestone.status = MilestoneStatus.REJECTED;
    
    // Create revision request
    await this.createRevisionRequest({
      milestoneId,
      reason,
      requiredChanges,
      deadline: this.calculateRevisionDeadline(milestone)
    });
    
    // Notify agent
    await this.notifyAgent(milestone, 'rejected', { reason, requiredChanges });
  }
  
  private async processMilestonePayment(milestone: Milestone): Promise<void> {
    // Calculate payment amount
    const payment = this.calculateMilestonePayment(milestone);
    
    // Release from escrow
    await this.escrowService.releasePartial({
      escrowId: milestone.contract.escrowId,
      amount: payment,
      milestoneId: milestone.id
    });
    
    // Update milestone status
    milestone.status = MilestoneStatus.PAID;
    await this.updateMilestone(milestone);
    
    // Record transaction
    await this.recordPaymentTransaction(milestone, payment);
  }
}
```

## Deliverable Management

### Deliverable Submission and Review

```typescript
interface Deliverable {
  id: string;
  contractId: string;
  milestoneId?: string;
  name: string;
  type: DeliverableType;
  status: DeliverableStatus;
  files: DeliverableFile[];
  metadata: any;
  submittedAt?: Date;
  reviewedAt?: Date;
  feedback?: Review[];
}

enum DeliverableType {
  DOCUMENT = 'document',
  CODE = 'code',
  DESIGN = 'design',
  VIDEO = 'video',
  DATA = 'data',
  REPORT = 'report',
  OTHER = 'other'
}

class DeliverableManager {
  async submitDeliverable(
    contractId: string,
    deliverable: DeliverableSubmission
  ): Promise<Deliverable> {
    // Validate deliverable
    await this.validateDeliverable(deliverable);
    
    // Process files
    const processedFiles = await this.processFiles(deliverable.files);
    
    // Create deliverable record
    const record: Deliverable = {
      id: generateId(),
      contractId,
      milestoneId: deliverable.milestoneId,
      name: deliverable.name,
      type: deliverable.type,
      status: DeliverableStatus.SUBMITTED,
      files: processedFiles,
      metadata: deliverable.metadata,
      submittedAt: new Date()
    };
    
    // Store deliverable
    await this.storeDeliverable(record);
    
    // Run automated checks
    await this.runAutomatedChecks(record);
    
    // Notify for review
    await this.notifyForReview(record);
    
    return record;
  }
  
  private async processFiles(files: File[]): Promise<DeliverableFile[]> {
    const processed: DeliverableFile[] = [];
    
    for (const file of files) {
      // Scan for malware
      await this.scanFile(file);
      
      // Upload to storage
      const url = await this.uploadFile(file);
      
      // Generate preview if applicable
      const preview = await this.generatePreview(file);
      
      processed.push({
        name: file.name,
        size: file.size,
        type: file.type,
        url,
        preview,
        hash: await this.calculateHash(file),
        uploadedAt: new Date()
      });
    }
    
    return processed;
  }
  
  private async runAutomatedChecks(deliverable: Deliverable): Promise<void> {
    const checks = [];
    
    // Type-specific checks
    switch (deliverable.type) {
      case DeliverableType.DOCUMENT:
        checks.push(this.checkDocumentQuality(deliverable));
        checks.push(this.checkPlagiarism(deliverable));
        break;
        
      case DeliverableType.CODE:
        checks.push(this.runCodeAnalysis(deliverable));
        checks.push(this.runSecurityScan(deliverable));
        break;
        
      case DeliverableType.DESIGN:
        checks.push(this.checkDesignSpecs(deliverable));
        checks.push(this.validateBrandCompliance(deliverable));
        break;
    }
    
    const results = await Promise.all(checks);
    
    // Store check results
    await this.storeCheckResults(deliverable.id, results);
  }
}
```

## Payment Flow

### Escrow and Payment Processing

```typescript
class ContractPaymentProcessor {
  async processContractPayment(contractId: string): Promise<PaymentResult> {
    const contract = await this.getContract(contractId);
    const escrow = await this.getEscrow(contract.escrowId);
    
    try {
      // Step 1: Validate completion
      const validation = await this.validateCompletion(contract);
      if (!validation.isValid) {
        throw new Error(`Completion validation failed: ${validation.reason}`);
      }
      
      // Step 2: Calculate final payment
      const payment = await this.calculateFinalPayment({
        contract,
        escrow,
        performanceMetrics: validation.metrics
      });
      
      // Step 3: Apply performance adjustments
      const adjusted = this.applyPerformanceAdjustments(payment, validation.metrics);
      
      // Step 4: Process multi-currency payment
      const transactions = await this.processMultiCurrencyPayment(adjusted);
      
      // Step 5: Update records
      await this.updatePaymentRecords(contract, transactions);
      
      // Step 6: Distribute team payments if applicable
      if (contract.teamId) {
        await this.distributeTeamPayments(contract, adjusted);
      }
      
      return {
        success: true,
        transactions,
        totalPaid: adjusted,
        performanceBonus: adjusted.performanceBonus
      };
      
    } catch (error) {
      // Handle payment failure
      await this.handlePaymentFailure(contract, error);
      throw error;
    }
  }
  
  private async calculateFinalPayment(params: any): Promise<PaymentBreakdown> {
    const { contract, escrow, performanceMetrics } = params;
    
    const breakdown: PaymentBreakdown = {
      base: {
        economic: escrow.amount.economic,
        quality: escrow.amount.quality,
        temporal: escrow.amount.temporal,
        reliability: escrow.amount.reliability,
        innovation: escrow.amount.innovation
      },
      adjustments: {},
      deductions: {},
      bonuses: {},
      final: {}
    };
    
    // Quality adjustments
    if (performanceMetrics.qualityScore) {
      const qualityMultiplier = performanceMetrics.qualityScore / contract.budget.quality;
      breakdown.adjustments.quality = 
        breakdown.base.economic.mul(qualityMultiplier - 1);
    }
    
    // Temporal adjustments (early delivery bonus)
    if (performanceMetrics.deliveredEarly) {
      const daysEarly = performanceMetrics.daysEarly;
      const temporalBonus = this.calculateTemporalBonus(daysEarly, contract);
      breakdown.bonuses.temporal = temporalBonus;
    }
    
    // Innovation rewards
    if (performanceMetrics.innovationScore > 0.8) {
      breakdown.bonuses.innovation = 
        contract.budget.innovation?.mul(performanceMetrics.innovationScore) || 
        new Decimal(0);
    }
    
    // Calculate final amounts
    breakdown.final = this.sumPaymentComponents(breakdown);
    
    return breakdown;
  }
  
  private async distributeTeamPayments(
    contract: Contract, 
    payment: PaymentBreakdown
  ): Promise<void> {
    const team = await this.getTeam(contract.teamId);
    const distribution = this.calculateTeamDistribution(team, payment);
    
    // Process individual payments
    for (const member of distribution) {
      await this.processAgentPayment({
        agentId: member.agentId,
        amount: member.amount,
        contractId: contract.id,
        role: member.role,
        contribution: member.contribution
      });
    }
    
    // Record distribution
    await this.recordTeamDistribution(contract.id, distribution);
  }
}
```

## Dispute Resolution

### Dispute Handling Workflow

```typescript
class DisputeResolutionService {
  async initiateDispute(
    contractId: string, 
    disputeRequest: DisputeRequest
  ): Promise<Dispute> {
    const contract = await this.getContract(contractId);
    
    // Validate dispute can be initiated
    this.validateDisputeEligibility(contract, disputeRequest);
    
    // Create dispute record
    const dispute: Dispute = {
      id: generateId(),
      contractId,
      initiatedBy: disputeRequest.initiatedBy,
      type: disputeRequest.type,
      description: disputeRequest.description,
      evidence: disputeRequest.evidence,
      status: DisputeStatus.OPEN,
      createdAt: new Date(),
      deadline: this.calculateResolutionDeadline(disputeRequest.type)
    };
    
    // Freeze contract and escrow
    await this.freezeContract(contractId);
    await this.freezeEscrow(contract.escrowId);
    
    // Assign mediator
    const mediator = await this.assignMediator(dispute);
    dispute.mediatorId = mediator.id;
    
    // Notify all parties
    await this.notifyDisputeParties(dispute);
    
    // Start resolution process
    await this.startResolutionProcess(dispute);
    
    return dispute;
  }
  
  async resolveDispute(
    disputeId: string, 
    resolution: DisputeResolution
  ): Promise<void> {
    const dispute = await this.getDispute(disputeId);
    const contract = await this.getContract(dispute.contractId);
    
    // Apply resolution
    switch (resolution.outcome) {
      case 'favor_client':
        await this.resolveInClientFavor(contract, resolution);
        break;
        
      case 'favor_agent':
        await this.resolveInAgentFavor(contract, resolution);
        break;
        
      case 'partial':
        await this.resolvePartial(contract, resolution);
        break;
        
      case 'mutual_agreement':
        await this.resolveMutualAgreement(contract, resolution);
        break;
    }
    
    // Update dispute status
    dispute.status = DisputeStatus.RESOLVED;
    dispute.resolution = resolution;
    dispute.resolvedAt = new Date();
    await this.updateDispute(dispute);
    
    // Unfreeze contract
    await this.unfreezeContract(contract.id);
    
    // Process payments according to resolution
    await this.processResolutionPayments(contract, resolution);
    
    // Update reputation scores
    await this.updateReputationScores(dispute, resolution);
  }
}
```

## Real-time Updates

### WebSocket Integration

```typescript
class ContractRealtimeService {
  private wsConnections: Map<string, Set<WebSocket>> = new Map();
  
  subscribeToContract(contractId: string, ws: WebSocket): void {
    if (!this.wsConnections.has(contractId)) {
      this.wsConnections.set(contractId, new Set());
    }
    this.wsConnections.get(contractId)!.add(ws);
    
    // Send initial state
    this.sendContractState(contractId, ws);
  }
  
  async broadcastContractUpdate(
    contractId: string, 
    update: ContractUpdate
  ): Promise<void> {
    const connections = this.wsConnections.get(contractId) || new Set();
    
    const message = JSON.stringify({
      type: 'contract_update',
      contractId,
      update,
      timestamp: new Date()
    });
    
    // Broadcast to all subscribers
    for (const ws of connections) {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      } else {
        // Clean up closed connections
        connections.delete(ws);
      }
    }
  }
  
  setupContractEventStream(contractId: string): void {
    // Subscribe to contract events
    this.eventBus.subscribe(`contract.${contractId}.*`, async (event) => {
      const update = this.transformEventToUpdate(event);
      await this.broadcastContractUpdate(contractId, update);
    });
  }
}
```

## Complete Lifecycle Example

### End-to-End Contract Flow

```typescript
// Complete example of contract lifecycle
async function completeContractLifecycle() {
  const lifecycle = new ContractLifecycleOrchestrator();
  
  try {
    // 1. Create and publish contract
    const contract = await lifecycle.createContract({
      title: 'Complete Marketing Campaign',
      budget: {
        economic: new Decimal(10000),
        quality: new Decimal(1.5),
        temporal: new Decimal(1000),
        reliability: new Decimal(0.9),
        innovation: new Decimal(500)
      },
      deadline: addDays(new Date(), 30)
    });
    
    console.log('Contract created:', contract.id);
    
    // 2. Publish and collect bids
    await lifecycle.publishContract(contract.id);
    const bids = await lifecycle.collectBids(contract.id, {
      duration: '24h',
      minimumBids: 3
    });
    
    console.log(`Received ${bids.length} bids`);
    
    // 3. Select winning bid
    const winner = await lifecycle.selectWinningBid(contract.id, {
      strategy: 'best_value',
      weights: {
        price: 0.3,
        quality: 0.3,
        experience: 0.2,
        innovation: 0.2
      }
    });
    
    console.log('Winner selected:', winner.agentId);
    
    // 4. Award contract and start work
    await lifecycle.awardContract(contract.id, winner.id);
    await lifecycle.startWork(contract.id);
    
    // 5. Track milestones
    lifecycle.on('milestone_completed', async (milestone) => {
      console.log('Milestone completed:', milestone.name);
      await lifecycle.reviewMilestone(milestone.id);
    });
    
    // 6. Handle deliverables
    lifecycle.on('deliverable_submitted', async (deliverable) => {
      console.log('Deliverable received:', deliverable.name);
      const review = await lifecycle.reviewDeliverable(deliverable.id);
      
      if (review.approved) {
        await lifecycle.approveDeliverable(deliverable.id);
      } else {
        await lifecycle.requestRevision(deliverable.id, review.feedback);
      }
    });
    
    // 7. Complete contract
    lifecycle.on('all_deliverables_approved', async () => {
      await lifecycle.completeContract(contract.id);
      console.log('Contract completed successfully');
    });
    
    // 8. Process payment
    const payment = await lifecycle.processPayment(contract.id);
    console.log('Payment processed:', payment);
    
    // 9. Collect feedback
    const feedback = await lifecycle.collectFeedback(contract.id);
    console.log('Feedback collected:', feedback);
    
    // 10. Close contract
    await lifecycle.closeContract(contract.id);
    console.log('Contract closed');
    
  } catch (error) {
    console.error('Contract lifecycle error:', error);
    // Handle error appropriately
  }
}
```

## Next Steps
- Implement state machine for your contracts
- Set up event handlers for all state transitions
- Configure milestone tracking for your use case
- Test payment flows with different scenarios
- See [multi-currency-budgets.md](./multi-currency-budgets.md) for budget examples
