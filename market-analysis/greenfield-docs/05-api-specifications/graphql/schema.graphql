# VibeLaunch Genesis GraphQL Schema
# Revolutionary AI-powered marketplace with five-dimensional currency system
# Targeting 95%+ efficiency and 194.4% team synergy

scalar DateTime
scalar Decimal
scalar UUID
scalar JSON
scalar Currency "Custom scalar for precise currency calculations"

# =====================================================
# DIRECTIVES
# =====================================================

directive @auth(requires: UserRole!) on FIELD_DEFINITION
directive @rateLimit(max: Int!, window: String!) on FIELD_DEFINITION
directive @economicValidation on FIELD_DEFINITION
directive @deprecated(reason: String!) on FIELD_DEFINITION | ENUM_VALUE

# =====================================================
# ENUMS
# =====================================================

enum OrganizationType {
  BUSINESS
  AGENCY
  INDIVIDUAL
  ENTERPRISE
}

enum UserRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
  GUEST
}

"Five-dimensional currency types in the VibeLaunch economy"
enum CurrencyType {
  "Economic value (₥) - Base monetary unit"
  ECONOMIC
  "Quality multiplier (◈) - 0-2x value enhancement"
  QUALITY
  "Temporal value (⧗) - Time-decaying currency"
  TEMPORAL
  "Reliability score (☆) - Trust-based yield generation"
  RELIABILITY
  "Innovation factor (◊) - Appreciation-based growth"
  INNOVATION
}

enum ContractStatus {
  DRAFT
  OPEN
  BIDDING
  IN_PROGRESS
  REVIEW
  COMPLETED
  CANCELLED
  DISPUTED
}

enum BidStatus {
  PENDING
  ACCEPTED
  REJECTED
  WITHDRAWN
  EXPIRED
}

enum AgentType {
  MASTER
  CONTENT_CREATOR
  SEO_SPECIALIST
  SOCIAL_MEDIA
  DATA_ANALYST
  VISUAL_DESIGNER
  EMAIL_MARKETER
  CREATIVE_DIRECTOR
}

enum TransactionType {
  TRANSFER
  EXCHANGE
  FEE
  REWARD
  PENALTY
  ESCROW
  YIELD
  SETTLEMENT
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  REVERSED
}

enum OrderSide {
  BUY
  SELL
}

enum OrderType {
  MARKET
  LIMIT
  STOP
  STOP_LIMIT
}

enum OrderStatus {
  PENDING
  PARTIAL
  FILLED
  CANCELLED
  REJECTED
  EXPIRED
}

enum TeamStatus {
  FORMING
  ACTIVE
  DISBANDED
  SUSPENDED
}

enum ProposalType {
  PARAMETER_CHANGE
  FEATURE_ADDITION
  ECONOMIC_ADJUSTMENT
  EMERGENCY
}

enum ProposalStatus {
  DRAFT
  ACTIVE
  PASSED
  REJECTED
  IMPLEMENTED
}

enum VoteType {
  YES
  NO
  ABSTAIN
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum EconomicLaw {
  VALUE_CONSERVATION
  INFORMATION_ENTROPY
  COLLABORATIVE_ADVANTAGE
  REPUTATION_ACCUMULATION
}

# =====================================================
# INTERFACES
# =====================================================

interface Node {
  id: UUID!
}

interface Timestamped {
  createdAt: DateTime!
  updatedAt: DateTime!
}

interface Economical {
  economicValidation: EconomicValidation!
}

# =====================================================
# INPUT TYPES
# =====================================================

input CreateOrganizationInput {
  name: String!
  type: OrganizationType!
  metadata: JSON
  settings: OrganizationSettingsInput
}

input OrganizationSettingsInput {
  autoSelectBids: Boolean
  preferredAgentTypes: [AgentType!]
  riskTolerance: RiskLevel
}

input UpdateOrganizationInput {
  name: String
  metadata: JSON
  settings: OrganizationSettingsInput
}

input CreateContractInput {
  title: String!
  description: String!
  requirements: [String!]!
  budget: MultiCurrencyAmountInput!
  deadline: DateTime!
  teamRequirements: TeamRequirementsInput
  tags: [String!]
  metadata: JSON
}

input TeamRequirementsInput {
  minSize: Int = 1
  maxSize: Int = 5
  requiredCapabilities: [String!]
  targetSynergy: Decimal
}

input UpdateContractInput {
  title: String
  description: String
  requirements: [String!]
  budget: MultiCurrencyAmountInput
  deadline: DateTime
  tags: [String!]
}

input MultiCurrencyAmountInput {
  economic: Decimal
  quality: Decimal
  temporal: Decimal
  reliability: Decimal
  innovation: Decimal
}

input SubmitBidInput {
  contractId: UUID!
  pricing: MultiCurrencyAmountInput!
  proposal: String!
  deliveryTime: Int! # hours
  teamAgentIds: [UUID!]
  approach: JSON
}

input CreateTeamInput {
  name: String!
  agentIds: [UUID!]!
  contractId: UUID
  purpose: String
}

input TransferCurrencyInput {
  fromWallet: UUID!
  toWallet: UUID!
  currency: CurrencyType!
  amount: Decimal!
  metadata: JSON
}

input BatchTransferInput {
  transfers: [TransferCurrencyInput!]!
  atomic: Boolean = true
}

input ExchangeCurrencyInput {
  walletId: UUID!
  fromCurrency: CurrencyType!
  toCurrency: CurrencyType!
  amount: Decimal!
  slippageTolerance: Decimal = 0.01
}

input CreateOrderInput {
  pair: String! # e.g., "ECO_QUA"
  side: OrderSide!
  type: OrderType!
  price: Decimal
  quantity: Decimal!
  stopPrice: Decimal
  timeInForce: String = "GTC"
}

input RegisterAgentInput {
  name: String!
  type: AgentType!
  capabilities: [String!]!
  llmProvider: String!
  llmConfig: JSON
}

input UpdateAgentInput {
  name: String
  capabilities: [String!]
  status: String
  llmConfig: JSON
}

input CreateProposalInput {
  title: String!
  description: String!
  type: ProposalType!
  category: String!
  changes: [ProposalChangeInput!]!
}

input ProposalChangeInput {
  parameter: String!
  currentValue: String!
  proposedValue: String!
  impact: String
}

input RiskAssessmentInput {
  entityId: UUID!
  entityType: String!
  riskCategories: [String!]
}

input TimeRangeInput {
  start: DateTime!
  end: DateTime!
}

input PaginationInput {
  limit: Int = 20
  offset: Int = 0
  cursor: String
}

input FilterInput {
  field: String!
  operator: String!
  value: JSON!
}

input SortInput {
  field: String!
  direction: String! # ASC or DESC
}

# =====================================================
# OBJECT TYPES
# =====================================================

type Organization implements Node & Timestamped {
  id: UUID!
  name: String!
  type: OrganizationType!
  wallet: Wallet!
  users: [User!]!
  contracts(
    status: ContractStatus
    pagination: PaginationInput
    sort: SortInput
  ): ContractConnection!
  teams: [Team!]!
  economicProfile: EconomicProfile!
  metadata: JSON
  settings: OrganizationSettings!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type OrganizationSettings {
  autoSelectBids: Boolean!
  preferredAgentTypes: [AgentType!]!
  riskTolerance: RiskLevel!
}

type EconomicProfile {
  totalSpent: MultiCurrencyAmount!
  totalEarned: MultiCurrencyAmount!
  contractsCreated: Int!
  contractsCompleted: Int!
  averageEfficiency: Decimal!
  reliabilityScore: Decimal!
}

type User implements Node & Timestamped {
  id: UUID!
  organization: Organization!
  email: String!
  name: String
  role: UserRole!
  votingPower: Decimal!
  lastLogin: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Wallet implements Node {
  id: UUID!
  owner: WalletOwner!
  balances: MultiCurrencyAmount!
  locked: MultiCurrencyAmount!
  pending: MultiCurrencyAmount!
  yields: YieldInfo!
  temporalDecay: TemporalDecayInfo!
  transactions(
    currency: CurrencyType
    type: TransactionType
    timeRange: TimeRangeInput
    pagination: PaginationInput
  ): TransactionConnection!
  history(period: String!): [WalletSnapshot!]!
  version: Int!
  updatedAt: DateTime!
}

union WalletOwner = User | Agent | Organization | Team | SystemWallet

type SystemWallet {
  id: UUID!
  type: String!
  description: String!
}

type MultiCurrencyAmount {
  economic: Decimal!
  quality: Decimal!
  temporal: Decimal!
  reliability: Decimal!
  innovation: Decimal!
  # Computed fields
  totalValueUSD: Decimal!
  totalValueEconomic: Decimal!
  temporalDecayRate: Decimal!
  reliabilityYieldRate: Decimal!
  innovationAppreciationRate: Decimal!
}

type YieldInfo {
  reliabilityYield: Decimal!
  nextYieldDate: DateTime!
  yieldRate: Decimal!
  historicalYields: Decimal!
  compoundingEnabled: Boolean!
}

type TemporalDecayInfo {
  lastDecayApplied: DateTime!
  nextDecayDate: DateTime!
  decayRate: Decimal!
  totalDecayed: Decimal!
}

type Contract implements Node & Timestamped & Economical {
  id: UUID!
  organization: Organization!
  title: String!
  description: String!
  requirements: [String!]!
  deliverables: [Deliverable!]!
  budget: MultiCurrencyAmount!
  escrowAmount: MultiCurrencyAmount!
  deadline: DateTime!
  status: ContractStatus!
  
  # Team requirements
  teamRequirements: TeamRequirements!
  
  # Bidding
  bids(
    status: BidStatus
    orderBy: BidOrderBy
    pagination: PaginationInput
  ): BidConnection!
  selectedBid: Bid
  winningTeam: Team
  
  # Performance
  actualQuality: Decimal
  actualDeliveryTime: Int
  efficiencyImpact: Decimal!
  synergyAchieved: Decimal
  
  # Economic validation
  economicValidation: EconomicValidation!
  
  # Metadata
  tags: [String!]!
  metadata: JSON
  createdAt: DateTime!
  updatedAt: DateTime!
  completedAt: DateTime
  
  # Computed fields
  timeRemaining: String!
  bidCount: Int!
  averageBidPrice: MultiCurrencyAmount!
  lowestBidPrice: MultiCurrencyAmount!
  marketEfficiencyScore: Decimal!
}

type TeamRequirements {
  minSize: Int!
  maxSize: Int!
  requiredCapabilities: [String!]!
  targetSynergy: Decimal!
  preferredAgentTypes: [AgentType!]
}

type Deliverable {
  id: UUID!
  title: String!
  description: String
  status: String!
  dueDate: DateTime
  completedAt: DateTime
  qualityScore: Decimal
  deliveryUrl: String
}

type Bid implements Node & Timestamped & Economical {
  id: UUID!
  contract: Contract!
  leadAgent: Agent!
  pricing: MultiCurrencyAmount!
  proposal: String!
  approach: JSON
  deliveryTime: Int! # hours
  
  # Team bidding
  team: Team
  synergyScore: Decimal!
  
  # Scoring
  totalScore: Decimal!
  priceScore: Decimal!
  qualityScore: Decimal!
  timeScore: Decimal!
  synergyBonus: Decimal!
  efficiencyScore: Decimal!
  
  # Economic validation
  economicValidation: EconomicValidation!
  
  # Status
  status: BidStatus!
  rejectionReason: String
  createdAt: DateTime!
  updatedAt: DateTime!
  
  # Computed fields
  savingsPercentage: Decimal!
  rankingPosition: Int!
  competitiveAdvantage: Decimal!
}

type Team implements Node & Timestamped {
  id: UUID!
  name: String!
  agents: [TeamMember!]!
  leadAgent: Agent!
  wallet: Wallet!
  
  # Synergy metrics
  synergyScore: Decimal!
  targetSynergy: Decimal!
  synergyBreakdown: SynergyBreakdown!
  
  # Performance
  contracts(
    status: ContractStatus
    pagination: PaginationInput
  ): ContractConnection!
  performance: TeamPerformance!
  
  # Status
  status: TeamStatus!
  purpose: String
  contractId: UUID
  
  # Timestamps
  createdAt: DateTime!
  updatedAt: DateTime!
  disbandedAt: DateTime
}

type TeamMember {
  agent: Agent!
  role: String!
  contribution: Decimal!
  joinedAt: DateTime!
}

type SynergyBreakdown {
  skillDiversity: Decimal!
  collaborationHistory: Decimal!
  specializationBonus: Decimal!
  communicationEfficiency: Decimal!
  innovationMultiplier: Decimal!
  total: Decimal!
}

type TeamPerformance {
  contractsCompleted: Int!
  successRate: Decimal!
  averageQuality: Decimal!
  averageDeliveryTime: Int!
  totalEarnings: MultiCurrencyAmount!
  efficiencyContribution: Decimal!
  reliabilityScore: Decimal!
}

type Agent implements Node & Timestamped {
  id: UUID!
  name: String!
  type: AgentType!
  capabilities: [String!]!
  wallet: Wallet!
  
  # Performance
  performance: AgentPerformance!
  rankings: AgentRankings!
  
  # Team participation
  teams(active: Boolean): [Team!]!
  teamHistory: [TeamHistoryEntry!]!
  
  # Configuration
  llmProvider: String!
  llmConfig: JSON
  status: String!
  
  # History
  contracts(
    status: ContractStatus
    pagination: PaginationInput
  ): ContractConnection!
  bids(
    status: BidStatus
    pagination: PaginationInput
  ): BidConnection!
  
  # Innovation tracking
  innovations: [Innovation!]!
  innovationScore: Decimal!
  
  # Timestamps
  createdAt: DateTime!
  updatedAt: DateTime!
  lastActiveAt: DateTime!
}

type AgentPerformance {
  totalContracts: Int!
  successfulContracts: Int!
  failedContracts: Int!
  averageQuality: Decimal!
  averageDeliveryTime: Int!
  reliabilityScore: Decimal!
  totalEarnings: MultiCurrencyAmount!
  
  # Synergy metrics
  averageSynergyContribution: Decimal!
  maxSynergyAchieved: Decimal!
  preferredTeamSize: Int!
  
  # Trends
  monthlyImprovement: Decimal!
  successRate: Decimal!
  efficiencyContribution: Decimal!
}

type AgentRankings {
  overall: Int!
  reliability: Int!
  quality: Int!
  earnings: Int!
  innovation: Int!
  synergy: Int!
  efficiency: Int!
}

type TeamHistoryEntry {
  team: Team!
  role: String!
  joinedAt: DateTime!
  leftAt: DateTime
  contractsCompleted: Int!
  averageSynergy: Decimal!
}

type Innovation {
  id: UUID!
  agent: Agent!
  type: String!
  description: String!
  impactScore: Decimal!
  adoptionRate: Decimal!
  economicValue: MultiCurrencyAmount!
  createdAt: DateTime!
}

type Transaction implements Node & Timestamped & Economical {
  id: UUID!
  fromWallet: Wallet
  toWallet: Wallet
  amounts: MultiCurrencyAmount!
  currencyType: CurrencyType!
  type: TransactionType!
  status: TransactionStatus!
  
  # Economic validation
  economicValidation: EconomicValidation!
  
  # Exchange details
  exchangeRate: Decimal
  exchangeFee: Decimal
  slippage: Decimal
  
  # Settlement
  settlementGroup: SettlementGroup
  
  # Metadata
  metadata: JSON
  reference: String
  createdAt: DateTime!
  updatedAt: DateTime!
  completedAt: DateTime
  reversedAt: DateTime
}

type SettlementGroup {
  id: UUID!
  transactions: [Transaction!]!
  status: String!
  atomic: Boolean!
  totalValue: MultiCurrencyAmount!
  createdAt: DateTime!
}

type MarketData {
  pair: String!
  lastPrice: Decimal!
  bidPrice: Decimal!
  askPrice: Decimal!
  spread: Decimal!
  volume24h: MultiCurrencyAmount!
  high24h: Decimal!
  low24h: Decimal!
  changePercent24h: Decimal!
  
  # Liquidity metrics
  liquidityScore: Decimal!
  marketDepth: Decimal!
  
  # Order book
  orderBook(depth: Int = 20): OrderBook!
  
  # Historical
  candlesticks(
    interval: CandlestickInterval!
    limit: Int = 100
  ): [Candlestick!]!
  
  # Market efficiency
  priceEfficiency: Decimal!
  
  # Real-time
  timestamp: DateTime!
}

enum CandlestickInterval {
  ONE_MINUTE
  FIVE_MINUTES
  FIFTEEN_MINUTES
  ONE_HOUR
  FOUR_HOURS
  ONE_DAY
  ONE_WEEK
}

type OrderBook {
  pair: String!
  bids: [OrderBookEntry!]!
  asks: [OrderBookEntry!]!
  spread: Decimal!
  spreadPercent: Decimal!
  midPrice: Decimal!
  imbalance: Decimal!
  timestamp: DateTime!
}

type OrderBookEntry {
  price: Decimal!
  quantity: Decimal!
  total: Decimal!
  orderCount: Int!
}

type Candlestick {
  time: DateTime!
  open: Decimal!
  high: Decimal!
  low: Decimal!
  close: Decimal!
  volume: Decimal!
  trades: Int!
}

type Order implements Node & Timestamped {
  id: UUID!
  wallet: Wallet!
  pair: String!
  side: OrderSide!
  type: OrderType!
  price: Decimal
  stopPrice: Decimal
  quantity: Decimal!
  filledQuantity: Decimal!
  remainingQuantity: Decimal!
  averagePrice: Decimal
  status: OrderStatus!
  timeInForce: String!
  fees: MultiCurrencyAmount!
  trades: [Trade!]!
  createdAt: DateTime!
  updatedAt: DateTime!
  filledAt: DateTime
  cancelledAt: DateTime
}

type ExchangeRates {
  base: CurrencyType!
  rates: [ExchangeRate!]!
  spreads: [SpreadInfo!]!
  liquidity: [LiquidityInfo!]!
  lastUpdate: DateTime!
}

type ExchangeRate {
  currency: CurrencyType!
  rate: Decimal!
  volume24h: Decimal!
  change24h: Decimal!
}

type SpreadInfo {
  pair: String!
  spread: Decimal!
  spreadPercent: Decimal!
}

type LiquidityInfo {
  pair: String!
  score: Decimal!
  depth: Decimal!
}

type EfficiencyMetrics {
  overall: Decimal!
  target: Decimal!
  achievingTarget: Boolean!
  components: EfficiencyComponents!
  trend: [EfficiencyPoint!]!
  improvementRate: Decimal!
  projectedTargetDate: DateTime
  recommendations: [String!]!
}

type EfficiencyComponents {
  priceDiscovery: Decimal!
  allocation: Decimal!
  execution: Decimal!
  information: Decimal!
  innovation: Decimal!
}

type EfficiencyPoint {
  timestamp: DateTime!
  value: Decimal!
  components: EfficiencyComponents!
}

type TeamAnalysis {
  agents: [Agent!]!
  synergyScore: Decimal!
  targetSynergy: Decimal!
  synergyBreakdown: SynergyBreakdown!
  predictedPerformance: TeamPerformancePrediction!
  optimalComposition: Boolean!
  recommendations: [String!]!
  alternativeCompositions: [TeamComposition!]!
}

type TeamComposition {
  agents: [Agent!]!
  expectedSynergy: Decimal!
  expectedPerformance: TeamPerformancePrediction!
  strengthsWeaknesses: JSON!
}

type TeamPerformancePrediction {
  expectedQuality: Decimal!
  expectedDeliveryTime: Int!
  successProbability: Decimal!
  efficiencyGain: Decimal!
  expectedCost: MultiCurrencyAmount!
}

type EconomicHealth {
  timestamp: DateTime!
  overallHealth: Decimal!
  laws: EconomicLawsStatus!
  systemMetrics: SystemMetrics!
  alerts: [EconomicAlert!]!
  predictions: EconomicPredictions!
}

type EconomicLawsStatus {
  valueConservation: LawStatus!
  informationEntropy: LawStatus!
  collaborativeAdvantage: LawStatus!
  reputationAccumulation: LawStatus!
}

type LawStatus {
  compliant: Boolean!
  score: Decimal!
  violations: [EconomicViolation!]!
  trend: String!
}

type EconomicViolation {
  law: EconomicLaw!
  description: String!
  severity: RiskLevel!
  impact: Decimal!
  entities: [UUID!]!
  detectedAt: DateTime!
}

type SystemMetrics {
  totalUsers: Int!
  activeUsers: Int!
  totalAgents: Int!
  activeAgents: Int!
  totalTeams: Int!
  activeTeams: Int!
  contractVolume: MultiCurrencyAmount!
  transactionVolume: MultiCurrencyAmount!
  marketLiquidity: Decimal!
  averageSynergy: Decimal!
}

type EconomicAlert {
  id: UUID!
  type: String!
  severity: RiskLevel!
  message: String!
  affectedEntities: [UUID!]!
  timestamp: DateTime!
  autoResolved: Boolean!
}

type EconomicPredictions {
  efficiencyForecast: [EfficiencyPoint!]!
  volumeForecast: [VolumePoint!]!
  riskForecast: [RiskPoint!]!
}

type VolumePoint {
  timestamp: DateTime!
  volume: MultiCurrencyAmount!
}

type RiskPoint {
  timestamp: DateTime!
  riskScore: Decimal!
  primaryRisks: [String!]!
}

type Proposal implements Node & Timestamped {
  id: UUID!
  title: String!
  description: String!
  type: ProposalType!
  category: String!
  status: ProposalStatus!
  proposer: User!
  
  # Changes
  changes: [ProposalChange!]!
  economicImpact: EconomicImpactAnalysis!
  
  # Voting
  votingPower: VotingPowerInfo!
  votes: VoteSummary!
  userVote(userId: UUID!): Vote
  
  # Timeline
  votingStartsAt: DateTime!
  votingEndsAt: DateTime!
  implementationDate: DateTime
  
  # Metadata
  discussion: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type ProposalChange {
  parameter: String!
  currentValue: String!
  proposedValue: String!
  impact: String!
  validation: EconomicValidation!
}

type EconomicImpactAnalysis {
  efficiencyImpact: Decimal!
  volumeImpact: MultiCurrencyAmount!
  riskImpact: Decimal!
  affectedUsers: Int!
  affectedAgents: Int!
}

type VotingPowerInfo {
  totalPower: Decimal!
  quorum: Decimal!
  threshold: Decimal!
  participation: Decimal!
}

type VoteSummary {
  yes: Decimal!
  no: Decimal!
  abstain: Decimal!
  total: Decimal!
}

type Vote {
  id: UUID!
  proposal: Proposal!
  voter: User!
  vote: VoteType!
  votingPower: Decimal!
  reason: String
  timestamp: DateTime!
}

type RiskAssessment {
  entityId: UUID!
  entityType: String!
  overallScore: Decimal!
  riskLevel: RiskLevel!
  categories: [RiskCategory!]!
  historicalIncidents: [RiskIncident!]!
  recommendations: [RiskRecommendation!]!
  validUntil: DateTime!
}

type RiskCategory {
  name: String!
  score: Decimal!
  factors: [RiskFactor!]!
}

type RiskFactor {
  name: String!
  impact: Decimal!
  probability: Decimal!
  description: String!
  mitigations: [String!]!
}

type RiskIncident {
  type: String!
  severity: RiskLevel!
  date: DateTime!
  resolved: Boolean!
  impactValue: MultiCurrencyAmount!
}

type RiskRecommendation {
  action: String!
  priority: RiskLevel!
  expectedRiskReduction: Decimal!
  estimatedCost: MultiCurrencyAmount!
  implementation: String!
}

type EconomicValidation {
  valid: Boolean!
  valueConserved: Boolean!
  efficiencyImpact: Decimal!
  synergyContribution: Decimal!
  violations: [EconomicViolation!]!
  warnings: [String!]!
}

# =====================================================
# CONNECTIONS (Pagination)
# =====================================================

type ContractConnection {
  edges: [ContractEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
  aggregations: ContractAggregations!
}

type ContractEdge {
  node: Contract!
  cursor: String!
}

type ContractAggregations {
  totalBudget: MultiCurrencyAmount!
  averageEfficiency: Decimal!
  averageSynergy: Decimal!
  statusCounts: JSON!
  tagCloud: [TagCount!]!
}

type TagCount {
  tag: String!
  count: Int!
}

type BidConnection {
  edges: [BidEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
  statistics: BidStatistics!
}

type BidEdge {
  node: Bid!
  cursor: String!
}

type BidStatistics {
  averagePrice: MultiCurrencyAmount!
  lowestPrice: MultiCurrencyAmount!
  highestPrice: MultiCurrencyAmount!
  averageSynergy: Decimal!
  maxSynergy: Decimal!
  priceRange: Decimal!
}

type TransactionConnection {
  edges: [TransactionEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
  totalVolume: MultiCurrencyAmount!
  summary: TransactionSummary!
}

type TransactionEdge {
  node: Transaction!
  cursor: String!
}

type TransactionSummary {
  byType: JSON!
  byCurrency: JSON!
  averageSize: MultiCurrencyAmount!
  largestTransaction: Transaction!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
  totalPages: Int!
  currentPage: Int!
}

type WalletSnapshot {
  timestamp: DateTime!
  balances: MultiCurrencyAmount!
  locked: MultiCurrencyAmount!
  totalValueUSD: Decimal!
  totalValueEconomic: Decimal!
}

# =====================================================
# QUERY ROOT
# =====================================================

type Query {
  # Organizations
  organization(id: UUID!): Organization
  organizations(
    type: OrganizationType
    filter: FilterInput
    sort: SortInput
    pagination: PaginationInput
  ): [Organization!]!
  
  # Users
  me: User @auth(requires: VIEWER)
  user(id: UUID!): User
  users(
    organizationId: UUID
    role: UserRole
    pagination: PaginationInput
  ): [User!]!
  
  # Contracts
  contract(id: UUID!): Contract
  contracts(
    organizationId: UUID
    status: ContractStatus
    tags: [String!]
    minBudget: Decimal
    maxBudget: Decimal
    minEfficiency: Decimal
    teamSize: Int
    filter: FilterInput
    sort: SortInput
    pagination: PaginationInput
  ): ContractConnection!
  
  # Bids
  bid(id: UUID!): Bid
  bids(
    contractId: UUID
    agentId: UUID
    status: BidStatus
    pagination: PaginationInput
  ): BidConnection!
  
  # Teams
  team(id: UUID!): Team
  teams(
    status: TeamStatus
    minSynergy: Decimal
    contractId: UUID
    agentId: UUID
    pagination: PaginationInput
  ): [Team!]!
  
  optimalTeam(
    contractId: UUID!
    availableAgentIds: [UUID!]!
    constraints: TeamConstraints
  ): TeamAnalysis!
  
  # Agents
  agent(id: UUID!): Agent
  agents(
    type: AgentType
    capability: String
    minReliability: Decimal
    status: String
    teamId: UUID
    filter: FilterInput
    sort: SortInput
    pagination: PaginationInput
  ): [Agent!]!
  
  agentRankings(
    metric: RankingMetric!
    type: AgentType
    period: String
    limit: Int = 10
  ): [RankedAgent!]!
  
  # Wallets
  wallet(id: UUID!): Wallet
  walletHistory(
    walletId: UUID!
    period: String!
    interval: String
  ): [WalletSnapshot!]!
  
  # Markets
  marketData(pair: String!): MarketData
  marketPairs: [MarketPair!]!
  exchangeRates(
    base: CurrencyType!
    targets: [CurrencyType!]
  ): ExchangeRates!
  
  orderBook(
    pair: String!
    depth: Int = 20
  ): OrderBook!
  
  # Orders
  order(id: UUID!): Order
  orders(
    walletId: UUID
    pair: String
    status: OrderStatus
    pagination: PaginationInput
  ): [Order!]!
  
  # Transactions
  transaction(id: UUID!): Transaction
  transactions(
    walletId: UUID
    type: TransactionType
    currency: CurrencyType
    timeRange: TimeRangeInput
    pagination: PaginationInput
  ): TransactionConnection!
  
  # Analytics
  marketEfficiency(
    period: String = "day"
    interval: String
  ): EfficiencyMetrics! @rateLimit(max: 100, window: "minute")
  
  economicHealth: EconomicHealth!
  
  teamAnalysis(
    agentIds: [UUID!]!
    contractType: String
  ): TeamAnalysis!
  
  contractAnalytics(
    timeRange: TimeRangeInput
    groupBy: String
  ): ContractAnalytics!
  
  # Governance
  proposal(id: UUID!): Proposal
  proposals(
    status: ProposalStatus
    type: ProposalType
    proposerId: UUID
    pagination: PaginationInput
  ): [Proposal!]!
  
  votingPower(userId: UUID!): VotingPowerBreakdown!
  
  # Risk
  riskAssessment(input: RiskAssessmentInput!): RiskAssessment!
  
  systemRisks(
    severity: RiskLevel
    pagination: PaginationInput
  ): [SystemRisk!]!
  
  economicAttackDetection(
    windowMinutes: Int = 60
  ): AttackDetection!
  
  # System
  systemHealth: SystemHealth!
  
  economicLaws: EconomicLawsInfo!
  
  platformStatistics(
    timeRange: TimeRangeInput
  ): PlatformStatistics!
}

# Additional types for queries
input TeamConstraints {
  maxTeamSize: Int = 5
  minReliability: Decimal = 0.7
  requiredCapabilities: [String!]
  budgetLimit: MultiCurrencyAmountInput
}

enum RankingMetric {
  RELIABILITY
  QUALITY
  EARNINGS
  INNOVATION
  SYNERGY
  EFFICIENCY
}

type RankedAgent {
  rank: Int!
  previousRank: Int
  agent: Agent!
  score: Decimal!
  trend: String!
}

type MarketPair {
  pair: String!
  baseCurrency: CurrencyType!
  quoteCurrency: CurrencyType!
  active: Boolean!
  minOrderSize: Decimal!
  pricePrecision: Int!
  volumePrecision: Int!
}

type ContractAnalytics {
  totalContracts: Int!
  totalValue: MultiCurrencyAmount!
  averageValue: MultiCurrencyAmount!
  completionRate: Decimal!
  averageDeliveryTime: Int!
  efficiencyTrend: [EfficiencyPoint!]!
  topTags: [TagCount!]!
}

type VotingPowerBreakdown {
  baseVotingPower: Decimal!
  reliabilityBonus: Decimal!
  stakingBonus: Decimal!
  participationBonus: Decimal!
  totalVotingPower: Decimal!
}

type SystemRisk {
  id: UUID!
  type: String!
  severity: RiskLevel!
  affectedEntities: Int!
  potentialImpact: MultiCurrencyAmount!
  mitigationStatus: String!
  detectedAt: DateTime!
}

type AttackDetection {
  attackDetected: Boolean!
  attacks: [EconomicAttack!]!
  systemHealth: Decimal!
  recommendations: [String!]!
}

type EconomicAttack {
  id: UUID!
  type: String!
  confidence: Decimal!
  involvedEntities: [UUID!]!
  estimatedDamage: MultiCurrencyAmount!
  status: String!
  detectedAt: DateTime!
}

type EconomicLawsInfo {
  laws: [EconomicLawInfo!]!
  overallCompliance: Decimal!
  enforcementActive: Boolean!
}

type EconomicLawInfo {
  law: EconomicLaw!
  description: String!
  importance: String!
  currentCompliance: Decimal!
  violations24h: Int!
}

type PlatformStatistics {
  users: UserStatistics!
  agents: AgentStatistics!
  contracts: ContractStatistics!
  market: MarketStatistics!
  economics: EconomicStatistics!
}

type UserStatistics {
  total: Int!
  active24h: Int!
  new7d: Int!
  byRole: JSON!
}

type AgentStatistics {
  total: Int!
  active: Int!
  byType: JSON!
  averageReliability: Decimal!
}

type ContractStatistics {
  total: Int!
  active: Int!
  completed7d: Int!
  totalVolume: MultiCurrencyAmount!
  averageValue: MultiCurrencyAmount!
}

type MarketStatistics {
  totalVolume24h: MultiCurrencyAmount!
  totalTransactions24h: Int!
  averageTransactionSize: MultiCurrencyAmount!
  mostTradedPair: String!
}

type EconomicStatistics {
  totalValueLocked: MultiCurrencyAmount!
  circulatingSupply: MultiCurrencyAmount!
  marketCap: Decimal!
  velocity: Decimal!
}

# =====================================================
# MUTATION ROOT
# =====================================================

type Mutation {
  # Organizations
  createOrganization(input: CreateOrganizationInput!): Organization! @auth(requires: ADMIN)
  updateOrganization(id: UUID!, input: UpdateOrganizationInput!): Organization! @auth(requires: ADMIN)
  deleteOrganization(id: UUID!): Boolean! @auth(requires: OWNER)
  
  # Users
  inviteUser(email: String!, role: UserRole!, organizationId: UUID!): User! @auth(requires: ADMIN)
  updateUserRole(userId: UUID!, role: UserRole!): User! @auth(requires: ADMIN)
  removeUser(userId: UUID!): Boolean! @auth(requires: ADMIN)
  
  # Contracts
  createContract(input: CreateContractInput!): Contract! @economicValidation
  updateContract(id: UUID!, input: UpdateContractInput!): Contract!
  updateContractStatus(id: UUID!, status: ContractStatus!): Contract!
  selectBid(contractId: UUID!, bidId: UUID!, reason: String): Contract! @economicValidation
  completeContract(
    id: UUID!
    deliverables: JSON!
    qualityScore: Decimal!
    feedback: String
  ): Contract! @economicValidation
  disputeContract(id: UUID!, reason: String!): Contract!
  
  # Bids
  submitBid(input: SubmitBidInput!): Bid! @economicValidation
  updateBid(id: UUID!, proposal: String!, pricing: MultiCurrencyAmountInput!): Bid!
  withdrawBid(id: UUID!, reason: String): Bid!
  
  # Teams
  createTeam(input: CreateTeamInput!): Team!
  updateTeam(id: UUID!, name: String, purpose: String): Team!
  addTeamMember(teamId: UUID!, agentId: UUID!, role: String!): Team!
  removeTeamMember(teamId: UUID!, agentId: UUID!): Team!
  disbandTeam(id: UUID!): Team!
  
  # Agents
  registerAgent(input: RegisterAgentInput!): Agent!
  updateAgent(id: UUID!, input: UpdateAgentInput!): Agent!
  suspendAgent(id: UUID!, reason: String!): Agent!
  reactivateAgent(id: UUID!): Agent!
  
  # Wallets
  transferCurrency(input: TransferCurrencyInput!): Transaction! @economicValidation
  batchTransfer(input: BatchTransferInput!): BatchTransferResult! @economicValidation
  exchangeCurrency(input: ExchangeCurrencyInput!): Transaction! @economicValidation
  claimYield(walletId: UUID!): Transaction! @economicValidation
  
  # Markets
  createOrder(input: CreateOrderInput!): Order! @economicValidation
  cancelOrder(id: UUID!): Order!
  
  # Governance
  createProposal(input: CreateProposalInput!): Proposal! @auth(requires: MEMBER)
  updateProposal(id: UUID!, description: String!): Proposal!
  submitProposalForVoting(id: UUID!): Proposal!
  vote(proposalId: UUID!, vote: VoteType!, reason: String): Vote! @auth(requires: MEMBER)
  executeProposal(id: UUID!): Proposal! @auth(requires: ADMIN)
  
  # Risk Management
  reportRisk(
    entityId: UUID!
    entityType: String!
    riskType: String!
    description: String!
  ): RiskReport!
  
  mitigateRisk(
    riskId: UUID!
    action: String!
  ): RiskMitigation!
  
  # System Administration
  triggerTemporalDecay: SystemOperation! @auth(requires: ADMIN)
  triggerYieldGeneration: SystemOperation! @auth(requires: ADMIN)
  recalculateEfficiency: SystemOperation! @auth(requires: ADMIN)
  enforceEconomicLaws: SystemOperation! @auth(requires: ADMIN)
}

# Additional mutation types
type BatchTransferResult {
  batchId: UUID!
  transfers: [TransferResult!]!
  success: Boolean!
  totalTransferred: MultiCurrencyAmount!
  economicValidation: EconomicValidation!
}

type TransferResult {
  index: Int!
  transaction: Transaction
  success: Boolean!
  error: String
}

type RiskReport {
  id: UUID!
  reporter: User!
  entityId: UUID!
  entityType: String!
  riskType: String!
  description: String!
  status: String!
  createdAt: DateTime!
}

type RiskMitigation {
  id: UUID!
  risk: SystemRisk!
  action: String!
  executor: User!
  status: String!
  result: String
  executedAt: DateTime!
}

type SystemOperation {
  id: UUID!
  operation: String!
  status: String!
  affectedEntities: Int!
  result: JSON!
  executedBy: User!
  executedAt: DateTime!
}

# =====================================================
# SUBSCRIPTION ROOT
# =====================================================

type Subscription {
  # Contract updates
  contractCreated(organizationId: UUID): Contract!
  contractUpdated(id: UUID!): Contract!
  contractStatusChanged(id: UUID!): ContractStatusUpdate!
  contractBids(contractId: UUID!): Bid!
  
  # Bid updates
  bidSubmitted(contractId: UUID!): Bid!
  bidUpdated(id: UUID!): Bid!
  bidSelected(contractId: UUID!): BidSelection!
  
  # Team updates
  teamFormed(agentId: UUID): Team!
  teamSynergyChanged(teamId: UUID!): SynergyUpdate!
  teamDisbanded(teamId: UUID!): Team!
  
  # Market data
  priceUpdates(pair: String!): PriceUpdate!
  orderBookUpdates(pair: String!, depth: Int = 20): OrderBook!
  trades(pair: String): Trade!
  marketEfficiencyUpdate: EfficiencyUpdate!
  
  # Wallet updates
  walletBalanceChanged(walletId: UUID!): WalletUpdate!
  transactionCreated(walletId: UUID!): Transaction!
  yieldGenerated(walletId: UUID!): YieldUpdate!
  
  # Order updates
  orderUpdated(orderId: UUID!): Order!
  orderFilled(walletId: UUID): OrderFillUpdate!
  
  # System events
  economicViolation: EconomicViolation!
  systemAlert: SystemAlert!
  riskAlert(severity: RiskLevel): RiskAlert!
  
  # Governance
  proposalCreated: Proposal!
  proposalStatusChanged(id: UUID!): ProposalStatusUpdate!
  voteReceived(proposalId: UUID!): Vote!
  
  # Innovation
  innovationSubmitted: Innovation!
  synergyMilestone(threshold: Decimal = 150): SynergyMilestone!
}

# Subscription-specific types
type ContractStatusUpdate {
  contract: Contract!
  previousStatus: ContractStatus!
  newStatus: ContractStatus!
  reason: String
  timestamp: DateTime!
}

type BidSelection {
  contract: Contract!
  selectedBid: Bid!
  runnerUpBids: [Bid!]!
  selectionReason: String
  efficiencyImpact: Decimal!
}

type SynergyUpdate {
  team: Team!
  previousSynergy: Decimal!
  newSynergy: Decimal!
  breakdown: SynergyBreakdown!
  trigger: String!
}

type PriceUpdate {
  pair: String!
  price: Decimal!
  previousPrice: Decimal!
  volume: Decimal!
  changePercent: Decimal!
  timestamp: DateTime!
}

type Trade {
  id: UUID!
  pair: String!
  price: Decimal!
  quantity: Decimal!
  side: OrderSide!
  maker: Order!
  taker: Order!
  fee: MultiCurrencyAmount!
  timestamp: DateTime!
}

type EfficiencyUpdate {
  current: Decimal!
  previous: Decimal!
  target: Decimal!
  components: EfficiencyComponents!
  trend: String!
  timestamp: DateTime!
}

type WalletUpdate {
  wallet: Wallet!
  changes: MultiCurrencyAmount!
  trigger: String!
  transaction: Transaction
  timestamp: DateTime!
}

type YieldUpdate {
  wallet: Wallet!
  yieldAmount: Decimal!
  reliabilityScore: Decimal!
  period: Int!
  transaction: Transaction!
}

type OrderFillUpdate {
  order: Order!
  trade: Trade!
  remainingQuantity: Decimal!
  status: OrderStatus!
}

type SystemAlert {
  id: UUID!
  type: String!
  severity: String!
  message: String!
  affectedServices: [String!]!
  autoResolved: Boolean!
  timestamp: DateTime!
}

type RiskAlert {
  id: UUID!
  entityId: UUID!
  entityType: String!
  riskType: String!
  severity: RiskLevel!
  score: Decimal!
  message: String!
  recommendedActions: [String!]!
  timestamp: DateTime!
}

type ProposalStatusUpdate {
  proposal: Proposal!
  previousStatus: ProposalStatus!
  newStatus: ProposalStatus!
  timestamp: DateTime!
}

type SynergyMilestone {
  team: Team!
  milestone: Decimal!
  reward: MultiCurrencyAmount!
  achievement: String!
  timestamp: DateTime!
}

# =====================================================
# CUSTOM ENUMS FOR COMPLEX TYPES
# =====================================================

enum BidOrderBy {
  PRICE_ASC
  PRICE_DESC
  SYNERGY_DESC
  QUALITY_DESC
  TIME_ASC
  CREATED_AT_DESC
}