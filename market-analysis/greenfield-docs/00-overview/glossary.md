# Glossary

This glossary defines key technical and economic terms used throughout the VibeLaunch documentation.

## Technical Terms

| Term | Definition |
| --- | --- |
| **Agent Registry** | A dynamic, in-memory service that maintains a directory of all available AI agents, their capabilities, and their online status. It allows the Master Agent to discover and invoke agents based on task requirements. |
| **`bus_notify`** | A PostgreSQL RPC function that acts as a unified entry point for publishing events. It is part of an effort to consolidate a fragmented event system. |
| **Event-Driven Architecture** | A software architecture pattern where the flow of the system is determined by events, such as user actions, sensor outputs, or messages from other services. |
| **Master Agent** | The central orchestrator of the AI Agent System. It processes user requests, queries the Agent Registry to find suitable agents, and coordinates task execution. |
| **MCP (Master Control Protocol)** | The communication layer that enables the Master Agent to send commands (e.g., `tool_call`) and receive results from specialized agents. |
| **Multi-Tenant Architecture** | An architecture where a single instance of the software serves multiple customers (tenants). Each tenant's data is isolated and remains invisible to other tenants. |
| **RLS (Row-Level Security)** | A PostgreSQL feature that controls which rows a user can access in a table. In VibeLaunch, it is the primary mechanism for enforcing tenant data isolation. |
| **`service_role`** | A high-privilege role in Supabase that can bypass Row-Level Security. Its misuse is a critical security vulnerability in the current system. |
| **Supabase Realtime** | A service that allows broadcasting real-time messages to subscribed clients. It is the primary mechanism for pushing events from the backend to the UI. |
| **Worker Queue** | A system for managing background tasks. The VibeLaunch documentation describes an aspirational worker queue system that is not yet implemented. |

## Economic Terms

| Term | Definition |
| --- | --- |
| **Clarity (◈)** | A currency representing the quality, accuracy, and informational value of a product or service. |
| **Collateralized Task Obligations (CTOs)** | Structured financial products where payments are derived from the successful completion of a pool of tasks. |
| **Flow (⧗)** | A currency that captures the temporal value of work, including urgency, project velocity, and opportunity cost. |
| **Futarchy** | A form of governance where decisions are made by betting on the outcome of policies in prediction markets. |
| **Merit (₥)** | The primary economic currency, used as a unit of account, medium of exchange, and store of value. |
| **Multi-Dimensional Value** | A core principle recognizing that value is not purely monetary and can be expressed across multiple dimensions (e.g., quality, time, trust, innovation). |
| **Shapley Value** | A game theory concept used to fairly distribute the "payout" of a collaborative effort among the contributors. |
| **Spark (◊)** | A currency that rewards innovation, creativity, discovery, and paradigm-shifting contributions. |
| **Steadfast (☆)** | A non-transferable currency representing reputation, reliability, and trustworthiness. It can be used as collateral and generates yield. |
| **Synergy Discovery Markets** | Markets designed to identify and foster high-performing teams and collaborations that can create supra-additive value. |
| **VCG (Vickrey-Clarke-Groves) Mechanism** | An incentive-compatible mechanism used to encourage truthful reporting and revelation of information from participants. |
