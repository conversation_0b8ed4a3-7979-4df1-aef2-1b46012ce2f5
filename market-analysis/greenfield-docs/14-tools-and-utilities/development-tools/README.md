# Development Tools

## Purpose
Essential development tools for working with VibeLaunch's revolutionary five-dimensional currency system and economic laws.

## Available Tools

### 1. Currency Calculator (`currency-calculator/`)
Interactive CLI and library for multi-dimensional currency calculations
- Exchange rates between all 5 currencies
- Team synergy calculations (194.4% target)
- Temporal decay and reliability yield
- Conservation law validation

**Usage:**
```bash
cd currency-calculator
npm install
npm run dev
```

### 2. Economic Law Validator (`economic-validator/`)
Validates transactions against the four fundamental economic laws
- Value Conservation validation
- Information Entropy checking
- Collaborative Advantage verification
- Reputation Accumulation rules

**Usage:**
```bash
cd economic-validator
npm install
npm run dev
```

### 3. Code Generators (`code-generators/`)
Templates and generators for VibeLaunch services
- TypeScript service template with economic law enforcement
- Multi-currency support built-in
- Event-driven architecture

### 4. Setup Scripts (`setup-scripts/`)
Environment setup and configuration tools
- Docker compose configurations
- Development environment setup

### 5. Validation Tools (`validation-tools/`)
Additional validation utilities for development

## Quick Start

1. **Install a tool globally:**
```bash
cd currency-calculator
npm install -g .
vibelaunch-calc --help
```

2. **Use programmatically:**
```typescript
import { CurrencyCalculator } from '@vibelaunch/currency-calculator';
import { EconomicValidator } from '@vibelaunch/economic-validator';
```

## Key Concepts

### Five-Dimensional Currency System
- **Economic (₥)**: Traditional monetary value
- **Quality (◈)**: Technical excellence (0-2 range)
- **Temporal (⧗)**: Time efficiency (decays over time)
- **Reliability (☆)**: Trust and consistency (0-1 range)
- **Innovation (◊)**: Creative solutions

### Four Economic Laws
1. **Value Conservation**: Total value preserved
2. **Information Entropy**: Decreases through aggregation
3. **Collaborative Advantage**: Teams exceed individuals (194.4%)
4. **Reputation Accumulation**: Performance-based changes only

## Audience
- VibeLaunch developers
- Smart contract engineers
- DevOps teams
- QA engineers

## Navigation
- Parent: [14-tools-and-utilities](../) - Tools
- Main: [Documentation Home](../../README.md)
