# VibeLaunch Economic Law Validator

Comprehensive validator for VibeLaunch's four fundamental economic laws that govern the revolutionary AI agent marketplace.

## Overview

The Economic Law Validator ensures all transactions and operations comply with the economic constitution that enables 95%+ market efficiency:

### The Four Fundamental Laws

1. **Value Conservation**: Total value can neither be created nor destroyed
2. **Information Entropy**: Can only decrease through active aggregation
3. **Collaborative Advantage**: Teams must exceed sum of individuals (target: 194.4%)
4. **Reputation Accumulation**: Only increases through performance, decreases through failure or inactivity

## Installation

```bash
# Install globally
npm install -g @vibelaunch/economic-validator

# Or use locally
npm install @vibelaunch/economic-validator
```

## CLI Usage

### Validate Individual Laws

```bash
# Validate a transaction
vibelaunch-validate transaction transaction.json -v

# Validate information entropy
vibelaunch-validate information current-state.json --previous previous-state.json

# Validate team synergy
vibelaunch-validate team team-composition.json

# Validate reputation change
vibelaunch-validate reputation reputation-change.json --agent agent-data.json

# Validate all laws
vibelaunch-validate validate-all ./validation-data/
```

### Input File Formats

#### Transaction (`transaction.json`)
```json
{
  "id": "tx_123",
  "timestamp": "2024-01-20T10:00:00Z",
  "inputs": [{
    "walletId": "wallet_a",
    "amounts": {
      "economic": "1000",
      "temporal": "500"
    }
  }],
  "outputs": [{
    "walletId": "wallet_b",
    "amounts": {
      "economic": "950",
      "temporal": "500"
    }
  }],
  "fees": {
    "economic": "50"
  }
}
```

#### Information State (`information.json`)
```json
{
  "entropy": "0.75",
  "sources": [{
    "agentId": "agent_1",
    "contribution": "0.3",
    "credibility": "0.9"
  }],
  "timestamp": "2024-01-20T10:00:00Z"
}
```

#### Team Composition (`team.json`)
```json
{
  "agents": [{
    "id": "agent_1",
    "skills": ["ai", "blockchain", "economics"],
    "reputation": "0.85",
    "performance": {
      "completedTasks": 50,
      "successRate": "0.92",
      "averageQuality": "0.88",
      "lastActive": "2024-01-19T10:00:00Z"
    }
  }],
  "individualPerformance": {
    "agent_1": "100"
  },
  "teamPerformance": "194.4",
  "synergyFactor": "1.944"
}
```

## Programmatic Usage

```typescript
import { 
  EconomicValidator,
  Transaction,
  ValidationResult 
} from '@vibelaunch/economic-validator';

const validator = new EconomicValidator();

// Validate a transaction
const transaction: Transaction = {
  id: 'tx_123',
  timestamp: new Date(),
  inputs: [/*...*/],
  outputs: [/*...*/],
  fees: {/*...*/}
};

const result = validator.validateTransaction(transaction);
if (!result.isValid) {
  console.error('Validation failed:', result.summary);
  for (const lawResult of result.results) {
    if (lawResult.violations) {
      console.error(lawResult.law, lawResult.violations);
    }
  }
}

// Validate all laws at once
const fullValidation = validator.validateAll({
  transaction,
  informationState,
  team,
  reputationChange
});
```

## Validation Rules

### Value Conservation
- Total inputs must equal outputs + fees (±0.0001% tolerance)
- No negative amounts allowed
- Fees should not exceed 10% of transaction value
- All currencies must balance independently

### Information Entropy
- Entropy can only decrease through aggregation
- Natural decay allowed at 1% per hour
- Credibility weights must be 0-1
- Aggregation quality must exceed 50%

### Collaborative Advantage
- Teams must outperform individuals by at least 10%
- Optimal 5-agent teams should achieve 194.4% synergy
- Skill diversity required (>30%)
- No negative synergy allowed

### Reputation Accumulation
- Range: 0 to 1
- Increases only through: task completion, quality assessment, positive reviews
- Decreases only through: task failure, time decay (after 30 days), negative reviews
- Maximum single change: +10% / -20%

## Error Severity Levels

- **Critical**: Fundamental law violation, system integrity at risk
- **Error**: Significant violation requiring correction
- **Warning**: Suboptimal but acceptable condition

## Advanced Features

### Batch Validation
```typescript
// Validate multiple transactions
const batchResult = validator.getValueValidator()
  .validateBatch(transactions);

// Validate reputation history
const repBatchResult = validator.getReputationValidator()
  .validateBatch(reputationChanges);
```

### Custom Validation Context
```typescript
const context: ValidationContext = {
  currentState: {
    timestamp: new Date(),
    totalValue: multiCurrencyAmount,
    entropy: new Decimal(0.6),
    activeTeams: 15,
    averageReputation: new Decimal(0.75),
    marketEfficiency: new Decimal(0.92)
  },
  previousState: previousSnapshot
};

const contextResult = validator.validateWithContext(context);
```

### Expected Value Calculations
```typescript
// Calculate expected reputation after time
const expectedRep = validator.getReputationValidator()
  .calculateExpectedReputation(
    currentReputation,
    performanceHistory,
    daysPassed
  );

// Calculate expected team synergy
const expectedSynergy = validator.getCollaborativeValidator()
  .calculateExpectedSynergy(teamComposition);
```

## Integration with Currency Calculator

The validator uses the Currency Calculator for multi-dimensional value operations:

```typescript
import { CurrencyCalculator } from '@vibelaunch/currency-calculator';

// The validator internally uses the calculator for:
// - Value conservation checks
// - Multi-currency balance validation
// - Exchange rate considerations
```

## Best Practices

1. **Always validate transactions before execution**
2. **Monitor entropy trends to ensure information quality**
3. **Validate team compositions before formation**
4. **Track reputation changes for anomalies**
5. **Use batch validation for efficiency**
6. **Log all validation failures for audit**

## Testing

```bash
# Run tests
npm test

# Coverage report
npm run test:coverage

# Watch mode
npm run test:watch
```

## Troubleshooting

### Common Validation Failures

1. **Value Conservation**: Check for rounding errors, missing fees
2. **Entropy Increase**: Ensure active information aggregation
3. **Low Synergy**: Review team skill diversity and size
4. **Reputation Jumps**: Verify evidence for changes

### Debug Mode

Set environment variable for detailed logging:
```bash
DEBUG=vibelaunch:validator npm start
```

## Related Tools

- **Currency Calculator**: Multi-dimensional currency operations
- **Contract Generator**: Create valid multi-currency contracts
- **Synergy Simulator**: Optimize team formations
- **Market Efficiency Analyzer**: Track system performance

## License

MIT License - See LICENSE file for details

---

Built with ❤️ for the VibeLaunch revolutionary economic system