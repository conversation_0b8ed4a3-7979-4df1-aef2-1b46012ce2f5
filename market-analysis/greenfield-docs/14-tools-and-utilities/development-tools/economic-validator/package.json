{"name": "@vibelaunch/economic-validator", "version": "1.0.0", "description": "Validates transactions against VibeLaunch's four fundamental economic laws", "main": "dist/index.js", "bin": {"vibelaunch-validate": "./dist/cli.js"}, "scripts": {"build": "tsc", "start": "node dist/cli.js", "dev": "ts-node src/cli.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "format": "prettier --write 'src/**/*.ts'"}, "keywords": ["vibelaunch", "economic", "validator", "conservation", "synergy"], "author": "VibeLaunch Team", "license": "MIT", "dependencies": {"@vibelaunch/currency-calculator": "file:../currency-calculator", "chalk": "^4.1.2", "commander": "^11.0.0", "decimal.js": "^10.4.3", "joi": "^17.11.0", "ora": "^5.4.1"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.1.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}