#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { readFileSync } from 'fs';
import { Decimal } from 'decimal.js';
import { EconomicValidator } from './lib/economic-validator';
import { 
  Transaction, 
  InformationState, 
  TeamComposition, 
  ReputationChange,
  ValidationResult,
  Violation
} from './types/validation';

const validator = new EconomicValidator();
const program = new Command();

program
  .name('vibelaunch-validate')
  .description('Validate transactions against VibeLaunch\'s four fundamental economic laws')
  .version('1.0.0');

// Validate transaction command
program
  .command('transaction <file>')
  .description('Validate a transaction against value conservation law')
  .option('-v, --verbose', 'Show detailed validation results')
  .action(async (file: string, options) => {
    const spinner = ora('Loading transaction...').start();
    
    try {
      const data = JSON.parse(readFileSync(file, 'utf-8'));
      const transaction = parseTransaction(data);
      
      spinner.text = 'Validating transaction...';
      const result = validator.validateTransaction(transaction);
      
      spinner.stop();
      displayValidationResult(result, options.verbose);
    } catch (error: any) {
      spinner.fail('Validation failed: ' + error.message);
      process.exit(1);
    }
  });

// Validate information state command
program
  .command('information <file>')
  .description('Validate information state against entropy law')
  .option('-p, --previous <file>', 'Previous state file for comparison')
  .option('-v, --verbose', 'Show detailed validation results')
  .action(async (file: string, options) => {
    const spinner = ora('Loading information state...').start();
    
    try {
      const currentData = JSON.parse(readFileSync(file, 'utf-8'));
      const currentState = parseInformationState(currentData);
      
      let previousState;
      if (options.previous) {
        const previousData = JSON.parse(readFileSync(options.previous, 'utf-8'));
        previousState = parseInformationState(previousData);
      }
      
      spinner.text = 'Validating information entropy...';
      const result = validator.validateInformation(currentState, previousState);
      
      spinner.stop();
      displayValidationResult(result, options.verbose);
    } catch (error: any) {
      spinner.fail('Validation failed: ' + error.message);
      process.exit(1);
    }
  });

// Validate team command
program
  .command('team <file>')
  .description('Validate team composition against collaborative advantage law')
  .option('-v, --verbose', 'Show detailed validation results')
  .action(async (file: string, options) => {
    const spinner = ora('Loading team composition...').start();
    
    try {
      const data = JSON.parse(readFileSync(file, 'utf-8'));
      const team = parseTeamComposition(data);
      
      spinner.text = 'Validating collaborative advantage...';
      const result = validator.validateTeam(team);
      
      spinner.stop();
      displayValidationResult(result, options.verbose);
      
      // Show synergy achievement
      const synergy = team.synergyFactor.mul(100);
      console.log(chalk.bold('\nTeam Synergy: ') + 
        (synergy.gte(194.4) ? chalk.green(synergy.toFixed(1) + '%') : chalk.yellow(synergy.toFixed(1) + '%')));
    } catch (error: any) {
      spinner.fail('Validation failed: ' + error.message);
      process.exit(1);
    }
  });

// Validate reputation command
program
  .command('reputation <file>')
  .description('Validate reputation change against accumulation law')
  .option('-a, --agent <file>', 'Agent data file for context')
  .option('-v, --verbose', 'Show detailed validation results')
  .action(async (file: string, options) => {
    const spinner = ora('Loading reputation change...').start();
    
    try {
      const data = JSON.parse(readFileSync(file, 'utf-8'));
      const change = parseReputationChange(data);
      
      let agent;
      if (options.agent) {
        const agentData = JSON.parse(readFileSync(options.agent, 'utf-8'));
        agent = parseAgent(agentData);
      }
      
      spinner.text = 'Validating reputation accumulation...';
      const result = validator.validateReputation(change, agent);
      
      spinner.stop();
      displayValidationResult(result, options.verbose);
    } catch (error: any) {
      spinner.fail('Validation failed: ' + error.message);
      process.exit(1);
    }
  });

// Validate all command
program
  .command('validate-all <directory>')
  .description('Validate all economic laws with complete context')
  .option('-v, --verbose', 'Show detailed validation results')
  .action(async (directory: string, options) => {
    const spinner = ora('Loading validation data...').start();
    
    try {
      // Load all available data
      const params: any = {};
      
      try {
        const txData = JSON.parse(readFileSync(`${directory}/transaction.json`, 'utf-8'));
        params.transaction = parseTransaction(txData);
      } catch {}
      
      try {
        const infoData = JSON.parse(readFileSync(`${directory}/information.json`, 'utf-8'));
        params.informationState = parseInformationState(infoData);
      } catch {}
      
      try {
        const teamData = JSON.parse(readFileSync(`${directory}/team.json`, 'utf-8'));
        params.team = parseTeamComposition(teamData);
      } catch {}
      
      try {
        const repData = JSON.parse(readFileSync(`${directory}/reputation.json`, 'utf-8'));
        params.reputationChange = parseReputationChange(repData);
      } catch {}
      
      spinner.text = 'Validating all economic laws...';
      const result = validator.validateAll(params);
      
      spinner.stop();
      displayValidationResult(result, options.verbose);
    } catch (error: any) {
      spinner.fail('Validation failed: ' + error.message);
      process.exit(1);
    }
  });

// Helper functions
function parseTransaction(data: any): Transaction {
  return {
    id: data.id,
    timestamp: new Date(data.timestamp),
    inputs: data.inputs.map((input: any) => ({
      walletId: input.walletId,
      amounts: {
        economic: new Decimal(input.amounts.economic || 0),
        quality: new Decimal(input.amounts.quality || 0),
        temporal: new Decimal(input.amounts.temporal || 0),
        reliability: new Decimal(input.amounts.reliability || 0),
        innovation: new Decimal(input.amounts.innovation || 0)
      }
    })),
    outputs: data.outputs.map((output: any) => ({
      walletId: output.walletId,
      amounts: {
        economic: new Decimal(output.amounts.economic || 0),
        quality: new Decimal(output.amounts.quality || 0),
        temporal: new Decimal(output.amounts.temporal || 0),
        reliability: new Decimal(output.amounts.reliability || 0),
        innovation: new Decimal(output.amounts.innovation || 0)
      }
    })),
    fees: {
      economic: new Decimal(data.fees?.economic || 0),
      quality: new Decimal(data.fees?.quality || 0),
      temporal: new Decimal(data.fees?.temporal || 0),
      reliability: new Decimal(data.fees?.reliability || 0),
      innovation: new Decimal(data.fees?.innovation || 0)
    },
    metadata: data.metadata
  };
}

function parseInformationState(data: any): InformationState {
  return {
    entropy: new Decimal(data.entropy),
    sources: data.sources.map((source: any) => ({
      agentId: source.agentId,
      contribution: new Decimal(source.contribution),
      credibility: new Decimal(source.credibility)
    })),
    timestamp: new Date(data.timestamp)
  };
}

function parseTeamComposition(data: any): TeamComposition {
  return {
    agents: data.agents.map((agent: any) => ({
      id: agent.id,
      skills: agent.skills,
      reputation: new Decimal(agent.reputation),
      performance: {
        completedTasks: agent.performance.completedTasks,
        successRate: new Decimal(agent.performance.successRate),
        averageQuality: new Decimal(agent.performance.averageQuality),
        lastActive: new Date(agent.performance.lastActive)
      }
    })),
    individualPerformance: Object.entries(data.individualPerformance).reduce((acc, [id, perf]) => {
      acc[id] = new Decimal(perf as any);
      return acc;
    }, {} as Record<string, Decimal>),
    teamPerformance: new Decimal(data.teamPerformance),
    synergyFactor: new Decimal(data.synergyFactor)
  };
}

function parseReputationChange(data: any): ReputationChange {
  return {
    agentId: data.agentId,
    previousReputation: new Decimal(data.previousReputation),
    newReputation: new Decimal(data.newReputation),
    reason: data.reason,
    evidence: data.evidence,
    timestamp: new Date(data.timestamp)
  };
}

function parseAgent(data: any): any {
  return {
    id: data.id,
    skills: data.skills,
    reputation: new Decimal(data.reputation),
    performance: {
      completedTasks: data.performance.completedTasks,
      successRate: new Decimal(data.performance.successRate),
      averageQuality: new Decimal(data.performance.averageQuality),
      lastActive: new Date(data.performance.lastActive)
    }
  };
}

function displayValidationResult(result: ValidationResult, verbose: boolean): void {
  console.log('\n' + chalk.bold.underline('Validation Result'));
  console.log(chalk.bold('Status: ') + (result.isValid ? chalk.green('✓ VALID') : chalk.red('✗ INVALID')));
  console.log(chalk.bold('Summary: ') + result.summary);
  
  for (const lawResult of result.results) {
    console.log('\n' + chalk.bold(lawResult.law));
    console.log('  ' + (lawResult.isValid ? chalk.green('✓') : chalk.red('✗')) + ' ' + lawResult.details);
    
    if (verbose && lawResult.violations) {
      console.log(chalk.dim('  Violations:'));
      for (const violation of lawResult.violations) {
        displayViolation(violation);
      }
    }
    
    if (lawResult.suggestions && lawResult.suggestions.length > 0) {
      console.log(chalk.dim('  Suggestions:'));
      for (const suggestion of lawResult.suggestions) {
        console.log('    • ' + suggestion);
      }
    }
  }
  
  if (!result.isValid) {
    process.exit(1);
  }
}

function displayViolation(violation: Violation): void {
  const severityColor = {
    critical: chalk.red,
    error: chalk.yellow,
    warning: chalk.cyan
  };
  
  const color = severityColor[violation.severity];
  console.log(`    ${color('●')} [${violation.severity}] ${violation.description}`);
  
  if (violation.data) {
    console.log(chalk.dim('      Data: ') + JSON.stringify(violation.data, null, 2).split('\n').join('\n      '));
  }
}

// Parse command line arguments
program.parse(process.argv);

// If no command specified, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}