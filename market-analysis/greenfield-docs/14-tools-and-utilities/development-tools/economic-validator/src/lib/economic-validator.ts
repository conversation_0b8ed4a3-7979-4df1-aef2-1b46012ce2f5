import { 
  ValidationResult, 
  ValidationContext,
  Transaction,
  InformationState,
  TeamComposition,
  ReputationChange,
  Agent,
  EconomicLaw
} from '../types/validation';
import { ValueConservationValidator } from './validators/value-conservation';
import { InformationEntropyValidator } from './validators/information-entropy';
import { CollaborativeAdvantageValidator } from './validators/collaborative-advantage';
import { ReputationAccumulationValidator } from './validators/reputation-accumulation';

/**
 * Main economic law validator that orchestrates all four law validators
 */
export class EconomicValidator {
  private valueValidator: ValueConservationValidator;
  private entropyValidator: InformationEntropyValidator;
  private collaborativeValidator: CollaborativeAdvantageValidator;
  private reputationValidator: ReputationAccumulationValidator;

  constructor() {
    this.valueValidator = new ValueConservationValidator();
    this.entropyValidator = new InformationEntropyValidator();
    this.collaborativeValidator = new CollaborativeAdvantageValidator();
    this.reputationValidator = new ReputationAccumulationValidator();
  }

  /**
   * Validate a transaction against all applicable economic laws
   */
  validateTransaction(transaction: Transaction): ValidationResult {
    const results = [];

    // Always validate value conservation for transactions
    results.push(this.valueValidator.validate(transaction));

    return this.createValidationResult(results);
  }

  /**
   * Validate information state changes
   */
  validateInformation(
    currentState: InformationState, 
    previousState?: InformationState
  ): ValidationResult {
    const results = [];

    results.push(this.entropyValidator.validate(currentState, previousState));

    return this.createValidationResult(results);
  }

  /**
   * Validate team composition and synergy
   */
  validateTeam(team: TeamComposition): ValidationResult {
    const results = [];

    results.push(this.collaborativeValidator.validate(team));

    return this.createValidationResult(results);
  }

  /**
   * Validate reputation change
   */
  validateReputation(change: ReputationChange, agent?: Agent): ValidationResult {
    const results = [];

    results.push(this.reputationValidator.validate(change, agent));

    return this.createValidationResult(results);
  }

  /**
   * Comprehensive validation with full context
   */
  validateWithContext(context: ValidationContext): ValidationResult {
    const results = [];
    const currentState = context.currentState;
    const previousState = context.previousState;

    // Check if we need to validate value conservation
    if (previousState && !currentState.totalValue.economic.eq(previousState.totalValue.economic)) {
      // Total value changed, need to validate transaction
      results.push({
        law: EconomicLaw.VALUE_CONSERVATION,
        isValid: false,
        details: 'System state shows value change without validated transaction',
        violations: [{
          type: 'unaccounted_value_change',
          severity: 'critical' as const,
          description: 'Total system value changed without proper transaction validation'
        }]
      });
    }

    // Validate entropy changes
    if (previousState) {
      const infoState: InformationState = {
        entropy: currentState.entropy,
        sources: [], // Would need to be provided
        timestamp: currentState.timestamp
      };
      const prevInfoState: InformationState = {
        entropy: previousState.entropy,
        sources: [],
        timestamp: previousState.timestamp
      };
      
      results.push(this.entropyValidator.validate(infoState, prevInfoState));
    }

    // Check market efficiency trend
    if (previousState && currentState.marketEfficiency.lt(previousState.marketEfficiency)) {
      results.push({
        law: EconomicLaw.INFORMATION_ENTROPY,
        isValid: false,
        details: 'Market efficiency decreased, indicating information entropy increase',
        violations: [{
          type: 'efficiency_regression',
          severity: 'warning' as const,
          description: 'Market efficiency should continuously improve through information aggregation',
          data: {
            previous: previousState.marketEfficiency.toString(),
            current: currentState.marketEfficiency.toString()
          }
        }],
        suggestions: ['Increase information aggregation activities', 'Improve source credibility weighting']
      });
    }

    return this.createValidationResult(results);
  }

  /**
   * Validate all four laws comprehensively
   */
  validateAll(params: {
    transaction?: Transaction;
    informationState?: InformationState;
    previousInformationState?: InformationState;
    team?: TeamComposition;
    reputationChange?: ReputationChange;
    agent?: Agent;
  }): ValidationResult {
    const results = [];

    if (params.transaction) {
      results.push(this.valueValidator.validate(params.transaction));
    }

    if (params.informationState) {
      results.push(this.entropyValidator.validate(
        params.informationState, 
        params.previousInformationState
      ));
    }

    if (params.team) {
      results.push(this.collaborativeValidator.validate(params.team));
    }

    if (params.reputationChange) {
      results.push(this.reputationValidator.validate(
        params.reputationChange, 
        params.agent
      ));
    }

    return this.createValidationResult(results);
  }

  /**
   * Create comprehensive validation result
   */
  private createValidationResult(results: any[]): ValidationResult {
    const isValid = results.every(r => r.isValid);
    const timestamp = new Date();

    const criticalViolations = results
      .flatMap(r => r.violations || [])
      .filter(v => v.severity === 'critical').length;

    const errorViolations = results
      .flatMap(r => r.violations || [])
      .filter(v => v.severity === 'error').length;

    const warningViolations = results
      .flatMap(r => r.violations || [])
      .filter(v => v.severity === 'warning').length;

    let summary = '';
    if (isValid) {
      summary = 'All economic laws validated successfully';
    } else {
      summary = `Validation failed: ${criticalViolations} critical, ${errorViolations} errors, ${warningViolations} warnings`;
    }

    return {
      isValid,
      timestamp,
      results,
      summary
    };
  }

  /**
   * Get specific validator for advanced usage
   */
  getValueValidator(): ValueConservationValidator {
    return this.valueValidator;
  }

  getEntropyValidator(): InformationEntropyValidator {
    return this.entropyValidator;
  }

  getCollaborativeValidator(): CollaborativeAdvantageValidator {
    return this.collaborativeValidator;
  }

  getReputationValidator(): ReputationAccumulationValidator {
    return this.reputationValidator;
  }
}