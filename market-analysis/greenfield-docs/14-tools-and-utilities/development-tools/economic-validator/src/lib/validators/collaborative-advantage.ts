import { Decimal } from 'decimal.js';
import { CurrencyCalculator, TARGET_SYNERGY } from '@vibelaunch/currency-calculator';
import { 
  TeamComposition, 
  LawValidationResult, 
  EconomicLaw,
  Violation 
} from '../../types/validation';

/**
 * Validator for the Law of Collaborative Advantage
 * 
 * "The total value created by a properly formed team must exceed the sum 
 * of individual agent values by a factor proportional to the synergy coefficient."
 */
export class CollaborativeAdvantageValidator {
  private calculator: CurrencyCalculator;
  private readonly MIN_SYNERGY = new Decimal(1.1); // Minimum 10% improvement
  private readonly OPTIMAL_TEAM_SIZE = 5;

  constructor() {
    this.calculator = new CurrencyCalculator();
  }

  /**
   * Validate team composition against collaborative advantage law
   */
  validate(team: TeamComposition): LawValidationResult {
    const violations: Violation[] = [];

    // Calculate expected minimum performance
    const individualSum = Object.values(team.individualPerformance)
      .reduce((sum, perf) => sum.plus(perf), new Decimal(0));
    
    const expectedMinimum = individualSum.mul(this.MIN_SYNERGY);
    const expectedOptimal = individualSum.mul(TARGET_SYNERGY);

    // Check if team performance meets minimum requirement
    if (team.teamPerformance.lt(expectedMinimum)) {
      violations.push({
        type: 'insufficient_synergy',
        severity: 'error',
        description: 'Team performance does not exceed individual sum by required margin',
        data: {
          teamPerformance: team.teamPerformance.toString(),
          individualSum: individualSum.toString(),
          minimumRequired: expectedMinimum.toString(),
          actualSynergy: team.synergyFactor.toString()
        }
      });
    }

    // Check synergy factor validity
    if (team.synergyFactor.lt(1)) {
      violations.push({
        type: 'negative_synergy',
        severity: 'critical',
        description: 'Team performs worse than individuals (negative synergy)',
        data: {
          synergyFactor: team.synergyFactor.toString(),
          performance: team.teamPerformance.toString()
        }
      });
    }

    // Validate team size
    if (team.agents.length < 2) {
      violations.push({
        type: 'invalid_team_size',
        severity: 'error',
        description: 'Team must have at least 2 agents',
        data: { teamSize: team.agents.length }
      });
    } else if (team.agents.length > 10) {
      violations.push({
        type: 'oversized_team',
        severity: 'warning',
        description: 'Large teams may suffer from coordination overhead',
        data: { 
          teamSize: team.agents.length,
          optimalSize: this.OPTIMAL_TEAM_SIZE
        }
      });
    }

    // Validate skill diversity
    const skillDiversity = this.calculateSkillDiversity(team);
    if (skillDiversity.lt(0.3)) {
      violations.push({
        type: 'low_skill_diversity',
        severity: 'warning',
        description: 'Team lacks skill diversity for optimal synergy',
        data: {
          diversity: skillDiversity.toString(),
          threshold: '0.3'
        }
      });
    }

    // Check for optimal team achievement
    const achievesOptimal = team.teamPerformance.gte(expectedOptimal);
    if (!achievesOptimal && team.agents.length === this.OPTIMAL_TEAM_SIZE) {
      violations.push({
        type: 'suboptimal_performance',
        severity: 'warning',
        description: 'Optimal-sized team not achieving 194.4% synergy target',
        data: {
          actualSynergy: team.synergyFactor.mul(100).toFixed(1) + '%',
          targetSynergy: TARGET_SYNERGY.mul(100).toFixed(1) + '%'
        }
      });
    }

    // Validate individual performances
    this.validateIndividualPerformances(team, violations);

    return {
      law: EconomicLaw.COLLABORATIVE_ADVANTAGE,
      isValid: violations.filter(v => v.severity !== 'warning').length === 0,
      details: violations.length === 0 
        ? `Team achieves ${team.synergyFactor.mul(100).toFixed(1)}% synergy - collaborative advantage maintained`
        : `Collaborative advantage issues: ${violations.length} detected`,
      violations,
      suggestions: this.generateSuggestions(violations, team)
    };
  }

  /**
   * Calculate skill diversity score
   */
  private calculateSkillDiversity(team: TeamComposition): Decimal {
    const allSkills = new Set<string>();
    const skillCounts = new Map<string, number>();

    for (const agent of team.agents) {
      for (const skill of agent.skills) {
        allSkills.add(skill);
        skillCounts.set(skill, (skillCounts.get(skill) || 0) + 1);
      }
    }

    if (allSkills.size === 0) {
      return new Decimal(0);
    }

    // Calculate how evenly distributed skills are
    const uniqueSkills = Array.from(skillCounts.values())
      .filter(count => count === 1).length;
    
    const diversity = new Decimal(uniqueSkills).div(allSkills.size);
    
    // Bonus for having complementary skills
    const complementarity = new Decimal(allSkills.size).div(
      team.agents.length * 3 // Assume agents should have ~3 skills each
    ).clamp(0, 1);

    return diversity.mul(0.7).plus(complementarity.mul(0.3));
  }

  /**
   * Validate individual performance values
   */
  private validateIndividualPerformances(team: TeamComposition, violations: Violation[]): void {
    const agentIds = team.agents.map(a => a.id);
    const performanceIds = Object.keys(team.individualPerformance);

    // Check all agents have performance data
    for (const agentId of agentIds) {
      if (!performanceIds.includes(agentId)) {
        violations.push({
          type: 'missing_performance_data',
          severity: 'error',
          description: 'Agent missing individual performance data',
          data: { agentId }
        });
      }
    }

    // Check for negative performance
    for (const [agentId, performance] of Object.entries(team.individualPerformance)) {
      if (performance.lt(0)) {
        violations.push({
          type: 'negative_performance',
          severity: 'critical',
          description: 'Individual performance cannot be negative',
          data: { agentId, performance: performance.toString() }
        });
      }
    }

    // Check for suspiciously high individual performance
    const avgPerformance = Object.values(team.individualPerformance)
      .reduce((sum, p) => sum.plus(p), new Decimal(0))
      .div(Object.keys(team.individualPerformance).length);

    for (const [agentId, performance] of Object.entries(team.individualPerformance)) {
      if (performance.gt(avgPerformance.mul(5))) {
        violations.push({
          type: 'outlier_performance',
          severity: 'warning',
          description: 'Individual performance significantly above average',
          data: {
            agentId,
            performance: performance.toString(),
            average: avgPerformance.toString()
          }
        });
      }
    }
  }

  /**
   * Generate suggestions for improving collaborative advantage
   */
  private generateSuggestions(violations: Violation[], team: TeamComposition): string[] {
    const suggestions: string[] = [];

    const hasInsufficientSynergy = violations.some(v => v.type === 'insufficient_synergy');
    const hasNegativeSynergy = violations.some(v => v.type === 'negative_synergy');
    const hasLowDiversity = violations.some(v => v.type === 'low_skill_diversity');
    const isOversized = violations.some(v => v.type === 'oversized_team');

    if (hasInsufficientSynergy || hasNegativeSynergy) {
      suggestions.push('Review team composition for better skill complementarity');
      suggestions.push('Improve communication channels between team members');
      suggestions.push('Implement collaborative tools and processes');
      suggestions.push('Consider team training or workshops');
    }

    if (hasLowDiversity) {
      suggestions.push('Add agents with complementary skills to the team');
      suggestions.push('Avoid redundant skill sets in team composition');
      suggestions.push('Consider cross-training to increase skill diversity');
    }

    if (isOversized) {
      suggestions.push(`Consider splitting into smaller teams (optimal size: ${this.OPTIMAL_TEAM_SIZE})`);
      suggestions.push('Implement hierarchical coordination for large teams');
      suggestions.push('Use sub-teams with clear interfaces');
    }

    if (team.agents.length !== this.OPTIMAL_TEAM_SIZE) {
      suggestions.push(`Optimal team size is ${this.OPTIMAL_TEAM_SIZE} agents for 194.4% synergy`);
    }

    return suggestions;
  }

  /**
   * Calculate expected synergy based on team composition
   */
  calculateExpectedSynergy(team: TeamComposition): Decimal {
    // Start with base synergy
    let synergy = new Decimal(1);

    // Size factor (optimal at 5)
    const sizeDiff = Math.abs(team.agents.length - this.OPTIMAL_TEAM_SIZE);
    const sizeFactor = new Decimal(1).minus(new Decimal(sizeDiff).mul(0.05)).clamp(0.5, 1);
    synergy = synergy.mul(sizeFactor);

    // Skill diversity factor
    const diversity = this.calculateSkillDiversity(team);
    synergy = synergy.mul(new Decimal(1).plus(diversity.mul(0.4)));

    // Reputation factor (higher reputation = better coordination)
    const avgReputation = team.agents
      .reduce((sum, agent) => sum.plus(agent.reputation), new Decimal(0))
      .div(team.agents.length);
    synergy = synergy.mul(new Decimal(1).plus(avgReputation.mul(0.2)));

    // Communication efficiency (assumed based on team size)
    const communicationEfficiency = new Decimal(1).div(
      new Decimal(1).plus(new Decimal(team.agents.length).minus(2).mul(0.1))
    );
    synergy = synergy.mul(new Decimal(1).plus(communicationEfficiency.mul(0.25)));

    return synergy.clamp(0.5, 2.5);
  }
}