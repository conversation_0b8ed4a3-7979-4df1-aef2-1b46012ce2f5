import { Decimal } from 'decimal.js';
import { CurrencyCalculator, MultiCurrencyAmount, CONSERVATION_TOLERANCE } from '@vibelaunch/currency-calculator';
import { 
  Transaction, 
  LawValidationResult, 
  EconomicLaw,
  Violation 
} from '../../types/validation';

/**
 * Validator for the Law of Value Conservation
 * 
 * "Total value in the system can neither be created nor destroyed, 
 * only transformed and redistributed through agent interactions."
 */
export class ValueConservationValidator {
  private calculator: CurrencyCalculator;

  constructor() {
    this.calculator = new CurrencyCalculator();
  }

  /**
   * Validate a transaction against value conservation law
   */
  validate(transaction: Transaction): LawValidationResult {
    const violations: Violation[] = [];
    
    // Calculate total inputs
    const totalInputs = this.sumInputs(transaction.inputs);
    
    // Calculate total outputs including fees
    const totalOutputs = this.sumOutputs(transaction.outputs);
    const totalWithFees = this.calculator.addAmounts(totalOutputs, transaction.fees);
    
    // Check conservation
    const conservationProof = this.calculator.validateConservation(
      totalInputs,
      totalWithFees
    );

    if (!conservationProof.isValid) {
      // Identify specific violations
      for (const [currency, diff] of Object.entries(conservationProof.difference)) {
        const difference = diff as Decimal;
        if (difference.abs().gt(0)) {
          violations.push({
            type: 'value_imbalance',
            severity: difference.abs().gt(totalInputs[currency as keyof MultiCurrencyAmount].mul(0.01)) ? 'critical' : 'error',
            description: `${currency} imbalance: ${difference.gt(0) ? '+' : ''}${difference.toFixed(4)}`,
            data: {
              currency,
              difference: difference.toString(),
              inputAmount: totalInputs[currency as keyof MultiCurrencyAmount].toString(),
              outputAmount: totalWithFees[currency as keyof MultiCurrencyAmount].toString()
            }
          });
        }
      }
    }

    // Additional validations
    this.validateNoNegativeAmounts(transaction, violations);
    this.validateFeeReasonableness(transaction, totalInputs, violations);

    return {
      law: EconomicLaw.VALUE_CONSERVATION,
      isValid: violations.length === 0,
      details: violations.length === 0 
        ? 'Value conservation law maintained - all currency flows balance within tolerance'
        : `Value conservation violated: ${violations.length} issue(s) detected`,
      violations,
      suggestions: this.generateSuggestions(violations)
    };
  }

  /**
   * Sum all inputs
   */
  private sumInputs(inputs: Transaction['inputs']): MultiCurrencyAmount {
    let total = this.calculator.createEmptyAmount();
    
    for (const input of inputs) {
      total = this.calculator.addAmounts(total, input.amounts);
    }
    
    return total;
  }

  /**
   * Sum all outputs
   */
  private sumOutputs(outputs: Transaction['outputs']): MultiCurrencyAmount {
    let total = this.calculator.createEmptyAmount();
    
    for (const output of outputs) {
      total = this.calculator.addAmounts(total, output.amounts);
    }
    
    return total;
  }

  /**
   * Validate no negative amounts
   */
  private validateNoNegativeAmounts(transaction: Transaction, violations: Violation[]): void {
    // Check inputs
    for (const input of transaction.inputs) {
      for (const [currency, amount] of Object.entries(input.amounts)) {
        if ((amount as Decimal).lt(0)) {
          violations.push({
            type: 'negative_amount',
            severity: 'critical',
            description: `Negative ${currency} amount in input`,
            data: { walletId: input.walletId, currency, amount: amount.toString() }
          });
        }
      }
    }

    // Check outputs
    for (const output of transaction.outputs) {
      for (const [currency, amount] of Object.entries(output.amounts)) {
        if ((amount as Decimal).lt(0)) {
          violations.push({
            type: 'negative_amount',
            severity: 'critical',
            description: `Negative ${currency} amount in output`,
            data: { walletId: output.walletId, currency, amount: amount.toString() }
          });
        }
      }
    }

    // Check fees
    for (const [currency, amount] of Object.entries(transaction.fees)) {
      if ((amount as Decimal).lt(0)) {
        violations.push({
          type: 'negative_fee',
          severity: 'error',
          description: `Negative ${currency} fee`,
          data: { currency, amount: amount.toString() }
        });
      }
    }
  }

  /**
   * Validate fee reasonableness
   */
  private validateFeeReasonableness(
    transaction: Transaction, 
    totalInputs: MultiCurrencyAmount, 
    violations: Violation[]
  ): void {
    const MAX_FEE_PERCENTAGE = new Decimal(0.1); // 10% max fees
    
    for (const [currency, feeAmount] of Object.entries(transaction.fees)) {
      const inputAmount = totalInputs[currency as keyof MultiCurrencyAmount];
      if (inputAmount.gt(0) && (feeAmount as Decimal).gt(0)) {
        const feePercentage = (feeAmount as Decimal).div(inputAmount);
        
        if (feePercentage.gt(MAX_FEE_PERCENTAGE)) {
          violations.push({
            type: 'excessive_fee',
            severity: 'warning',
            description: `${currency} fee exceeds 10% of transaction value`,
            data: {
              currency,
              feeAmount: feeAmount.toString(),
              feePercentage: feePercentage.mul(100).toFixed(2) + '%'
            }
          });
        }
      }
    }
  }

  /**
   * Generate suggestions for fixing violations
   */
  private generateSuggestions(violations: Violation[]): string[] {
    const suggestions: string[] = [];
    
    const hasValueImbalance = violations.some(v => v.type === 'value_imbalance');
    const hasNegativeAmounts = violations.some(v => v.type === 'negative_amount');
    const hasExcessiveFees = violations.some(v => v.type === 'excessive_fee');
    
    if (hasValueImbalance) {
      suggestions.push('Ensure all inputs equal outputs plus fees for each currency');
      suggestions.push('Check for rounding errors in currency calculations');
      suggestions.push('Verify all wallets are properly debited and credited');
    }
    
    if (hasNegativeAmounts) {
      suggestions.push('All amounts must be non-negative');
      suggestions.push('Use separate reversal transactions instead of negative amounts');
    }
    
    if (hasExcessiveFees) {
      suggestions.push('Consider reducing transaction fees to under 10%');
      suggestions.push('High fees may indicate inefficient routing or excessive intermediaries');
    }
    
    return suggestions;
  }

  /**
   * Validate batch of transactions
   */
  validateBatch(transactions: Transaction[]): LawValidationResult {
    const allViolations: Violation[] = [];
    let allValid = true;

    // Check each transaction individually
    for (const tx of transactions) {
      const result = this.validate(tx);
      if (!result.isValid) {
        allValid = false;
        allViolations.push(...(result.violations || []));
      }
    }

    // Check batch-level conservation
    const batchInputs = this.calculator.createEmptyAmount();
    const batchOutputs = this.calculator.createEmptyAmount();

    for (const tx of transactions) {
      const inputs = this.sumInputs(tx.inputs);
      const outputs = this.sumOutputs(tx.outputs);
      const outputsWithFees = this.calculator.addAmounts(outputs, tx.fees);
      
      Object.keys(batchInputs).forEach(currency => {
        batchInputs[currency as keyof MultiCurrencyAmount] = 
          batchInputs[currency as keyof MultiCurrencyAmount].plus(
            inputs[currency as keyof MultiCurrencyAmount]
          );
        batchOutputs[currency as keyof MultiCurrencyAmount] = 
          batchOutputs[currency as keyof MultiCurrencyAmount].plus(
            outputsWithFees[currency as keyof MultiCurrencyAmount]
          );
      });
    }

    const batchConservation = this.calculator.validateConservation(batchInputs, batchOutputs);
    
    if (!batchConservation.isValid) {
      allValid = false;
      allViolations.push({
        type: 'batch_imbalance',
        severity: 'critical',
        description: 'Batch-level value conservation violated',
        data: {
          transactionCount: transactions.length,
          differences: batchConservation.difference
        }
      });
    }

    return {
      law: EconomicLaw.VALUE_CONSERVATION,
      isValid: allValid,
      details: allValid 
        ? `All ${transactions.length} transactions maintain value conservation`
        : `Value conservation violations in batch: ${allViolations.length} issue(s)`,
      violations: allViolations,
      suggestions: this.generateSuggestions(allViolations)
    };
  }
}