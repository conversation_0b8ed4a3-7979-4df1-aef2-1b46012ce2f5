import { Decimal } from 'decimal.js';
import { 
  InformationState, 
  LawValidationResult, 
  EconomicLaw,
  Violation 
} from '../../types/validation';

/**
 * Validator for the Law of Information Entropy
 * 
 * "Information entropy can only decrease through active aggregation mechanisms,
 * and the rate of entropy reduction determines the speed of market efficiency improvements."
 */
export class InformationEntropyValidator {
  /**
   * Validate information state changes against entropy law
   */
  validate(currentState: InformationState, previousState?: InformationState): LawValidationResult {
    const violations: Violation[] = [];

    // If no previous state, just validate current state properties
    if (!previousState) {
      return this.validateSingleState(currentState);
    }

    // Check entropy change
    const entropyChange = currentState.entropy.minus(previousState.entropy);
    
    if (entropyChange.gt(0)) {
      // Entropy increased - need to validate if it's justified
      const timeDiff = (currentState.timestamp.getTime() - previousState.timestamp.getTime()) / 1000; // seconds
      const naturalIncrease = this.calculateNaturalEntropyIncrease(previousState, timeDiff);
      
      if (entropyChange.gt(naturalIncrease)) {
        violations.push({
          type: 'excessive_entropy_increase',
          severity: 'error',
          description: 'Information entropy increased beyond natural information decay',
          data: {
            previousEntropy: previousState.entropy.toString(),
            currentEntropy: currentState.entropy.toString(),
            change: entropyChange.toString(),
            maxAllowed: naturalIncrease.toString()
          }
        });
      }
    }

    // Validate aggregation effectiveness
    if (currentState.sources.length > 0) {
      const aggregationQuality = this.calculateAggregationQuality(currentState);
      
      if (aggregationQuality.lt(0.5)) {
        violations.push({
          type: 'poor_aggregation',
          severity: 'warning',
          description: 'Information aggregation quality below threshold',
          data: {
            quality: aggregationQuality.toString(),
            threshold: '0.5',
            sourceCount: currentState.sources.length
          }
        });
      }

      // Check for entropy reduction with active aggregation
      if (entropyChange.gte(0) && currentState.sources.length > previousState.sources.length) {
        violations.push({
          type: 'ineffective_aggregation',
          severity: 'error',
          description: 'Active aggregation failed to reduce entropy',
          data: {
            newSources: currentState.sources.length - previousState.sources.length,
            entropyChange: entropyChange.toString()
          }
        });
      }
    }

    // Validate credibility weights
    this.validateCredibilityWeights(currentState, violations);

    return {
      law: EconomicLaw.INFORMATION_ENTROPY,
      isValid: violations.length === 0,
      details: violations.length === 0 
        ? 'Information entropy law maintained - entropy properly managed through aggregation'
        : `Information entropy violations: ${violations.length} issue(s) detected`,
      violations,
      suggestions: this.generateSuggestions(violations, currentState)
    };
  }

  /**
   * Validate a single information state
   */
  private validateSingleState(state: InformationState): LawValidationResult {
    const violations: Violation[] = [];

    // Check entropy bounds
    if (state.entropy.lt(0) || state.entropy.gt(1)) {
      violations.push({
        type: 'invalid_entropy',
        severity: 'critical',
        description: 'Entropy must be between 0 and 1',
        data: { entropy: state.entropy.toString() }
      });
    }

    // Validate credibility weights
    this.validateCredibilityWeights(state, violations);

    // Check for duplicate sources
    const uniqueSources = new Set(state.sources.map(s => s.agentId));
    if (uniqueSources.size < state.sources.length) {
      violations.push({
        type: 'duplicate_sources',
        severity: 'warning',
        description: 'Duplicate information sources detected',
        data: {
          totalSources: state.sources.length,
          uniqueSources: uniqueSources.size
        }
      });
    }

    return {
      law: EconomicLaw.INFORMATION_ENTROPY,
      isValid: violations.length === 0,
      details: violations.length === 0
        ? 'Information state is valid'
        : `Information state violations: ${violations.length} issue(s) detected`,
      violations,
      suggestions: this.generateSuggestions(violations, state)
    };
  }

  /**
   * Calculate natural entropy increase over time
   */
  private calculateNaturalEntropyIncrease(state: InformationState, timeDelta: number): Decimal {
    // Information decays at 1% per hour without active maintenance
    const hoursPassed = new Decimal(timeDelta).div(3600);
    const decayRate = new Decimal(0.01);
    
    // Maximum entropy is 1, so increase is limited
    const maxIncrease = new Decimal(1).minus(state.entropy);
    const naturalIncrease = state.entropy.mul(decayRate).mul(hoursPassed);
    
    return Decimal.min(naturalIncrease, maxIncrease);
  }

  /**
   * Calculate quality of information aggregation
   */
  private calculateAggregationQuality(state: InformationState): Decimal {
    if (state.sources.length === 0) {
      return new Decimal(0);
    }

    // Factors that determine aggregation quality:
    // 1. Average credibility of sources
    const avgCredibility = state.sources
      .reduce((sum, source) => sum.plus(source.credibility), new Decimal(0))
      .div(state.sources.length);

    // 2. Diversity of contributions (avoid over-reliance on single source)
    const totalContribution = state.sources
      .reduce((sum, source) => sum.plus(source.contribution), new Decimal(0));
    
    const contributions = state.sources.map(s => s.contribution.div(totalContribution));
    const shannonIndex = contributions
      .reduce((sum, p) => {
        if (p.gt(0)) {
          return sum.minus(p.mul(Decimal.ln(p)));
        }
        return sum;
      }, new Decimal(0));
    
    const maxEntropy = Decimal.ln(state.sources.length);
    const diversity = maxEntropy.gt(0) ? shannonIndex.div(maxEntropy) : new Decimal(0);

    // 3. Total information contributed
    const informationCompleteness = Decimal.min(totalContribution, new Decimal(1));

    // Weighted quality score
    const quality = avgCredibility.mul(0.4)
      .plus(diversity.mul(0.3))
      .plus(informationCompleteness.mul(0.3));

    return quality;
  }

  /**
   * Validate credibility weights
   */
  private validateCredibilityWeights(state: InformationState, violations: Violation[]): void {
    for (const source of state.sources) {
      if (source.credibility.lt(0) || source.credibility.gt(1)) {
        violations.push({
          type: 'invalid_credibility',
          severity: 'error',
          description: 'Credibility must be between 0 and 1',
          data: {
            agentId: source.agentId,
            credibility: source.credibility.toString()
          }
        });
      }

      if (source.contribution.lt(0)) {
        violations.push({
          type: 'negative_contribution',
          severity: 'error',
          description: 'Information contribution cannot be negative',
          data: {
            agentId: source.agentId,
            contribution: source.contribution.toString()
          }
        });
      }
    }
  }

  /**
   * Generate suggestions for fixing violations
   */
  private generateSuggestions(violations: Violation[], state: InformationState): string[] {
    const suggestions: string[] = [];

    const hasEntropyIncrease = violations.some(v => v.type === 'excessive_entropy_increase');
    const hasPoorAggregation = violations.some(v => v.type === 'poor_aggregation');
    const hasIneffectiveAggregation = violations.some(v => v.type === 'ineffective_aggregation');

    if (hasEntropyIncrease) {
      suggestions.push('Implement active information aggregation to reduce entropy');
      suggestions.push('Increase frequency of information updates from high-credibility sources');
    }

    if (hasPoorAggregation) {
      suggestions.push('Increase participation from high-credibility agents');
      suggestions.push('Improve diversity of information sources');
      suggestions.push('Ensure all sources contribute meaningful information');
    }

    if (hasIneffectiveAggregation) {
      suggestions.push('Review aggregation algorithms for effectiveness');
      suggestions.push('Weight contributions by source credibility');
      suggestions.push('Filter out low-quality or redundant information');
    }

    if (state.sources.length < 3) {
      suggestions.push('Increase number of information sources for better aggregation');
    }

    return suggestions;
  }

  /**
   * Calculate entropy reduction from aggregation
   */
  calculateEntropyReduction(
    sources: InformationState['sources'], 
    initialEntropy: Decimal
  ): Decimal {
    if (sources.length === 0) {
      return new Decimal(0);
    }

    // Entropy reduction based on:
    // 1. Number of sources (diminishing returns)
    const sourceEffect = new Decimal(1).minus(
      new Decimal(1).div(new Decimal(sources.length).plus(1))
    );

    // 2. Average credibility
    const avgCredibility = sources
      .reduce((sum, s) => sum.plus(s.credibility), new Decimal(0))
      .div(sources.length);

    // 3. Total information contributed
    const totalInfo = sources
      .reduce((sum, s) => sum.plus(s.contribution), new Decimal(0));
    const infoEffect = Decimal.min(totalInfo, new Decimal(1));

    // Calculate reduction
    const reductionFactor = sourceEffect.mul(0.3)
      .plus(avgCredibility.mul(0.4))
      .plus(infoEffect.mul(0.3));

    return initialEntropy.mul(reductionFactor);
  }
}