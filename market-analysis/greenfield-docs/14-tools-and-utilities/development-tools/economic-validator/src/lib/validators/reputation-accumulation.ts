import { Decimal } from 'decimal.js';
import { 
  ReputationChange,
  ReputationChangeReason,
  LawValidationResult, 
  EconomicLaw,
  Violation,
  Agent
} from '../../types/validation';

/**
 * Validator for the Law of Reputation Accumulation
 * 
 * "Reputation can only increase through demonstrated performance and can only 
 * decrease through verified poor performance or the passage of time without activity."
 */
export class ReputationAccumulationValidator {
  private readonly MAX_REPUTATION = new Decimal(1);
  private readonly MIN_REPUTATION = new Decimal(0);
  private readonly MAX_SINGLE_INCREASE = new Decimal(0.1); // 10% max per event
  private readonly MAX_SINGLE_DECREASE = new Decimal(0.2); // 20% max per event
  private readonly INACTIVITY_THRESHOLD_DAYS = 30;
  private readonly DAILY_DECAY_RATE = new Decimal(0.001); // 0.1% per day

  /**
   * Validate a reputation change against accumulation law
   */
  validate(change: ReputationChang<PERSON>, agent?: Agent): LawValidationResult {
    const violations: Violation[] = [];

    // Validate reputation bounds
    if (change.newReputation.lt(this.MIN_REPUTATION) || change.newReputation.gt(this.MAX_REPUTATION)) {
      violations.push({
        type: 'reputation_out_of_bounds',
        severity: 'critical',
        description: 'Reputation must be between 0 and 1',
        data: {
          newReputation: change.newReputation.toString(),
          previousReputation: change.previousReputation.toString()
        }
      });
    }

    // Calculate change amount
    const changeAmount = change.newReputation.minus(change.previousReputation);
    const isIncrease = changeAmount.gt(0);

    // Validate change based on reason
    if (isIncrease) {
      this.validateReputationIncrease(change, changeAmount, violations);
    } else if (changeAmount.lt(0)) {
      this.validateReputationDecrease(change, changeAmount.abs(), agent, violations);
    }

    // Check for sudden large changes
    if (changeAmount.abs().gt(this.MAX_SINGLE_INCREASE)) {
      violations.push({
        type: 'excessive_reputation_change',
        severity: 'warning',
        description: 'Reputation change exceeds recommended single-event maximum',
        data: {
          change: changeAmount.toString(),
          maximum: isIncrease ? this.MAX_SINGLE_INCREASE.toString() : this.MAX_SINGLE_DECREASE.toString()
        }
      });
    }

    // Validate evidence for significant changes
    if (changeAmount.abs().gt(0.05) && !change.evidence) {
      violations.push({
        type: 'missing_evidence',
        severity: 'error',
        description: 'Significant reputation change requires evidence',
        data: {
          change: changeAmount.toString(),
          reason: change.reason
        }
      });
    }

    return {
      law: EconomicLaw.REPUTATION_ACCUMULATION,
      isValid: violations.filter(v => v.severity !== 'warning').length === 0,
      details: violations.length === 0 
        ? 'Reputation change follows accumulation law'
        : `Reputation accumulation violations: ${violations.length} issue(s) detected`,
      violations,
      suggestions: this.generateSuggestions(violations, change)
    };
  }

  /**
   * Validate reputation increase
   */
  private validateReputationIncrease(
    change: ReputationChange, 
    amount: Decimal, 
    violations: Violation[]
  ): void {
    // Check valid reasons for increase
    const validIncreaseReasons = [
      ReputationChangeReason.TASK_COMPLETED,
      ReputationChangeReason.QUALITY_ASSESSMENT,
      ReputationChangeReason.PEER_REVIEW
    ];

    if (!validIncreaseReasons.includes(change.reason)) {
      violations.push({
        type: 'invalid_increase_reason',
        severity: 'error',
        description: 'Reputation can only increase through demonstrated performance',
        data: {
          reason: change.reason,
          validReasons: validIncreaseReasons
        }
      });
    }

    // Validate increase is proportional to achievement
    if (change.reason === ReputationChangeReason.TASK_COMPLETED) {
      // Should have evidence of task completion
      if (!change.evidence?.taskId || !change.evidence?.quality) {
        violations.push({
          type: 'insufficient_task_evidence',
          severity: 'error',
          description: 'Task completion requires taskId and quality score',
          data: { evidence: change.evidence }
        });
      } else {
        // Increase should be proportional to quality
        const quality = new Decimal(change.evidence.quality);
        const maxIncrease = quality.mul(0.05); // Max 5% for perfect quality
        
        if (amount.gt(maxIncrease)) {
          violations.push({
            type: 'disproportionate_increase',
            severity: 'warning',
            description: 'Reputation increase exceeds quality-based maximum',
            data: {
              increase: amount.toString(),
              quality: quality.toString(),
              maxAllowed: maxIncrease.toString()
            }
          });
        }
      }
    }
  }

  /**
   * Validate reputation decrease
   */
  private validateReputationDecrease(
    change: ReputationChange, 
    amount: Decimal,
    agent: Agent | undefined,
    violations: Violation[]
  ): void {
    // Check valid reasons for decrease
    const validDecreaseReasons = [
      ReputationChangeReason.TASK_FAILED,
      ReputationChangeReason.TIME_DECAY,
      ReputationChangeReason.QUALITY_ASSESSMENT,
      ReputationChangeReason.PEER_REVIEW
    ];

    if (!validDecreaseReasons.includes(change.reason)) {
      violations.push({
        type: 'invalid_decrease_reason',
        severity: 'error',
        description: 'Reputation can only decrease through poor performance or inactivity',
        data: {
          reason: change.reason,
          validReasons: validDecreaseReasons
        }
      });
    }

    // Validate time decay
    if (change.reason === ReputationChangeReason.TIME_DECAY) {
      if (!agent) {
        violations.push({
          type: 'missing_agent_data',
          severity: 'error',
          description: 'Time decay validation requires agent performance data'
        });
      } else {
        const daysSinceActive = this.calculateDaysSinceActive(agent);
        
        if (daysSinceActive < this.INACTIVITY_THRESHOLD_DAYS) {
          violations.push({
            type: 'premature_decay',
            severity: 'error',
            description: 'Time decay only applies after inactivity threshold',
            data: {
              daysSinceActive,
              threshold: this.INACTIVITY_THRESHOLD_DAYS
            }
          });
        } else {
          // Validate decay amount
          const expectedDecay = this.DAILY_DECAY_RATE.mul(daysSinceActive - this.INACTIVITY_THRESHOLD_DAYS);
          const tolerance = expectedDecay.mul(0.1); // 10% tolerance
          
          if (amount.gt(expectedDecay.plus(tolerance))) {
            violations.push({
              type: 'excessive_decay',
              severity: 'warning',
              description: 'Time decay exceeds expected amount',
              data: {
                actualDecay: amount.toString(),
                expectedDecay: expectedDecay.toString(),
                daysInactive: daysSinceActive - this.INACTIVITY_THRESHOLD_DAYS
              }
            });
          }
        }
      }
    }

    // Validate failure-based decrease
    if (change.reason === ReputationChangeReason.TASK_FAILED) {
      if (!change.evidence?.taskId || !change.evidence?.severity) {
        violations.push({
          type: 'insufficient_failure_evidence',
          severity: 'error',
          description: 'Task failure requires taskId and severity',
          data: { evidence: change.evidence }
        });
      }
    }
  }

  /**
   * Calculate days since agent was last active
   */
  private calculateDaysSinceActive(agent: Agent): number {
    const now = new Date();
    const lastActive = agent.performance.lastActive;
    const timeDiff = now.getTime() - lastActive.getTime();
    return Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  }

  /**
   * Generate suggestions for fixing violations
   */
  private generateSuggestions(violations: Violation[], change: ReputationChange): string[] {
    const suggestions: string[] = [];

    const hasInvalidReason = violations.some(v => 
      v.type === 'invalid_increase_reason' || v.type === 'invalid_decrease_reason'
    );
    const hasMissingEvidence = violations.some(v => v.type === 'missing_evidence');
    const hasExcessiveChange = violations.some(v => v.type === 'excessive_reputation_change');

    if (hasInvalidReason) {
      suggestions.push('Use only approved reasons for reputation changes');
      suggestions.push('Increases: task completion, quality assessment, positive peer review');
      suggestions.push('Decreases: task failure, time decay, negative assessment');
    }

    if (hasMissingEvidence) {
      suggestions.push('Provide evidence for all significant reputation changes');
      suggestions.push('Include task IDs, quality scores, or review details');
    }

    if (hasExcessiveChange) {
      suggestions.push('Consider gradual reputation changes over multiple events');
      suggestions.push('Large single changes may indicate system manipulation');
    }

    return suggestions;
  }

  /**
   * Calculate expected reputation after time period
   */
  calculateExpectedReputation(
    currentReputation: Decimal,
    performanceHistory: Agent['performance'],
    daysPassed: number
  ): Decimal {
    let reputation = currentReputation;

    // Apply time decay if inactive
    const daysSinceActive = this.calculateDaysSinceActive({ 
      performance: performanceHistory 
    } as Agent);

    if (daysSinceActive > this.INACTIVITY_THRESHOLD_DAYS) {
      const decayDays = daysSinceActive - this.INACTIVITY_THRESHOLD_DAYS;
      const decay = this.DAILY_DECAY_RATE.mul(decayDays);
      reputation = reputation.minus(decay);
    }

    // Apply performance-based changes
    if (performanceHistory.completedTasks > 0) {
      const successBonus = performanceHistory.successRate.mul(0.01).mul(performanceHistory.completedTasks);
      const qualityBonus = performanceHistory.averageQuality.minus(0.5).mul(0.1);
      
      reputation = reputation.plus(successBonus).plus(qualityBonus);
    }

    // Clamp to valid range
    return reputation.clamp(this.MIN_REPUTATION, this.MAX_REPUTATION);
  }

  /**
   * Validate batch of reputation changes
   */
  validateBatch(changes: ReputationChange[]): LawValidationResult {
    const violations: Violation[] = [];
    const agentChanges = new Map<string, ReputationChange[]>();

    // Group changes by agent
    for (const change of changes) {
      const existing = agentChanges.get(change.agentId) || [];
      existing.push(change);
      agentChanges.set(change.agentId, existing);
    }

    // Validate each agent's changes
    for (const [agentId, agentChangeList] of agentChanges) {
      // Sort by timestamp
      const sorted = agentChangeList.sort((a, b) => 
        a.timestamp.getTime() - b.timestamp.getTime()
      );

      // Check for consistency
      for (let i = 1; i < sorted.length; i++) {
        if (!sorted[i].previousReputation.eq(sorted[i-1].newReputation)) {
          violations.push({
            type: 'inconsistent_reputation_chain',
            severity: 'critical',
            description: 'Reputation changes do not form consistent chain',
            data: {
              agentId,
              change1: { timestamp: sorted[i-1].timestamp, newRep: sorted[i-1].newReputation.toString() },
              change2: { timestamp: sorted[i].timestamp, prevRep: sorted[i].previousReputation.toString() }
            }
          });
        }
      }

      // Check total change
      const totalChange = sorted[sorted.length - 1].newReputation
        .minus(sorted[0].previousReputation);
      
      if (totalChange.abs().gt(0.5)) {
        violations.push({
          type: 'excessive_batch_change',
          severity: 'warning',
          description: 'Large cumulative reputation change in batch',
          data: {
            agentId,
            totalChange: totalChange.toString(),
            changeCount: sorted.length
          }
        });
      }
    }

    return {
      law: EconomicLaw.REPUTATION_ACCUMULATION,
      isValid: violations.filter(v => v.severity !== 'warning').length === 0,
      details: violations.length === 0
        ? `All ${changes.length} reputation changes follow accumulation law`
        : `Reputation batch violations: ${violations.length} issue(s) detected`,
      violations,
      suggestions: this.generateSuggestions(violations, changes[0])
    };
  }
}