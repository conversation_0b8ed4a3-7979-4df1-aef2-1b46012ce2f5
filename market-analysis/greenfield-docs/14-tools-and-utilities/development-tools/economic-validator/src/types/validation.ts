import { Decimal } from 'decimal.js';
import { MultiCurrencyAmount } from '@vibelaunch/currency-calculator';

/**
 * The four fundamental economic laws of VibeLaunch
 */
export enum EconomicLaw {
  VALUE_CONSERVATION = 'VALUE_CONSERVATION',
  INFORMATION_ENTROPY = 'INFORMATION_ENTROPY',
  COLLABORATIVE_ADVANTAGE = 'COLLABORATIVE_ADVANTAGE',
  REPUTATION_ACCUMULATION = 'REPUTATION_ACCUMULATION'
}

/**
 * Transaction to validate
 */
export interface Transaction {
  id: string;
  timestamp: Date;
  inputs: TransactionInput[];
  outputs: TransactionOutput[];
  fees: MultiCurrencyAmount;
  metadata?: Record<string, any>;
}

/**
 * Transaction input
 */
export interface TransactionInput {
  walletId: string;
  amounts: MultiCurrencyAmount;
  signature?: string;
}

/**
 * Transaction output
 */
export interface TransactionOutput {
  walletId: string;
  amounts: MultiCurrencyAmount;
}

/**
 * Information state for entropy calculation
 */
export interface InformationState {
  entropy: Decimal;
  sources: InformationSource[];
  timestamp: Date;
}

/**
 * Information source contribution
 */
export interface InformationSource {
  agentId: string;
  contribution: Decimal;
  credibility: Decimal;
}

/**
 * Team composition for collaborative advantage
 */
export interface TeamComposition {
  agents: Agent[];
  individualPerformance: Record<string, Decimal>;
  teamPerformance: Decimal;
  synergyFactor: Decimal;
}

/**
 * Agent representation
 */
export interface Agent {
  id: string;
  skills: string[];
  reputation: Decimal;
  performance: AgentPerformance;
}

/**
 * Agent performance metrics
 */
export interface AgentPerformance {
  completedTasks: number;
  successRate: Decimal;
  averageQuality: Decimal;
  lastActive: Date;
}

/**
 * Reputation change event
 */
export interface ReputationChange {
  agentId: string;
  previousReputation: Decimal;
  newReputation: Decimal;
  reason: ReputationChangeReason;
  evidence?: any;
  timestamp: Date;
}

/**
 * Reasons for reputation changes
 */
export enum ReputationChangeReason {
  TASK_COMPLETED = 'TASK_COMPLETED',
  TASK_FAILED = 'TASK_FAILED',
  QUALITY_ASSESSMENT = 'QUALITY_ASSESSMENT',
  TIME_DECAY = 'TIME_DECAY',
  PEER_REVIEW = 'PEER_REVIEW'
}

/**
 * Validation result for a single law
 */
export interface LawValidationResult {
  law: EconomicLaw;
  isValid: boolean;
  details: string;
  violations?: Violation[];
  suggestions?: string[];
}

/**
 * Specific violation details
 */
export interface Violation {
  type: string;
  severity: 'warning' | 'error' | 'critical';
  description: string;
  data?: any;
}

/**
 * Complete validation result
 */
export interface ValidationResult {
  isValid: boolean;
  timestamp: Date;
  results: LawValidationResult[];
  summary: string;
}

/**
 * Validation context with historical data
 */
export interface ValidationContext {
  currentState: SystemState;
  previousState?: SystemState;
  history?: SystemState[];
}

/**
 * System state snapshot
 */
export interface SystemState {
  timestamp: Date;
  totalValue: MultiCurrencyAmount;
  entropy: Decimal;
  activeTeams: number;
  averageReputation: Decimal;
  marketEfficiency: Decimal;
}