import { Decimal } from 'decimal.js';
import { CurrencyCalculator } from '../src/lib/calculator';
import { CurrencyType, SynergyComponents } from '../src/types/currency';
import { TARGET_SYNERGY, CONSERVATION_TOLERANCE } from '../src/lib/currency-properties';

describe('CurrencyCalculator', () => {
  let calculator: CurrencyCalculator;

  beforeEach(() => {
    calculator = new CurrencyCalculator();
  });

  describe('Currency Operations', () => {
    test('should create empty multi-currency amount', () => {
      const amount = calculator.createEmptyAmount();
      
      expect(amount.economic.toNumber()).toBe(0);
      expect(amount.quality.toNumber()).toBe(0);
      expect(amount.temporal.toNumber()).toBe(0);
      expect(amount.reliability.toNumber()).toBe(0);
      expect(amount.innovation.toNumber()).toBe(0);
    });

    test('should add multi-currency amounts correctly', () => {
      const a = {
        economic: new Decimal(100),
        quality: new Decimal(1.5),
        temporal: new Decimal(50),
        reliability: new Decimal(0.8),
        innovation: new Decimal(25)
      };

      const b = {
        economic: new Decimal(50),
        quality: new Decimal(0.3),
        temporal: new Decimal(25),
        reliability: new Decimal(0.1),
        innovation: new Decimal(15)
      };

      const result = calculator.addAmounts(a, b);

      expect(result.economic.toNumber()).toBe(150);
      expect(result.quality.toNumber()).toBe(1.8);
      expect(result.temporal.toNumber()).toBe(75);
      expect(result.reliability.toNumber()).toBeCloseTo(0.9);
      expect(result.innovation.toNumber()).toBe(40);
    });

    test('should subtract multi-currency amounts correctly', () => {
      const a = {
        economic: new Decimal(100),
        quality: new Decimal(1.5),
        temporal: new Decimal(50),
        reliability: new Decimal(0.8),
        innovation: new Decimal(25)
      };

      const b = {
        economic: new Decimal(30),
        quality: new Decimal(0.5),
        temporal: new Decimal(20),
        reliability: new Decimal(0.3),
        innovation: new Decimal(10)
      };

      const result = calculator.subtractAmounts(a, b);

      expect(result.economic.toNumber()).toBe(70);
      expect(result.quality.toNumber()).toBe(1.0);
      expect(result.temporal.toNumber()).toBe(30);
      expect(result.reliability.toNumber()).toBeCloseTo(0.5);
      expect(result.innovation.toNumber()).toBe(15);
    });
  });

  describe('Currency Exchange', () => {
    test('should exchange between transferable currencies', () => {
      const amount = new Decimal(1000);
      const result = calculator.exchange(amount, CurrencyType.ECONOMIC, CurrencyType.TEMPORAL);
      
      // Default rate: 1 economic = 100 temporal
      expect(result.toNumber()).toBe(100000);
    });

    test('should return same amount when exchanging to same currency', () => {
      const amount = new Decimal(500);
      const result = calculator.exchange(amount, CurrencyType.ECONOMIC, CurrencyType.ECONOMIC);
      
      expect(result.toNumber()).toBe(500);
    });

    test('should throw error for non-transferable currency', () => {
      const amount = new Decimal(1);
      
      expect(() => {
        calculator.exchange(amount, CurrencyType.QUALITY, CurrencyType.ECONOMIC);
      }).toThrow('quality currency is not transferable');
    });

    test('should respect currency bounds', () => {
      const amount = new Decimal(10); // Will become 10,000 quality, but max is 2
      const result = calculator.exchange(amount, CurrencyType.ECONOMIC, CurrencyType.QUALITY);
      
      expect(result.toNumber()).toBe(2); // Clamped to max
    });
  });

  describe('Temporal Decay', () => {
    test('should apply temporal decay correctly', () => {
      const amount = new Decimal(1000);
      const weekInSeconds = 7 * 24 * 60 * 60;
      
      const decayed = calculator.applyTemporalDecay(amount, weekInSeconds);
      
      // Should be approximately 50% after one week (half-life)
      expect(decayed.toNumber()).toBeCloseTo(500, 0);
    });

    test('should not decay if no time has passed', () => {
      const amount = new Decimal(1000);
      const decayed = calculator.applyTemporalDecay(amount, 0);
      
      expect(decayed.toNumber()).toBe(1000);
    });
  });

  describe('Reliability Yield', () => {
    test('should calculate reliability yield correctly', () => {
      const amount = new Decimal(0.5);
      const days = 365; // One year
      
      const withYield = calculator.calculateReliabilityYield(amount, days);
      
      // 10% annual yield on 0.5 = 0.05
      expect(withYield.toNumber()).toBeCloseTo(0.55, 2);
    });

    test('should handle partial year yields', () => {
      const amount = new Decimal(0.8);
      const days = 182.5; // Half year
      
      const withYield = calculator.calculateReliabilityYield(amount, days);
      
      // 10% annual yield, so 5% for half year
      expect(withYield.toNumber()).toBeCloseTo(0.84, 2);
    });
  });

  describe('Conservation Law Validation', () => {
    test('should validate perfect conservation', () => {
      const before = {
        economic: new Decimal(1000),
        quality: new Decimal(1.5),
        temporal: new Decimal(500),
        reliability: new Decimal(0.8),
        innovation: new Decimal(100)
      };

      const after = { ...before }; // Perfect conservation

      const proof = calculator.validateConservation(before, after);
      
      expect(proof.isValid).toBe(true);
    });

    test('should detect conservation violation', () => {
      const before = {
        economic: new Decimal(1000),
        quality: new Decimal(1.5),
        temporal: new Decimal(500),
        reliability: new Decimal(0.8),
        innovation: new Decimal(100)
      };

      const after = {
        economic: new Decimal(1100), // Extra 100 economic created
        quality: new Decimal(1.5),
        temporal: new Decimal(500),
        reliability: new Decimal(0.8),
        innovation: new Decimal(100)
      };

      const proof = calculator.validateConservation(before, after);
      
      expect(proof.isValid).toBe(false);
    });

    test('should allow conservation within tolerance', () => {
      const before = {
        economic: new Decimal(1000000),
        quality: new Decimal(1.5),
        temporal: new Decimal(500),
        reliability: new Decimal(0.8),
        innovation: new Decimal(100)
      };

      const after = {
        economic: new Decimal(1000000.5), // Small rounding error
        quality: new Decimal(1.5),
        temporal: new Decimal(500),
        reliability: new Decimal(0.8),
        innovation: new Decimal(100)
      };

      const proof = calculator.validateConservation(before, after);
      
      expect(proof.isValid).toBe(true); // Within 0.0001% tolerance
    });
  });

  describe('Team Synergy Calculation', () => {
    test('should calculate optimal synergy to achieve 194.4% target', () => {
      const optimal = calculator.estimateOptimalSynergy();
      const result = calculator.calculateTeamSynergy(optimal);
      
      expect(result.achievesTarget).toBe(true);
      expect(result.totalSynergy.toNumber()).toBeGreaterThanOrEqual(TARGET_SYNERGY.toNumber());
    });

    test('should calculate synergy with custom components', () => {
      const components: SynergyComponents = {
        skillDiversity: new Decimal(0.5),
        knowledgeOverlap: new Decimal(0.5),
        communicationEfficiency: new Decimal(0.5),
        creativePotential: new Decimal(0.5),
        coordinationCost: new Decimal(0.1)
      };

      const result = calculator.calculateTeamSynergy(components);
      
      expect(result.totalSynergy.toNumber()).toBeGreaterThan(1);
      expect(result.totalSynergy.toNumber()).toBeLessThan(TARGET_SYNERGY.toNumber());
      expect(result.achievesTarget).toBe(false);
    });

    test('should clamp synergy between 50% and 250%', () => {
      // Test lower bound
      const lowComponents: SynergyComponents = {
        skillDiversity: new Decimal(0),
        knowledgeOverlap: new Decimal(0),
        communicationEfficiency: new Decimal(0),
        creativePotential: new Decimal(0),
        coordinationCost: new Decimal(0.9) // Very high coordination cost
      };

      const lowResult = calculator.calculateTeamSynergy(lowComponents);
      expect(lowResult.totalSynergy.toNumber()).toBe(0.5);

      // Test upper bound
      const highComponents: SynergyComponents = {
        skillDiversity: new Decimal(1),
        knowledgeOverlap: new Decimal(1),
        communicationEfficiency: new Decimal(1),
        creativePotential: new Decimal(1),
        coordinationCost: new Decimal(0)
      };

      const highResult = calculator.calculateTeamSynergy(highComponents);
      expect(highResult.totalSynergy.toNumber()).toBe(2.5);
    });
  });

  describe('Exchange Rate Management', () => {
    test('should update exchange rates bidirectionally', () => {
      const newRate = new Decimal(150);
      calculator.updateExchangeRate(CurrencyType.ECONOMIC, CurrencyType.TEMPORAL, newRate);
      
      const forwardRate = calculator.getExchangeRate(CurrencyType.ECONOMIC, CurrencyType.TEMPORAL);
      const reverseRate = calculator.getExchangeRate(CurrencyType.TEMPORAL, CurrencyType.ECONOMIC);
      
      expect(forwardRate.rate.toNumber()).toBe(150);
      expect(reverseRate.rate.toNumber()).toBeCloseTo(1 / 150, 6);
    });

    test('should return rate of 1 for same currency', () => {
      const rate = calculator.getExchangeRate(CurrencyType.ECONOMIC, CurrencyType.ECONOMIC);
      
      expect(rate.rate.toNumber()).toBe(1);
      expect(rate.liquidity.toNumber()).toBe(1e9); // High liquidity
    });
  });

  describe('Total Value Calculation', () => {
    test('should calculate total value in target currency', () => {
      const amounts = {
        economic: new Decimal(1000),
        quality: new Decimal(0), // Non-transferable
        temporal: new Decimal(10000), // Worth 100 economic
        reliability: new Decimal(0), // Non-transferable
        innovation: new Decimal(500) // Worth 1000 economic
      };

      const total = calculator.calculateTotalValue(amounts, CurrencyType.ECONOMIC);
      
      expect(total.toNumber()).toBe(2100); // 1000 + 100 + 1000
    });
  });
});