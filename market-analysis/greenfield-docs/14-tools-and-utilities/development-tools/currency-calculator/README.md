# VibeLaunch Currency Calculator

Interactive CLI and library for working with VibeLaunch's revolutionary five-dimensional currency system.

## Overview

The VibeLaunch Currency Calculator helps developers understand and work with the five-dimensional currency system that enables 95%+ market efficiency:

- **Economic (₥)**: Traditional monetary value with dynamic pricing
- **Quality (◈)**: Technical excellence and satisfaction (0-2 range, non-transferable)
- **Temporal (⧗)**: Time efficiency with exponential decay (7-day half-life)
- **Reliability (☆)**: Trust and consistency with yield generation (0-1 range, non-transferable)
- **Innovation (◊)**: Creative solutions with network effects

## Features

- 🔄 **Currency Exchange**: Calculate exchanges between transferable currencies
- 🤝 **Team Synergy**: Calculate team performance multipliers (target: 194.4%)
- ⏰ **Temporal Decay**: Model time-sensitive currency depreciation
- 📈 **Reliability Yield**: Calculate trust-based returns (5-15% annual)
- ⚖️ **Conservation Validation**: Verify economic law compliance
- 🎨 **Beautiful CLI**: Color-coded output with interactive prompts

## Installation

```bash
# Install globally
npm install -g @vibelaunch/currency-calculator

# Or use locally
npm install @vibelaunch/currency-calculator
```

## CLI Usage

### Interactive Mode

```bash
vibelaunch-calc interactive
```

### Direct Commands

```bash
# Exchange currencies
vibelaunch-calc exchange --amount 1000 --from economic --to temporal

# Calculate team synergy
vibelaunch-calc synergy --optimal  # Show optimal configuration
vibelaunch-calc synergy -i         # Interactive input

# Calculate temporal decay
vibelaunch-calc decay

# Calculate reliability yield  
vibelaunch-calc yield

# Validate conservation law
vibelaunch-calc conservation
```

## Programmatic Usage

```typescript
import { 
  CurrencyCalculator, 
  CurrencyType, 
  CurrencyFormatter 
} from '@vibelaunch/currency-calculator';

const calculator = new CurrencyCalculator();

// Exchange currencies
const result = calculator.exchange(
  new Decimal(1000), 
  CurrencyType.ECONOMIC, 
  CurrencyType.TEMPORAL
);

// Calculate team synergy
const optimal = calculator.estimateOptimalSynergy();
const synergyResult = calculator.calculateTeamSynergy(optimal);
console.log(`Synergy: ${synergyResult.percentageGain}%`);

// Apply temporal decay (7 days)
const decayed = calculator.applyTemporalDecay(
  new Decimal(1000), 
  7 * 24 * 60 * 60
);

// Validate conservation
const proof = calculator.validateConservation(before, after);
if (!proof.isValid) {
  throw new Error('Conservation law violated!');
}
```

## Currency Properties

### Economic (₥)
- Fully transferable and divisible
- 18 decimal precision
- Dynamic supply management
- Base currency for exchanges

### Quality (◈)
- Non-transferable (earned only)
- Range: 0 to 2
- Represents technical excellence
- Affects value multipliers

### Temporal (⧗)
- Transferable
- Exponential decay (50% per week)
- Urgency premiums
- Time-sensitive operations

### Reliability (☆)
- Non-transferable (trust-based)
- Range: 0 to 1
- Generates 5-15% annual yield
- Network trust propagation

### Innovation (◊)
- Transferable
- S-curve appreciation
- Network effects (Metcalfe's law)
- Top 10% qualification threshold

## Team Synergy Calculation

The calculator implements the sophisticated synergy formula from the Economic Constitution:

```
Synergy = (1 + 0.4 × SkillDiversity)
        × (1 + 0.3 × KnowledgeOverlap)
        × (1 + 0.25 × CommunicationEfficiency)
        × (1 + 0.2 × CreativePotential)
        × (1 - CoordinationCost)
```

Target: 194.4% (achieved with optimal 5-agent teams)

## Conservation Laws

The calculator enforces the four fundamental economic laws:

1. **Value Conservation**: Total value preserved (±0.0001% tolerance)
2. **Information Entropy**: Can only decrease through aggregation
3. **Collaborative Advantage**: Teams must exceed sum of individuals
4. **Reputation Accumulation**: Only increases through performance

## Development

```bash
# Install dependencies
npm install

# Run in development
npm run dev

# Run tests
npm test
npm run test:coverage

# Build
npm run build

# Lint
npm run lint
```

## Testing

Comprehensive test suite covering:
- Currency operations and exchanges
- Conservation law validation
- Synergy calculations
- Temporal decay modeling
- Reliability yield computation
- Edge cases and bounds

## Examples

See the `examples/` directory for:
- Basic currency operations
- Team formation optimization
- Conservation law scenarios
- Time-based calculations

## Contributing

1. Ensure all tests pass
2. Maintain 90%+ test coverage
3. Follow TypeScript strict mode
4. Update documentation
5. Add examples for new features

## License

MIT License - See LICENSE file for details

## Related Tools

- **Economic Validator**: Validate transactions against all economic laws
- **Contract Generator**: Generate multi-currency smart contracts
- **Synergy Simulator**: Simulate and optimize team formations
- **Market Efficiency Analyzer**: Track progress toward 95%+ efficiency

---

Built with ❤️ for the VibeLaunch revolutionary economic system