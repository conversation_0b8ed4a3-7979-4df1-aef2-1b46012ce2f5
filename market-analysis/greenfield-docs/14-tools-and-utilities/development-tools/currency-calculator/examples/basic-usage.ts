/**
 * Basic usage examples for the VibeLaunch Currency Calculator
 */

import { Decimal } from 'decimal.js';
import { 
  CurrencyCalculator, 
  CurrencyType, 
  CurrencyFormatter,
  MultiCurrencyAmount 
} from '../src';

// Initialize calculator
const calculator = new CurrencyCalculator();

console.log('=== VibeLaunch Currency Calculator Examples ===\n');

// Example 1: Basic Currency Exchange
console.log('1. Currency Exchange Example:');
const economicAmount = new Decimal(1000);
const temporalResult = calculator.exchange(economicAmount, CurrencyType.ECONOMIC, CurrencyType.TEMPORAL);
console.log(`${CurrencyFormatter.formatCurrency(economicAmount, CurrencyType.ECONOMIC)} = ${CurrencyFormatter.formatCurrency(temporalResult, CurrencyType.TEMPORAL)}`);

// Example 2: Multi-Currency Wallet
console.log('\n2. Multi-Currency Wallet:');
const wallet: MultiCurrencyAmount = {
  economic: new Decimal(5000),
  quality: new Decimal(1.8),
  temporal: new Decimal(10000),
  reliability: new Decimal(0.95),
  innovation: new Decimal(250)
};
console.log(CurrencyFormatter.formatMultiCurrencyTable(wallet, 'Agent Wallet Balance'));

// Example 3: Calculate Total Value
console.log('3. Total Value in Economic Currency:');
const totalValue = calculator.calculateTotalValue(wallet, CurrencyType.ECONOMIC);
console.log(`Total: ${CurrencyFormatter.formatCurrency(totalValue, CurrencyType.ECONOMIC)}`);

// Example 4: Temporal Decay
console.log('\n4. Temporal Currency Decay (7 days):');
const temporalAmount = new Decimal(1000);
const decayedAmount = calculator.applyTemporalDecay(temporalAmount, 7 * 24 * 60 * 60);
console.log(`Original: ${CurrencyFormatter.formatCurrency(temporalAmount, CurrencyType.TEMPORAL)}`);
console.log(`After 7 days: ${CurrencyFormatter.formatCurrency(decayedAmount, CurrencyType.TEMPORAL)}`);
console.log(`Loss: ${CurrencyFormatter.formatDecimal(temporalAmount.minus(decayedAmount).div(temporalAmount).mul(100))}%`);

// Example 5: Reliability Yield
console.log('\n5. Reliability Currency Yield (1 year):');
const reliabilityAmount = new Decimal(0.8);
const withYield = calculator.calculateReliabilityYield(reliabilityAmount, 365);
console.log(`Original: ${CurrencyFormatter.formatCurrency(reliabilityAmount, CurrencyType.RELIABILITY)}`);
console.log(`After 1 year: ${CurrencyFormatter.formatCurrency(withYield, CurrencyType.RELIABILITY)}`);

// Example 6: Team Synergy Calculation
console.log('\n6. Team Synergy Calculation:');
const optimalTeam = calculator.estimateOptimalSynergy();
const synergyResult = calculator.calculateTeamSynergy(optimalTeam);
console.log(CurrencyFormatter.formatSynergyResult(synergyResult));

// Example 7: Conservation Law Validation
console.log('\n7. Conservation Law Check:');
const beforeTransaction: MultiCurrencyAmount = {
  economic: new Decimal(1000),
  quality: new Decimal(1.5),
  temporal: new Decimal(500),
  reliability: new Decimal(0.8),
  innovation: new Decimal(100)
};

const afterTransaction: MultiCurrencyAmount = {
  economic: new Decimal(900),
  quality: new Decimal(1.5),
  temporal: new Decimal(600),
  reliability: new Decimal(0.8),
  innovation: new Decimal(100)
};

const proof = calculator.validateConservation(beforeTransaction, afterTransaction);
console.log(`Conservation Valid: ${proof.isValid ? '✓' : '✗'}`);
if (!proof.isValid) {
  console.log('Violations detected in:', Object.entries(proof.difference)
    .filter(([_, diff]) => (diff as Decimal).abs().gt(0))
    .map(([currency]) => currency)
    .join(', ')
  );
}

// Example 8: Custom Team Synergy
console.log('\n8. Custom Team Configuration:');
const customTeam = {
  skillDiversity: new Decimal(0.6),
  knowledgeOverlap: new Decimal(0.5),
  communicationEfficiency: new Decimal(0.7),
  creativePotential: new Decimal(0.6),
  coordinationCost: new Decimal(0.15)
};
const customResult = calculator.calculateTeamSynergy(customTeam);
console.log(`Synergy: ${CurrencyFormatter.formatDecimal(customResult.totalSynergy.mul(100))}%`);
console.log(`Achieves 194.4% target: ${customResult.achievesTarget ? 'Yes' : 'No'}`);

// Example 9: Exchange Rate Updates
console.log('\n9. Dynamic Exchange Rates:');
console.log('Default rate:', CurrencyFormatter.formatExchangeRate(
  CurrencyType.ECONOMIC, 
  CurrencyType.INNOVATION,
  calculator.getExchangeRate(CurrencyType.ECONOMIC, CurrencyType.INNOVATION).rate
));

// Update rate due to market conditions
calculator.updateExchangeRate(CurrencyType.ECONOMIC, CurrencyType.INNOVATION, new Decimal(600));
console.log('Updated rate:', CurrencyFormatter.formatExchangeRate(
  CurrencyType.ECONOMIC,
  CurrencyType.INNOVATION,
  calculator.getExchangeRate(CurrencyType.ECONOMIC, CurrencyType.INNOVATION).rate
));

// Example 10: Complex Transaction
console.log('\n10. Complex Multi-Currency Transaction:');
const agentA: MultiCurrencyAmount = {
  economic: new Decimal(5000),
  quality: new Decimal(1.7),
  temporal: new Decimal(8000),
  reliability: new Decimal(0.9),
  innovation: new Decimal(300)
};

const agentB: MultiCurrencyAmount = {
  economic: new Decimal(3000),
  quality: new Decimal(1.9),
  temporal: new Decimal(12000),
  reliability: new Decimal(0.85),
  innovation: new Decimal(200)
};

// Agent A transfers some currencies to Agent B
const transfer: MultiCurrencyAmount = {
  economic: new Decimal(1000),
  quality: new Decimal(0), // Can't transfer quality
  temporal: new Decimal(2000),
  reliability: new Decimal(0), // Can't transfer reliability
  innovation: new Decimal(50)
};

const agentAAfter = calculator.subtractAmounts(agentA, transfer);
const agentBAfter = calculator.addAmounts(agentB, transfer);

// Validate conservation
const totalBefore = calculator.addAmounts(agentA, agentB);
const totalAfter = calculator.addAmounts(agentAAfter, agentBAfter);
const conservationCheck = calculator.validateConservation(totalBefore, totalAfter);

console.log('Transfer:', CurrencyFormatter.formatMultiCurrencyTable(transfer, 'Transfer Amount'));
console.log(`Conservation maintained: ${conservationCheck.isValid ? '✓' : '✗'}`);

console.log('\n=== End of Examples ===');