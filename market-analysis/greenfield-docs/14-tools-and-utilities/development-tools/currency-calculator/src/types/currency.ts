import { Decimal } from 'decimal.js';

/**
 * The five dimensions of value in the VibeLaunch economic system
 */
export enum CurrencyType {
  ECONOMIC = 'economic',     // ₥ - Traditional monetary value
  QUALITY = 'quality',       // ◈ - Technical excellence and satisfaction
  TEMPORAL = 'temporal',     // ⧗ - Time efficiency and scheduling
  RELIABILITY = 'reliability', // ☆ - Consistency and dependability
  INNOVATION = 'innovation'  // ◊ - Creative and novel solutions
}

/**
 * Currency symbols for display
 */
export const CurrencySymbols: Record<CurrencyType, string> = {
  [CurrencyType.ECONOMIC]: '₥',
  [CurrencyType.QUALITY]: '◈',
  [CurrencyType.TEMPORAL]: '⧗',
  [CurrencyType.RELIABILITY]: '☆',
  [CurrencyType.INNOVATION]: '◊'
};

/**
 * Currency properties and constraints
 */
export interface CurrencyProperties {
  transferable: boolean;
  divisible: boolean;
  minValue: Decimal;
  maxValue: Decimal;
  decayRate?: Decimal; // For temporal currency
  yieldRate?: Decimal; // For reliability currency
}

/**
 * Multi-currency amount representation
 */
export interface MultiCurrencyAmount {
  [CurrencyType.ECONOMIC]: Decimal;
  [CurrencyType.QUALITY]: Decimal;
  [CurrencyType.TEMPORAL]: Decimal;
  [CurrencyType.RELIABILITY]: Decimal;
  [CurrencyType.INNOVATION]: Decimal;
}

/**
 * Exchange rate between two currencies
 */
export interface ExchangeRate {
  from: CurrencyType;
  to: CurrencyType;
  rate: Decimal;
  timestamp: Date;
  liquidity: Decimal;
}

/**
 * Conservation proof for transactions
 */
export interface ConservationProof {
  totalBefore: MultiCurrencyAmount;
  totalAfter: MultiCurrencyAmount;
  difference: MultiCurrencyAmount;
  isValid: boolean;
  tolerance: Decimal;
}

/**
 * Team synergy components
 */
export interface SynergyComponents {
  skillDiversity: Decimal;
  knowledgeOverlap: Decimal;
  communicationEfficiency: Decimal;
  creativePotential: Decimal;
  coordinationCost: Decimal;
}

/**
 * Team synergy calculation result
 */
export interface SynergyResult {
  components: SynergyComponents;
  totalSynergy: Decimal;
  percentageGain: Decimal;
  achievesTarget: boolean; // True if >= 194.4%
}