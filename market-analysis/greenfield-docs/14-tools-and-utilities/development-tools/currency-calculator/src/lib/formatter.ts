import { Decimal } from 'decimal.js';
import chalk from 'chalk';
import { table } from 'table';
import { CurrencyType, CurrencySymbols, MultiCurrencyAmount, SynergyResult } from '../types/currency';

/**
 * Formatting utilities for currency display
 */
export class CurrencyFormatter {
  /**
   * Format a decimal value with appropriate precision
   */
  static formatDecimal(value: Decimal, precision: number = 4): string {
    return value.toFixed(precision);
  }

  /**
   * Format a currency amount with symbol
   */
  static formatCurrency(value: Decimal, currencyType: CurrencyType): string {
    const symbol = CurrencySymbols[currencyType];
    const formatted = this.formatDecimal(value);
    
    switch (currencyType) {
      case CurrencyType.ECONOMIC:
        return chalk.green(`${symbol}${formatted}`);
      case CurrencyType.QUALITY:
        return chalk.blue(`${formatted}${symbol}`);
      case CurrencyType.TEMPORAL:
        return chalk.yellow(`${formatted}${symbol}`);
      case CurrencyType.RELIABILITY:
        return chalk.magenta(`${formatted}${symbol}`);
      case CurrencyType.INNOVATION:
        return chalk.cyan(`${formatted}${symbol}`);
      default:
        return `${formatted}${symbol}`;
    }
  }

  /**
   * Format multi-currency amount as a table
   */
  static formatMultiCurrencyTable(amounts: MultiCurrencyAmount, title?: string): string {
    const data = [
      ['Currency', 'Symbol', 'Amount'],
      ...Object.entries(amounts).map(([currency, amount]) => {
        const type = currency as CurrencyType;
        return [
          chalk.bold(currency.charAt(0).toUpperCase() + currency.slice(1)),
          CurrencySymbols[type],
          this.formatCurrency(amount, type)
        ];
      })
    ];

    const config = {
      header: {
        alignment: 'center' as const,
        content: title ? chalk.bold.underline(title) : undefined
      }
    };

    return table(data, title ? config : undefined);
  }

  /**
   * Format exchange rate
   */
  static formatExchangeRate(from: CurrencyType, to: CurrencyType, rate: Decimal): string {
    const fromSymbol = CurrencySymbols[from];
    const toSymbol = CurrencySymbols[to];
    return `1${fromSymbol} = ${this.formatDecimal(rate)}${toSymbol}`;
  }

  /**
   * Format synergy result
   */
  static formatSynergyResult(result: SynergyResult): string {
    const lines = [
      chalk.bold.underline('Team Synergy Calculation'),
      '',
      chalk.bold('Components:'),
      `  Skill Diversity:         ${this.formatPercent(result.components.skillDiversity)}`,
      `  Knowledge Overlap:       ${this.formatPercent(result.components.knowledgeOverlap)}`,
      `  Communication Efficiency: ${this.formatPercent(result.components.communicationEfficiency)}`,
      `  Creative Potential:      ${this.formatPercent(result.components.creativePotential)}`,
      `  Coordination Cost:       ${chalk.red(this.formatPercent(result.components.coordinationCost))}`,
      '',
      chalk.bold('Results:'),
      `  Total Synergy:    ${this.formatPercent(result.totalSynergy)}`,
      `  Percentage Gain:  ${result.percentageGain.gt(0) ? chalk.green('+') : chalk.red('')}${this.formatDecimal(result.percentageGain)}%`,
      `  Target (194.4%):  ${result.achievesTarget ? chalk.green('✓ ACHIEVED') : chalk.red('✗ NOT ACHIEVED')}`,
    ];

    return lines.join('\n');
  }

  /**
   * Format percentage
   */
  static formatPercent(value: Decimal): string {
    return `${this.formatDecimal(value.mul(100), 1)}%`;
  }

  /**
   * Format conservation proof
   */
  static formatConservationProof(proof: any): string {
    const status = proof.isValid ? 
      chalk.green('✓ CONSERVATION LAW MAINTAINED') : 
      chalk.red('✗ CONSERVATION LAW VIOLATED');

    const lines = [
      chalk.bold.underline('Conservation Law Validation'),
      '',
      `Status: ${status}`,
      `Tolerance: ${this.formatPercent(proof.tolerance)}`,
      '',
      'Differences by Currency:'
    ];

    for (const [currency, diff] of Object.entries(proof.difference)) {
      const type = currency as CurrencyType;
      const value = diff as Decimal;
      const symbol = CurrencySymbols[type];
      
      if (value.abs().gt(0)) {
        const formatted = value.gt(0) ? 
          chalk.red(`+${this.formatDecimal(value)}${symbol}`) :
          chalk.green(`${this.formatDecimal(value)}${symbol}`);
        lines.push(`  ${currency}: ${formatted}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * Format time duration
   */
  static formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds} seconds`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)} minutes`;
    } else if (seconds < 86400) {
      return `${Math.floor(seconds / 3600)} hours`;
    } else {
      return `${Math.floor(seconds / 86400)} days`;
    }
  }
}