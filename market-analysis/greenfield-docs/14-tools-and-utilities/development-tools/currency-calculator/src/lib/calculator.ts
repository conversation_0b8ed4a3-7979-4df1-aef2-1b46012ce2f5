import { Decimal } from 'decimal.js';
import {
  CurrencyType,
  MultiCurrencyAmount,
  ExchangeRate,
  ConservationProof,
  SynergyComponents,
  SynergyResult
} from '../types/currency';
import {
  CURRENCY_PROPERTIES,
  DEFAULT_EXCHANGE_RATES,
  CONSERVATION_TOLERANCE,
  TARGET_SYNERGY
} from './currency-properties';

/**
 * Core calculator for multi-dimensional currency operations
 */
export class CurrencyCalculator {
  private exchangeRates: Map<string, Decimal>;

  constructor() {
    this.exchangeRates = new Map(Object.entries(DEFAULT_EXCHANGE_RATES));
  }

  /**
   * Create an empty multi-currency amount
   */
  createEmptyAmount(): MultiCurrencyAmount {
    return {
      [CurrencyType.ECONOMIC]: new Decimal(0),
      [CurrencyType.QUALITY]: new Decimal(0),
      [CurrencyType.TEMPORAL]: new Decimal(0),
      [CurrencyType.RELIABILITY]: new Decimal(0),
      [CurrencyType.INNOVATION]: new Decimal(0)
    };
  }

  /**
   * Add two multi-currency amounts
   */
  addAmounts(a: MultiCurrencyAmount, b: MultiCurrencyAmount): MultiCurrencyAmount {
    return {
      [CurrencyType.ECONOMIC]: a.economic.plus(b.economic),
      [CurrencyType.QUALITY]: a.quality.plus(b.quality),
      [CurrencyType.TEMPORAL]: a.temporal.plus(b.temporal),
      [CurrencyType.RELIABILITY]: a.reliability.plus(b.reliability),
      [CurrencyType.INNOVATION]: a.innovation.plus(b.innovation)
    };
  }

  /**
   * Subtract multi-currency amounts (a - b)
   */
  subtractAmounts(a: MultiCurrencyAmount, b: MultiCurrencyAmount): MultiCurrencyAmount {
    return {
      [CurrencyType.ECONOMIC]: a.economic.minus(b.economic),
      [CurrencyType.QUALITY]: a.quality.minus(b.quality),
      [CurrencyType.TEMPORAL]: a.temporal.minus(b.temporal),
      [CurrencyType.RELIABILITY]: a.reliability.minus(b.reliability),
      [CurrencyType.INNOVATION]: a.innovation.minus(b.innovation)
    };
  }

  /**
   * Exchange currency from one type to another
   */
  exchange(amount: Decimal, from: CurrencyType, to: CurrencyType): Decimal {
    if (from === to) {
      return amount;
    }

    // Check if currencies are transferable
    const fromProps = CURRENCY_PROPERTIES[from];
    const toProps = CURRENCY_PROPERTIES[to];

    if (!fromProps.transferable) {
      throw new Error(`${from} currency is not transferable`);
    }

    // Get exchange rate
    const rateKey = `${from}-${to}`;
    const rate = this.exchangeRates.get(rateKey);

    if (!rate) {
      throw new Error(`No exchange rate found for ${from} to ${to}`);
    }

    // Calculate exchanged amount
    const exchanged = amount.mul(rate);

    // Apply bounds
    if (exchanged.lt(toProps.minValue)) {
      return toProps.minValue;
    }
    if (exchanged.gt(toProps.maxValue)) {
      return toProps.maxValue;
    }

    return exchanged;
  }

  /**
   * Calculate total value in a specific currency
   */
  calculateTotalValue(amounts: MultiCurrencyAmount, targetCurrency: CurrencyType): Decimal {
    let total = new Decimal(0);

    for (const [currency, amount] of Object.entries(amounts)) {
      if (currency === targetCurrency) {
        total = total.plus(amount as Decimal);
      } else {
        try {
          const exchanged = this.exchange(amount as Decimal, currency as CurrencyType, targetCurrency);
          total = total.plus(exchanged);
        } catch (e) {
          // Skip non-transferable currencies
        }
      }
    }

    return total;
  }

  /**
   * Apply temporal decay to an amount
   */
  applyTemporalDecay(amount: Decimal, secondsElapsed: number): Decimal {
    const decayRate = CURRENCY_PROPERTIES[CurrencyType.TEMPORAL].decayRate!;
    const decayFactor = Decimal.exp(decayRate.neg().mul(secondsElapsed));
    return amount.mul(decayFactor);
  }

  /**
   * Calculate reliability yield
   */
  calculateReliabilityYield(amount: Decimal, daysHeld: number): Decimal {
    const yieldRate = CURRENCY_PROPERTIES[CurrencyType.RELIABILITY].yieldRate!;
    const annualizedDays = new Decimal(daysHeld).div(365);
    const yieldAmount = amount.mul(yieldRate).mul(annualizedDays);
    return amount.plus(yieldAmount);
  }

  /**
   * Validate conservation law for a transaction
   */
  validateConservation(
    before: MultiCurrencyAmount,
    after: MultiCurrencyAmount,
    fees: MultiCurrencyAmount = this.createEmptyAmount()
  ): ConservationProof {
    // Calculate totals including fees
    const totalBefore = this.addAmounts(before, fees);
    const totalAfter = after;
    const difference = this.subtractAmounts(totalAfter, totalBefore);

    // Check conservation for each currency
    let isValid = true;
    for (const currency of Object.values(CurrencyType)) {
      const diff = difference[currency];
      const tolerance = totalBefore[currency].mul(CONSERVATION_TOLERANCE);
      
      if (diff.abs().gt(tolerance)) {
        isValid = false;
        break;
      }
    }

    return {
      totalBefore,
      totalAfter,
      difference,
      isValid,
      tolerance: CONSERVATION_TOLERANCE
    };
  }

  /**
   * Calculate team synergy based on the five components
   */
  calculateTeamSynergy(components: SynergyComponents): SynergyResult {
    // Apply the synergy formula from the economic constitution
    const synergy = new Decimal(1.0)
      .mul(new Decimal(1).plus(components.skillDiversity.mul(0.4)))      // Up to 40% from skills
      .mul(new Decimal(1).plus(components.knowledgeOverlap.mul(0.3)))    // Up to 30% from knowledge
      .mul(new Decimal(1).plus(components.communicationEfficiency.mul(0.25))) // Up to 25% from communication
      .mul(new Decimal(1).plus(components.creativePotential.mul(0.2)))   // Up to 20% from creativity
      .mul(new Decimal(1).minus(components.coordinationCost));            // Subtract coordination overhead

    // Clamp between 50% and 250%
    const clampedSynergy = synergy.lt(0.5) ? new Decimal(0.5) : 
                          synergy.gt(2.5) ? new Decimal(2.5) : synergy;

    const percentageGain = clampedSynergy.minus(1).mul(100);
    const achievesTarget = clampedSynergy.gte(TARGET_SYNERGY);

    return {
      components,
      totalSynergy: clampedSynergy,
      percentageGain,
      achievesTarget
    };
  }

  /**
   * Estimate optimal team synergy components for target achievement
   */
  estimateOptimalSynergy(): SynergyComponents {
    // These values are calibrated to achieve 194.4% synergy
    return {
      skillDiversity: new Decimal(0.85),      // 85% diverse skills
      knowledgeOverlap: new Decimal(0.75),    // 75% knowledge sharing
      communicationEfficiency: new Decimal(0.80), // 80% communication efficiency
      creativePotential: new Decimal(0.70),   // 70% creative potential
      coordinationCost: new Decimal(0.05)     // 5% coordination overhead
    };
  }

  /**
   * Update exchange rate
   */
  updateExchangeRate(from: CurrencyType, to: CurrencyType, rate: Decimal): void {
    const key = `${from}-${to}`;
    this.exchangeRates.set(key, rate);
    
    // Update reverse rate
    const reverseKey = `${to}-${from}`;
    this.exchangeRates.set(reverseKey, new Decimal(1).div(rate));
  }

  /**
   * Get current exchange rate
   */
  getExchangeRate(from: CurrencyType, to: CurrencyType): ExchangeRate {
    if (from === to) {
      return {
        from,
        to,
        rate: new Decimal(1),
        timestamp: new Date(),
        liquidity: new Decimal('1e9') // High liquidity for same currency
      };
    }

    const key = `${from}-${to}`;
    const rate = this.exchangeRates.get(key);

    if (!rate) {
      throw new Error(`No exchange rate found for ${from} to ${to}`);
    }

    return {
      from,
      to,
      rate,
      timestamp: new Date(),
      liquidity: new Decimal('1e6') // Default liquidity
    };
  }
}