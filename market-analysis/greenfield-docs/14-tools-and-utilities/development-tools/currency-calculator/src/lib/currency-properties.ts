import { Decimal } from 'decimal.js';
import { CurrencyType, CurrencyProperties } from '../types/currency';

/**
 * Define the properties and constraints for each currency type
 * Based on the VibeLaunch Economic Constitution
 */
export const CURRENCY_PROPERTIES: Record<CurrencyType, CurrencyProperties> = {
  [CurrencyType.ECONOMIC]: {
    transferable: true,
    divisible: true,
    minValue: new Decimal(0),
    maxValue: new Decimal('1e18'), // Very large number with 18 decimal precision
  },
  
  [CurrencyType.QUALITY]: {
    transferable: false, // Non-transferable, earned through performance
    divisible: true,
    minValue: new Decimal(0),
    maxValue: new Decimal(2), // Range: 0 to 2
  },
  
  [CurrencyType.TEMPORAL]: {
    transferable: true,
    divisible: true,
    minValue: new Decimal(0),
    maxValue: new Decimal('1e12'),
    decayRate: new Decimal(0.5).div(7 * 24 * 60 * 60), // 50% decay per week (per second)
  },
  
  [CurrencyType.RELIABILITY]: {
    transferable: false, // Non-transferable, represents trust
    divisible: true,
    minValue: new Decimal(0),
    maxValue: new Decimal(1), // Range: 0 to 1
    yieldRate: new Decimal(0.1), // 10% annual yield (average)
  },
  
  [CurrencyType.INNOVATION]: {
    transferable: true,
    divisible: true,
    minValue: new Decimal(0),
    maxValue: new Decimal('1e15'),
  }
};

/**
 * Default exchange rates (these would be dynamic in production)
 * Based on the multi-dimensional value theory
 */
export const DEFAULT_EXCHANGE_RATES = {
  // Economic to others
  [`${CurrencyType.ECONOMIC}-${CurrencyType.QUALITY}`]: new Decimal(1000),
  [`${CurrencyType.ECONOMIC}-${CurrencyType.TEMPORAL}`]: new Decimal(100),
  [`${CurrencyType.ECONOMIC}-${CurrencyType.RELIABILITY}`]: new Decimal(10000),
  [`${CurrencyType.ECONOMIC}-${CurrencyType.INNOVATION}`]: new Decimal(500),
  
  // Quality to others
  [`${CurrencyType.QUALITY}-${CurrencyType.ECONOMIC}`]: new Decimal(0.001),
  [`${CurrencyType.QUALITY}-${CurrencyType.TEMPORAL}`]: new Decimal(0.1),
  [`${CurrencyType.QUALITY}-${CurrencyType.RELIABILITY}`]: new Decimal(10),
  [`${CurrencyType.QUALITY}-${CurrencyType.INNOVATION}`]: new Decimal(0.5),
  
  // Temporal to others
  [`${CurrencyType.TEMPORAL}-${CurrencyType.ECONOMIC}`]: new Decimal(0.01),
  [`${CurrencyType.TEMPORAL}-${CurrencyType.QUALITY}`]: new Decimal(10),
  [`${CurrencyType.TEMPORAL}-${CurrencyType.RELIABILITY}`]: new Decimal(100),
  [`${CurrencyType.TEMPORAL}-${CurrencyType.INNOVATION}`]: new Decimal(5),
  
  // Reliability to others
  [`${CurrencyType.RELIABILITY}-${CurrencyType.ECONOMIC}`]: new Decimal(0.0001),
  [`${CurrencyType.RELIABILITY}-${CurrencyType.QUALITY}`]: new Decimal(0.1),
  [`${CurrencyType.RELIABILITY}-${CurrencyType.TEMPORAL}`]: new Decimal(0.01),
  [`${CurrencyType.RELIABILITY}-${CurrencyType.INNOVATION}`]: new Decimal(0.05),
  
  // Innovation to others
  [`${CurrencyType.INNOVATION}-${CurrencyType.ECONOMIC}`]: new Decimal(0.002),
  [`${CurrencyType.INNOVATION}-${CurrencyType.QUALITY}`]: new Decimal(2),
  [`${CurrencyType.INNOVATION}-${CurrencyType.TEMPORAL}`]: new Decimal(0.2),
  [`${CurrencyType.INNOVATION}-${CurrencyType.RELIABILITY}`]: new Decimal(20),
};

/**
 * Conservation tolerance (0.0001% as per requirements)
 */
export const CONSERVATION_TOLERANCE = new Decimal(0.000001);

/**
 * Target synergy multiplier (194.4%)
 */
export const TARGET_SYNERGY = new Decimal(1.944);

/**
 * Maximum team size for optimal synergy
 */
export const OPTIMAL_TEAM_SIZE = 5;