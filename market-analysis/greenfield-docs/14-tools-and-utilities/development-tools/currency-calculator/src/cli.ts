#!/usr/bin/env node

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import { Decimal } from 'decimal.js';
import { CurrencyCalculator } from './lib/calculator';
import { <PERSON><PERSON>rencyFormatter } from './lib/formatter';
import { CurrencyType, SynergyComponents } from './types/currency';
import { TARGET_SYNERGY } from './lib/currency-properties';

const calculator = new CurrencyCalculator();
const program = new Command();

program
  .name('vibelaunch-calc')
  .description('Interactive CLI for VibeLaunch\'s five-dimensional currency calculations')
  .version('1.0.0');

// Exchange command
program
  .command('exchange')
  .description('Calculate currency exchange between dimensions')
  .option('-a, --amount <amount>', 'Amount to exchange')
  .option('-f, --from <currency>', 'Source currency')
  .option('-t, --to <currency>', 'Target currency')
  .action(async (options) => {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'amount',
        message: 'Enter amount to exchange:',
        default: options.amount || '100',
        validate: (input) => !isNaN(parseFloat(input)) || 'Please enter a valid number'
      },
      {
        type: 'list',
        name: 'from',
        message: 'Select source currency:',
        choices: Object.values(CurrencyType),
        default: options.from
      },
      {
        type: 'list',
        name: 'to',
        message: 'Select target currency:',
        choices: Object.values(CurrencyType),
        default: options.to
      }
    ]);

    const spinner = ora('Calculating exchange...').start();
    
    try {
      const amount = new Decimal(answers.amount);
      const result = calculator.exchange(amount, answers.from, answers.to);
      const rate = calculator.getExchangeRate(answers.from, answers.to);
      
      spinner.succeed('Exchange calculated successfully!');
      console.log('\n' + chalk.bold('Exchange Result:'));
      console.log(CurrencyFormatter.formatCurrency(amount, answers.from) + ' → ' + 
                  CurrencyFormatter.formatCurrency(result, answers.to));
      console.log('Rate: ' + CurrencyFormatter.formatExchangeRate(answers.from, answers.to, rate.rate));
    } catch (error: any) {
      spinner.fail('Exchange failed: ' + error.message);
    }
  });

// Synergy command
program
  .command('synergy')
  .description('Calculate team synergy and check if it achieves 194.4% target')
  .option('-i, --interactive', 'Interactive mode to input synergy components')
  .option('-o, --optimal', 'Show optimal synergy configuration')
  .action(async (options) => {
    if (options.optimal) {
      const optimal = calculator.estimateOptimalSynergy();
      const result = calculator.calculateTeamSynergy(optimal);
      console.log('\n' + chalk.bold('Optimal Team Configuration for 194.4% Synergy:'));
      console.log(CurrencyFormatter.formatSynergyResult(result));
      return;
    }

    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'skillDiversity',
        message: 'Enter skill diversity (0-1):',
        default: '0.8',
        validate: (input) => {
          const val = parseFloat(input);
          return (val >= 0 && val <= 1) || 'Please enter a value between 0 and 1';
        }
      },
      {
        type: 'input',
        name: 'knowledgeOverlap',
        message: 'Enter knowledge overlap (0-1):',
        default: '0.7',
        validate: (input) => {
          const val = parseFloat(input);
          return (val >= 0 && val <= 1) || 'Please enter a value between 0 and 1';
        }
      },
      {
        type: 'input',
        name: 'communicationEfficiency',
        message: 'Enter communication efficiency (0-1):',
        default: '0.75',
        validate: (input) => {
          const val = parseFloat(input);
          return (val >= 0 && val <= 1) || 'Please enter a value between 0 and 1';
        }
      },
      {
        type: 'input',
        name: 'creativePotential',
        message: 'Enter creative potential (0-1):',
        default: '0.65',
        validate: (input) => {
          const val = parseFloat(input);
          return (val >= 0 && val <= 1) || 'Please enter a value between 0 and 1';
        }
      },
      {
        type: 'input',
        name: 'coordinationCost',
        message: 'Enter coordination cost (0-1):',
        default: '0.1',
        validate: (input) => {
          const val = parseFloat(input);
          return (val >= 0 && val <= 1) || 'Please enter a value between 0 and 1';
        }
      }
    ]);

    const components: SynergyComponents = {
      skillDiversity: new Decimal(answers.skillDiversity),
      knowledgeOverlap: new Decimal(answers.knowledgeOverlap),
      communicationEfficiency: new Decimal(answers.communicationEfficiency),
      creativePotential: new Decimal(answers.creativePotential),
      coordinationCost: new Decimal(answers.coordinationCost)
    };

    const spinner = ora('Calculating team synergy...').start();
    const result = calculator.calculateTeamSynergy(components);
    spinner.succeed('Synergy calculated successfully!');
    
    console.log('\n' + CurrencyFormatter.formatSynergyResult(result));
  });

// Decay command
program
  .command('decay')
  .description('Calculate temporal currency decay over time')
  .action(async () => {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'amount',
        message: 'Enter temporal currency amount:',
        default: '1000',
        validate: (input) => !isNaN(parseFloat(input)) || 'Please enter a valid number'
      },
      {
        type: 'input',
        name: 'days',
        message: 'Enter number of days elapsed:',
        default: '7',
        validate: (input) => !isNaN(parseInt(input)) || 'Please enter a valid number'
      }
    ]);

    const amount = new Decimal(answers.amount);
    const seconds = parseInt(answers.days) * 24 * 60 * 60;
    
    const spinner = ora('Calculating decay...').start();
    const decayed = calculator.applyTemporalDecay(amount, seconds);
    const lost = amount.minus(decayed);
    const percentLost = lost.div(amount).mul(100);
    
    spinner.succeed('Decay calculated successfully!');
    
    console.log('\n' + chalk.bold('Temporal Currency Decay:'));
    console.log(`Original: ${CurrencyFormatter.formatCurrency(amount, CurrencyType.TEMPORAL)}`);
    console.log(`After ${answers.days} days: ${CurrencyFormatter.formatCurrency(decayed, CurrencyType.TEMPORAL)}`);
    console.log(`Amount lost: ${chalk.red(CurrencyFormatter.formatCurrency(lost, CurrencyType.TEMPORAL))}`);
    console.log(`Percentage lost: ${chalk.red(CurrencyFormatter.formatDecimal(percentLost) + '%')}`);
  });

// Yield command
program
  .command('yield')
  .description('Calculate reliability currency yield over time')
  .action(async () => {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'amount',
        message: 'Enter reliability currency amount:',
        default: '0.8',
        validate: (input) => {
          const val = parseFloat(input);
          return (val >= 0 && val <= 1) || 'Please enter a value between 0 and 1';
        }
      },
      {
        type: 'input',
        name: 'days',
        message: 'Enter number of days held:',
        default: '365',
        validate: (input) => !isNaN(parseInt(input)) || 'Please enter a valid number'
      }
    ]);

    const amount = new Decimal(answers.amount);
    const days = parseInt(answers.days);
    
    const spinner = ora('Calculating yield...').start();
    const withYield = calculator.calculateReliabilityYield(amount, days);
    const gained = withYield.minus(amount);
    const percentGain = gained.div(amount).mul(100);
    
    spinner.succeed('Yield calculated successfully!');
    
    console.log('\n' + chalk.bold('Reliability Currency Yield:'));
    console.log(`Original: ${CurrencyFormatter.formatCurrency(amount, CurrencyType.RELIABILITY)}`);
    console.log(`After ${days} days: ${CurrencyFormatter.formatCurrency(withYield, CurrencyType.RELIABILITY)}`);
    console.log(`Amount gained: ${chalk.green(CurrencyFormatter.formatCurrency(gained, CurrencyType.RELIABILITY))}`);
    console.log(`Percentage gain: ${chalk.green('+' + CurrencyFormatter.formatDecimal(percentGain) + '%')}`);
  });

// Conservation command
program
  .command('conservation')
  .description('Validate conservation law for a transaction')
  .action(async () => {
    console.log(chalk.bold('\nEnter transaction details to validate conservation law\n'));
    
    const currencies = Object.values(CurrencyType);
    const before = calculator.createEmptyAmount();
    const after = calculator.createEmptyAmount();
    
    console.log(chalk.underline('Before transaction:'));
    for (const currency of currencies) {
      const answer = await inquirer.prompt({
        type: 'input',
        name: 'amount',
        message: `${currency} amount:`,
        default: '0',
        validate: (input) => !isNaN(parseFloat(input)) || 'Please enter a valid number'
      });
      before[currency] = new Decimal(answer.amount);
    }
    
    console.log('\n' + chalk.underline('After transaction:'));
    for (const currency of currencies) {
      const answer = await inquirer.prompt({
        type: 'input',
        name: 'amount',
        message: `${currency} amount:`,
        default: before[currency].toString(),
        validate: (input) => !isNaN(parseFloat(input)) || 'Please enter a valid number'
      });
      after[currency] = new Decimal(answer.amount);
    }
    
    const spinner = ora('Validating conservation law...').start();
    const proof = calculator.validateConservation(before, after);
    
    if (proof.isValid) {
      spinner.succeed('Conservation law validated!');
    } else {
      spinner.fail('Conservation law violated!');
    }
    
    console.log('\n' + CurrencyFormatter.formatConservationProof(proof));
  });

// Interactive mode
program
  .command('interactive')
  .description('Start interactive calculator mode')
  .action(async () => {
    console.log(chalk.bold.cyan('\nWelcome to VibeLaunch Currency Calculator!\n'));
    console.log('This tool helps you work with the five-dimensional currency system:');
    console.log('- Economic (₥): Traditional monetary value');
    console.log('- Quality (◈): Technical excellence (0-2 range)');
    console.log('- Temporal (⧗): Time efficiency (decays over time)');
    console.log('- Reliability (☆): Trust and consistency (0-1 range, generates yield)');
    console.log('- Innovation (◊): Creative solutions\n');

    let continueLoop = true;
    
    while (continueLoop) {
      const { operation } = await inquirer.prompt({
        type: 'list',
        name: 'operation',
        message: 'What would you like to calculate?',
        choices: [
          { name: 'Exchange currencies', value: 'exchange' },
          { name: 'Calculate team synergy', value: 'synergy' },
          { name: 'Calculate temporal decay', value: 'decay' },
          { name: 'Calculate reliability yield', value: 'yield' },
          { name: 'Validate conservation law', value: 'conservation' },
          { name: 'Exit', value: 'exit' }
        ]
      });

      if (operation === 'exit') {
        continueLoop = false;
        console.log(chalk.green('\nThank you for using VibeLaunch Currency Calculator!'));
      } else {
        // Execute the selected operation
        await program.parseAsync(['node', 'cli.js', operation], { from: 'user' });
        console.log('\n' + chalk.dim('─'.repeat(50)) + '\n');
      }
    }
  });

// Parse command line arguments
program.parse(process.argv);

// If no command specified, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}