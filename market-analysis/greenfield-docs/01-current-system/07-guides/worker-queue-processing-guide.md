# Worker & Queue Processing: Current State Analysis

*Last Updated: June 17, 2025*

## 1. Executive Summary

This document provides a factual analysis of the current state of background workers and queue processing in the VibeLaunch platform. It replaces the previous guide, which described a robust webhook-based queue system.

**Crucially, the `webhook_queue` table and the associated worker processing system described in the previous documentation do not exist in the current implementation.** The entire guide was aspirational and did not reflect the production environment.

## 2. The Non-Existent Worker/Queue System

Codebase and database schema analysis confirms the following:

*   **No `webhook_queue` Table**: There is no table named `webhook_queue` in the database schema. The `CREATE TABLE` statements found in the previous guide do not correspond to any active migration script.
*   **No Worker Process**: The worker implementation detailed in `packages/worker/src/webhook-processor.ts` is not actively used or deployed. It appears to be scaffolded code for a system that was never fully implemented.
*   **No Dead Letter Queue**: The `dead_letter_messages` table is also not present in the database.

The previous documentation, including detailed code examples for a webhook processor, retry logic, and monitoring dashboards, described a system that was planned or desired, but **not built**.

## 3. The Current Reality: Processing via Fragmented Events

All background and asynchronous processing in the current system is handled directly by consumers of the fragmented event systems. There is no intermediate queue.

*   **How it Works**: When an event is published (e.g., via `bus_notify` or a direct Supabase `broadcast`), a listener immediately picks it up and processes it in the same thread.
*   **Primary Handlers**:
    *   **Supabase Edge Functions**: These serverless functions are often triggered in response to database webhooks or other events.
    *   **Backend Services**: The main backend application listens for Supabase Realtime events and processes them as they arrive.
*   **Data Flow Example**:
    1.  A user posts a chat message.
    2.  The backend calls the `bus_notify` PostgreSQL function.
    3.  `bus_notify` sends a notification via `pg_notify`.
    4.  A database trigger relays this notification to the Supabase Realtime "bus" channel.
    5.  A subscribed backend service (e.g., the `agent-service`) receives the `chat_posted` event and immediately begins processing it.

## 4. Implications of the Current System

The lack of a dedicated queueing system has significant consequences:

*   **No Retry Logic**: If a consumer fails to process an event, the event is lost. There is no built-in mechanism for retries with exponential backoff as described in the aspirational documentation.
*   **No Dead Letter Queue**: Failed events are not stored for later inspection or reprocessing. They are permanently lost.
*   **Limited Scalability**: The system cannot absorb spikes in traffic. A sudden burst of events could overwhelm consumers, leading to dropped events or service degradation.
*   **Poor Observability**: It is difficult to monitor the health of background processing. There is no central queue to measure depth, processing latency, or failure rates.
*   **Tight Coupling**: Publishers and consumers are tightly coupled. The system relies on consumers being available and able to process events in real-time.

## 5. Conclusion

The VibeLaunch platform currently **lacks a formal background worker and queueing system**. All asynchronous tasks are handled directly by event listeners. The previous documentation was entirely aspirational and has been removed to prevent further confusion.

This guide should be considered the single source of truth. Any future development of a worker system should begin from the understanding that no such system currently exists.