# Multi-Tenant Architecture Deep Dive (Current Implementation)

**Warning: This document describes the current, critically flawed implementation of the multi-tenant architecture. It is intended to be a brutally honest technical assessment to inform immediate remediation efforts. Do not use this document as a guide for best practices.**

*Last updated: June 17, 2025*

## 1. Executive Summary: Critical Risk

The VibeLaunch multi-tenant architecture is in a **critically vulnerable state**. The implementation deviates significantly from the original design and contains severe security flaws that undermine tenant isolation.

- **Missing RLS Policies:** The documented RLS policies based on `organisation_members` are **not implemented**.
- **Unstable Core Schema:** The actual tenancy model relies on an ad-hoc `public.organisations_users` table with no formal schema management.
- **Blanket RLS Bypass:** A `service_role` bypass exists for all RLS, creating a single point of failure for the entire security model.

This document details the system as it currently exists.

## 2. The Real RLS Implementation

Tenant isolation is not governed by the intended `organisation_members` table. Instead, a fragile, two-table system is in place.

### 2.1. The `organisations_users` Table

The core of the tenancy model is the `public.organisations_users` table. This table is not formally managed and has been created and modified across multiple, conflicting migration files.

**Schema (as of latest migration):**
```sql
-- Defined in `20250515170901_foggy_union.sql` and `20250515172900_divine_field.sql`
CREATE TABLE IF NOT EXISTS organisations_users (
  organisation_id uuid REFERENCES organisations(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  role text DEFAULT 'member',
  PRIMARY KEY (organisation_id, user_id)
);
```

**Vulnerability: Unmanaged and Fragile Schema**
The schema for `organisations_users` is not defined in a single, authoritative source. It is created `IF NOT EXISTS` in multiple migration scripts (e.g., [`20250515170901_foggy_union.sql`](supabase/migrations/20250515170901_foggy_union.sql:33) and [`20250515172900_divine_field.sql`](supabase/migrations/20250515172900_divine_field.sql:25)). This ad-hoc approach makes the core of the security model unstable and prone to silent failures if migrations are run out of order or fail partially.

### 2.2. Implemented RLS Policies

The actual RLS policies in effect are based on this unstable `organisations_users` table.

**Example: `bus_events` RLS Policy**
```sql
-- From `20250617_fix_service_role_rls_all_tables.sql`
CREATE POLICY "Authenticated users can read events" ON public.bus_events
  FOR SELECT
  USING (
    auth.role() = 'authenticated'
    AND organisation_id IN (
      SELECT organisation_id::TEXT FROM public.organisations_users
      WHERE user_id = auth.uid()
    )
  );
```
This policy correctly attempts to limit access based on `organisations_users`, but it is built on a fragile foundation.

## 3. Critical Vulnerability: `service_role` RLS Bypass

The most severe vulnerability is a blanket bypass of all Row Level Security for any request using the `service_role` key.

### 3.1. The Bypass Policy

The migration [`20250617_fix_service_role_rls_all_tables.sql`](supabase/migrations/20250617_fix_service_role_rls_all_tables.sql) introduced a permissive policy that grants god-mode access to the `service_role`.

```sql
-- From `20250617_fix_service_role_rls_all_tables.sql`
CREATE POLICY "Service role bypass RLS" ON public.bus_events
  AS PERMISSIVE
  FOR ALL
  USING (
    (SELECT current_setting('request.jwt.claims', true)::json->>'role') = 'service_role'
  )
  WITH CHECK (
    (SELECT current_setting('request.jwt.claims', true)::json->>'role') = 'service_role'
  );
```
This exact policy has been applied to `bus_events`, `thoughts`, and `webhook_queue`, effectively disabling RLS for any process using the `service_role` key.

### 3.2. Risk Analysis: Single Point of Failure

This bypass represents a **total failure of the tenant isolation model**. If the `service_role` key is compromised, an attacker gains unrestricted access to all data for all tenants across the entire platform.

- **No Defense in Depth:** The RLS bypass is the only check. There are no secondary validation layers.
- **Violation of Least Privilege:** The `service_role` is used for system operations that should not require a full RLS bypass. This is a fundamental design flaw.
- **High-Value Target:** The `service_role` key is now the single most valuable secret in the system.

## 4. Remediation Priorities

This is not an exhaustive list, but represents the immediate, critical steps required to begin remediation.

1.  **Remove the `service_role` RLS Bypass Immediately.** This is the highest priority. All uses of the `service_role` key must be audited and replaced with more granular, secure access patterns, such as `SECURITY DEFINER` functions that perform their own permission checks.
2.  **Formalize the `organisations_users` Schema.** Create a single, authoritative migration to define the `organisations_users` table. Remove the ad-hoc `CREATE TABLE IF NOT EXISTS` from all other migration files.
3.  **Re-implement RLS Policies.** Once the schema is stable, review and re-implement all RLS policies to be secure, efficient, and based on the authoritative schema.
4.  **Full Security Audit.** A complete audit of all data access patterns is required to identify any other areas where tenant isolation is compromised.

This document should be updated as remediation proceeds.