# Event System: Current State Analysis

*Last Updated: June 17, 2025*

## 1. Executive Summary

The VibeLaunch event system is currently in a state of fragmentation and partial migration. Three distinct event mechanisms are active in the codebase, leading to complexity and a lack of a unified event-handling strategy. An effort to consolidate these systems is underway but is not complete.

This document provides a factual overview of the system as it exists today, replacing the previous aspirational guide that recommended a simplified architecture not yet achieved.

## 2. The Three Active Event Systems

Analysis of the codebase reveals three co-existing event systems, each used in different contexts.

### System 1: PostgreSQL `NOTIFY/LISTEN`

This mechanism is used for database-internal eventing, typically inside triggers. It allows for decoupled processing within the database itself.

*   **Mechanism**: A PostgreSQL session can send a notification payload on a specific channel, and any session listening on that channel will receive it.
*   **Typical Usage**: Used in database functions or triggers to signal that data has changed, without the function needing to know what process will handle the event.
*   **Example Invocation**:
    ```sql
    -- A function notifies that a contract has been published
    PERFORM pg_notify('contract_published', '{"contract_id": "...", "status": "published"}');
    ```

### System 2: Supabase Realtime (Broadcast)

Supabase Realtime is the primary mechanism for pushing events from the backend to subscribed clients, most notably the UI. It is used extensively for real-time updates.

*   **Mechanism**: The backend sends a message on a named Supabase channel. Any client (e.g., a web browser) subscribed to that channel receives the message as a broadcast event.
*   **Typical Usage**: Informing the UI of state changes, such as a new chat message, a task update, or a change in presence.
*   **Example Invocation**:
    ```typescript
    // Sending a bus event via a Supabase Realtime channel
    const channel = supabase.channel('bus');
    await channel.send({
      type: 'broadcast',
      event: 'chat_posted',
      payload: { message: 'Hello, world!' }
    });
    ```

### System 3: Redis Streams (Implemented but Inactive)

A complete implementation for an event system using Redis Streams exists within the `packages/redis-streams/` directory. However, it is **not currently integrated** into the main application flow.

*   **Mechanism**: Provides a robust, scalable event queue with features like consumer groups and message persistence.
*   **Status**: The code is present and appears functional, but no other part of the system actively publishes to or consumes from these streams. It represents a potential future direction, not a current reality.
*   **Example (from codebase, but not in use)**:
    ```typescript
    // Code exists to publish events, but is not called by core services
    await redis.xadd('vibelaunch:events', '*',
      'event', 'user_registered',
      'data', JSON.stringify({ userId: '...' })
    );
    ```

## 3. The `bus_notify` Consolidation Attempt

An undocumented effort is underway to consolidate event creation through a single PostgreSQL RPC function named `bus_notify`. This function acts as a unified entry point for publishing events, which are then propagated through the Supabase Realtime channel.

*   **Mechanism**: Instead of calling `pg_notify` or `channel.send()` directly, services call the `bus_notify` function in the database. This function is responsible for creating the event payload and using `pg_notify` to trigger its broadcast via Supabase.
*   **Status**: This migration is partial. Code analysis shows both direct calls to Supabase `broadcast` and calls to the new `bus_notify` function. There is no clear rule for when to use which.
*   **Example Invocation**:
    ```sql
    -- Calling the consolidated function
    SELECT bus_notify(
      'chat_posted',
      '{"message_id": "...", "content": "..."}',
      '{"organisation_id": "..."}'
    );
    ```

## 4. Summary of Issues

The current event system presents several challenges for developers:

*   **Fragmentation**: Three different systems make it difficult to trace the flow of events and understand the full picture.
*   **Confusion**: Developers must choose between `pg_notify`, direct Supabase `broadcast`, and the `bus_notify` function, with no clear guidance.
*   **Incomplete Migration**: The `bus_notify` consolidation is a work in progress, adding another layer of complexity during the transition.
*   **Dead Code**: The Redis Streams implementation is unused, potentially misleading developers into thinking it is an active part of the system.

This document should serve as the single source of truth for the event system's current, complex reality. All aspirational plans have been removed to provide a clear and accurate picture for the development team.