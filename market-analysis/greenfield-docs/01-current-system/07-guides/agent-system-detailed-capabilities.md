# AI Agent System Technical Guide

*Last updated: June 17, 2025*

## 1. Overview

This document provides a technical guide to the VibeLaunch AI Agent System. It describes the system's architecture as implemented in the codebase, focusing on the event-driven, registry-based model that has replaced the previously documented class-based approach. This guide is intended for developers who need to understand, maintain, or extend the agent system.

**Note:** This document supersedes any previous versions that described a class-based architecture with specialized agent classes (e.g., `ContentCreatorAgent`, `SEOSpecialistAgent`) and a detailed `agent_performance` database table. That model is not implemented in the current system.

## 2. System Architecture

The AI Agent System is designed as an event-driven, distributed network of services coordinated by a central `MasterAgent`. Instead of a rigid class hierarchy, the system uses a dynamic `AgentRegistry` to discover and invoke agents based on their advertised capabilities.

### Key Components:

*   **Master Agent (`master-agent.ts`):** The central orchestrator that processes incoming user requests, determines the required capabilities, and coordinates the execution of tasks across one or more specialized agents.
*   **Agent Registry (`agent-registry.ts`):** A service that maintains a dynamic, in-memory directory of all available agents. It loads agent definitions from the database and tracks their status.
*   **Specialized Agents:** Independent services that register their capabilities with the `AgentRegistry`. In the current implementation, these are not distinct classes but are dynamically created based on LLM configurations in the database.
*   **MCP (Master Control Protocol):** The communication layer that enables the `MasterAgent` to send commands (e.g., `tool_call`) and receive results from specialized agents.

### Architectural Diagram

```
+-----------------+      +---------------------+      +-----------------------+
|   User Request  |----->|    Master Agent     |----->|   MCP Event Broker    |
+-----------------+      | (Orchestrator)      |      +-----------------------+
                         +----------+----------+                 |
                                    |                            |
                                    | (1) Find Agents            | (3) Tool Call
                                    v                            v
                         +----------+----------+      +-----------------------+
                         |   Agent Registry    |<-----|   Specialized Agent   |
                         +---------------------+      | (e.g., OpenAI Service)|
                         | - Agent ID          |      +-----------------------+
                         | - Capabilities      |                 |
                         | - Status            |                 | (4) Tool Result
                         | - Service Endpoint  |                 |
                         +---------------------+                 |
                                    ^                            |
                                    | (2) Load Agents            |
                                    |                            |
                         +----------+----------+      +-----------+-----------+
                         |      Database       |----->|   MCP Event Broker    |
                         | (agent_registry)    |      +-----------------------+
                         +---------------------+                 ^
                                                                 |
                                                                 |
                                     +---------------------------+
                                     | (5) Synthesize & Respond
                                     v
                         +----------+----------+
                         |      User Response    |
                         +---------------------+
```

## 3. Core Components in Detail

### 3.1. Master Agent

The `MasterAgent` is the heart of the system, but it does not contain business logic for specialized tasks. Its primary responsibilities are:

*   **Event-Driven Processing:** It listens for `task` events from the MCP and `chat_posted` events from the bus. It does not operate on a simple request/response cycle.
*   **Task Analysis:** It analyzes incoming user requests to determine the required capabilities (e.g., `content_creation`, `marketing_strategy`).
*   **Agent Discovery:** It queries the `AgentRegistry` to find online agents that possess the required capabilities.
*   **Tool Invocation:** It dispatches work to specialized agents by sending `tool_call` messages via the MCP. The `tool_call` specifies the agent's service name and the parameters for the task.
*   **Result Synthesis:** It listens for `tool_result` messages, aggregates the outputs from the specialized agents, and synthesizes a final response for the user.

#### Code Example: `master-agent.ts`

The `MasterAgent` is not a class that is instantiated per request. It is a long-running process that listens for events. The following snippet illustrates its event-driven nature:

```typescript
// packages/agent/src/master-agent.ts

export class MasterAgent {
  private registry: AgentRegistry;
  // ...

  constructor() {
    this.registry = new AgentRegistry();
    this.initializeMCPListeners(); // Listens for task, tool_result, etc.
    this.initializeBusEventListeners(); // Listens for chat_posted
  }

  public async handleTaskRequest(message: MCPMessage<TaskMessage>): Promise<void> {
    // 1. Analyze task to determine capabilities
    const requiredCapabilities = this.analyzeTaskCapabilities(message.data.input);

    // 2. Find suitable agents in the registry
    const availableAgents = this.registry.findAgentsWithCapabilities(requiredCapabilities);

    // 3. For each agent, invoke their service via a tool_call
    for (const agent of availableAgents) {
      await this.invokeAgent(pipelineRun, agent);
    }
  }

  private async invokeAgent(pipelineRun: PipelineRun, agent: AgentMetadata): Promise<void> {
    const toolCall: MCPMessage<ToolCallRequestData> = {
      // ...
      data: {
        id: toolCallId,
        name: agent.service, // The service to call, e.g., "content_specialist"
        params: { /* ... */ }
      }
    };
    mcpConnector.send<ToolCallRequestData>(MCPMessageType.tool_call, toolCall.data);
  }

  private async handleToolResult(message: MCPMessage<CallToolResult<unknown>>): Promise<void> {
    // Process the result and check if all agents have responded
    // ...
  }
}
```

### 3.2. Agent Registry

The `AgentRegistry` is a dynamic service discovery mechanism. It is responsible for:

*   **Loading Agents:** On initialization, it loads agent definitions from the database. It does not use a hardcoded list of agent classes. Agents are dynamically created based on the LLM configurations stored in the `org_llm_credentials` and `org_llm_settings` tables.
*   **Capability Mapping:** It determines an agent's capabilities based on its LLM provider and model (e.g., an OpenAI GPT-4 model is assigned `content_creation` and `seo_optimization` capabilities).
*   **Status Tracking:** It tracks the `online`/`offline` status of agents via a heartbeat mechanism.

#### Code Example: `agent-registry.ts`

The registry maintains an in-memory map of agents, which can be queried by the `MasterAgent`.

```typescript
// packages/agent/src/agent-registry.ts

export class AgentRegistry {
  private agents: Map<string, AgentMetadata> = new Map();

  private async loadAgentsFromDatabase(): Promise<void> {
    // Fetches LLM configurations from the database
    // and dynamically creates agent definitions.
    // ...
  }

  public findAgentsWithCapabilities(capabilities: AgentCapability[]): AgentMetadata[] {
    return Array.from(this.agents.values())
      .filter(agent =>
        agent.status === 'online' &&
        capabilities.every(cap => agent.capabilities.includes(cap))
      );
  }
}
```

## 4. Database Schema

The database schema supporting the agent system is significantly simpler than what was previously documented. It is designed to be a lightweight state store, not a comprehensive performance tracking system.

### `agent_registry` Table

This table is the only one directly related to agent registration and status. It is extremely simple and is used to track the basic state of each agent role.

```sql
-- supabase/migrations/20250520_agent_market_system.sql

CREATE TABLE IF NOT EXISTS agent_registry (
  agent_role TEXT PRIMARY KEY,
  status agent_status NOT NULL DEFAULT 'idle', -- ENUM ('idle', 'running', 'error')
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

### Non-Existent Tables

The following tables, which were part of the old documentation, **do not exist** in the current implementation:

*   `agent_performance`: There is no dedicated table for tracking agent performance metrics like success rate, response time, or token usage.
*   A detailed `agent_registry` with columns for `max_capacity`, `success_rate`, `avg_response_time`, etc., is not implemented. The actual table is much simpler, as shown above.

## 5. Agent Invocation Flow

The process of invoking an agent and getting a result follows these steps:

1.  **Task Arrival:** The `MasterAgent` receives a task, either from a direct `task` event or a `chat_posted` event on the bus.
2.  **Capability Analysis:** The `MasterAgent` analyzes the task's requirements and determines the necessary capabilities (e.g., `content_creation`).
3.  **Agent Discovery:** It queries the `AgentRegistry` for all online agents that have the required capabilities.
4.  **Tool Call Dispatch:** For each selected agent, the `MasterAgent` constructs and sends a `tool_call` message via the MCP. The `name` of the tool call corresponds to the agent's registered service name.
5.  **Agent Execution:** The specialized agent (which is listening for its service name) receives the `tool_call`, executes the task, and generates a result.
6.  **Result Return:** The agent sends its result back to the `MasterAgent` in a `tool_result` message.
7.  **Synthesis:** The `MasterAgent` collects the results from all invoked agents. Once all have responded, it synthesizes them into a final, user-facing response.
8.  **Response Delivery:** The final response is sent back to the user via the appropriate chat channel.

## 6. Conclusion

The current AI Agent System is a flexible, event-driven architecture that leverages a dynamic registry for service discovery. It is not based on a rigid, class-based inheritance model. Developers working with the system should focus on the `MasterAgent` as the central orchestrator, the `AgentRegistry` for service discovery, and the MCP for communication between components. The database serves as a simple state store, and there is no built-in performance tracking at the database level.