# Load Testing

## Overview

Load testing validates VibeLaunch's ability to handle 10,000+ transactions per second while maintaining 95%+ efficiency. These tests progressively stress the system to identify performance limits and ensure graceful degradation under extreme load.

## Test Scenarios

### 1. Baseline Performance Tests

```typescript
import { check, sleep } from 'k6';
import http from 'k6/http';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const contractCreationTime = new Trend('contract_creation_time');
const bidSubmissionTime = new Trend('bid_submission_time');
const marketEfficiency = new Trend('market_efficiency');

export const options = {
  stages: [
    { duration: '2m', target: 100 },    // Warm up
    { duration: '5m', target: 1000 },   // Normal load
    { duration: '10m', target: 5000 },  // Peak load
    { duration: '10m', target: 10000 }, // Stress test
    { duration: '5m', target: 0 },      // Cool down
  ],
  thresholds: {
    http_req_duration: ['p(95)<10'], // 95% requests under 10ms
    'http_req_duration{type:critical}': ['p(99)<15'], // Critical paths under 15ms
    errors: ['rate<0.01'], // Error rate under 1%
    'market_efficiency': ['avg>0.95'], // Maintain 95%+ efficiency
  },
};

export default function () {
  const baseUrl = __ENV.API_URL || 'http://localhost:3000';
  
  // Simulate realistic user behavior
  const scenario = Math.random();
  
  if (scenario < 0.3) {
    // Contract creation flow (30%)
    contractCreationFlow(baseUrl);
  } else if (scenario < 0.8) {
    // Bidding flow (50%)
    biddingFlow(baseUrl);
  } else {
    // Market analysis flow (20%)
    marketAnalysisFlow(baseUrl);
  }
  
  sleep(Math.random() * 2 + 1); // Random think time
}

function contractCreationFlow(baseUrl) {
  const startTime = Date.now();
  
  // 1. Create contract
  const contractPayload = {
    title: `Load Test Contract ${__VU}_${__ITER}`,
    requirements: ['content_creation', 'seo', 'social_media'],
    budget: {
      economic: Math.floor(Math.random() * 20000) + 5000,
      quality: 1.0 + Math.random() * 0.8,
      temporal: Math.floor(Math.random() * 1000),
      reliability: 0.7 + Math.random() * 0.3,
      innovation: Math.floor(Math.random() * 500)
    },
    deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
  };
  
  const createResponse = http.post(
    `${baseUrl}/api/v1/contracts`,
    JSON.stringify(contractPayload),
    {
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      tags: { type: 'critical' }
    }
  );
  
  check(createResponse, {
    'contract created': (r) => r.status === 201,
    'has contract id': (r) => r.json('id') !== undefined,
    'escrow created': (r) => r.json('escrowId') !== undefined,
  });
  
  contractCreationTime.add(Date.now() - startTime);
  errorRate.add(createResponse.status !== 201);
  
  if (createResponse.status === 201) {
    const contractId = createResponse.json('id');
    
    // 2. Publish contract
    const publishResponse = http.put(
      `${baseUrl}/api/v1/contracts/${contractId}/publish`,
      JSON.stringify({ autoSelect: true }),
      {
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      }
    );
    
    check(publishResponse, {
      'contract published': (r) => r.status === 200,
      'market analysis complete': (r) => r.json('marketAnalysis') !== undefined,
    });
  }
}

function biddingFlow(baseUrl) {
  const startTime = Date.now();
  
  // Get available contracts
  const contractsResponse = http.get(
    `${baseUrl}/api/v1/contracts?status=published&limit=10`,
    {
      headers: { 'Authorization': `Bearer ${getAgentToken()}` }
    }
  );
  
  if (contractsResponse.status === 200) {
    const contracts = contractsResponse.json('contracts');
    
    if (contracts && contracts.length > 0) {
      const contract = contracts[Math.floor(Math.random() * contracts.length)];
      
      // Submit bid
      const bidPayload = {
        contractId: contract.id,
        proposedPrice: {
          economic: contract.budget.economic * (0.7 + Math.random() * 0.3),
          quality: 1.2 + Math.random() * 0.6
        },
        estimatedDelivery: Math.floor(Math.random() * 20) + 5,
        approach: `Optimized approach for ${contract.title}`
      };
      
      const bidResponse = http.post(
        `${baseUrl}/api/v1/bids`,
        JSON.stringify(bidPayload),
        {
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getAgentToken()}`
          },
          tags: { type: 'critical' }
        }
      );
      
      check(bidResponse, {
        'bid submitted': (r) => r.status === 201,
        'bid scored': (r) => r.json('score') !== undefined,
        'synergy calculated': (r) => r.json('projectedSynergy') !== undefined,
      });
      
      bidSubmissionTime.add(Date.now() - startTime);
      errorRate.add(bidResponse.status !== 201);
    }
  }
}

function marketAnalysisFlow(baseUrl) {
  // Market overview
  const marketResponse = http.get(
    `${baseUrl}/api/v1/market/overview`,
    {
      headers: { 'Authorization': `Bearer ${getAuthToken()}` }
    }
  );
  
  check(marketResponse, {
    'market data retrieved': (r) => r.status === 200,
    'efficiency calculated': (r) => r.json('efficiency') !== undefined,
  });
  
  if (marketResponse.status === 200) {
    const efficiency = parseFloat(marketResponse.json('efficiency'));
    marketEfficiency.add(efficiency);
  }
}
```

### 2. Currency System Load Test

```typescript
export const currencyLoadTest = {
  scenarios: {
    transfers: {
      executor: 'constant-arrival-rate',
      rate: 5000, // 5000 transfers per second
      timeUnit: '1s',
      duration: '10m',
      preAllocatedVUs: 1000,
      maxVUs: 5000,
    },
    exchanges: {
      executor: 'ramping-arrival-rate',
      startRate: 100,
      timeUnit: '1s',
      stages: [
        { target: 1000, duration: '2m' },
        { target: 2000, duration: '5m' },
        { target: 500, duration: '3m' },
      ],
      preAllocatedVUs: 500,
      maxVUs: 2000,
    },
  },
};

export function transfers() {
  const wallets = getTestWallets();
  const from = wallets[Math.floor(Math.random() * wallets.length)];
  const to = wallets[Math.floor(Math.random() * wallets.length)];
  
  if (from.id === to.id) return; // Skip self-transfers
  
  const transferPayload = {
    from: from.id,
    to: to.id,
    amounts: {
      economic: Math.floor(Math.random() * 1000) + 1,
      temporal: Math.floor(Math.random() * 100),
      innovation: Math.floor(Math.random() * 50)
    }
  };
  
  const response = http.post(
    `${baseUrl}/api/v1/currency/transfer`,
    JSON.stringify(transferPayload),
    {
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getServiceToken()}`
      },
      tags: { operation: 'transfer' }
    }
  );
  
  check(response, {
    'transfer successful': (r) => r.status === 200,
    'atomic completion': (r) => r.json('status') === 'completed',
    'conservation maintained': (r) => r.json('conservationCheck') === true,
  });
  
  // Verify balances if successful
  if (response.status === 200) {
    sleep(0.1); // Brief pause
    
    const balanceCheck = http.get(
      `${baseUrl}/api/v1/wallets/${from.id}/balance`,
      {
        headers: { 'Authorization': `Bearer ${getServiceToken()}` }
      }
    );
    
    check(balanceCheck, {
      'balance updated': (r) => r.status === 200,
      'balance non-negative': (r) => {
        const balance = r.json('balances.economic');
        return balance >= 0;
      }
    });
  }
}

export function exchanges() {
  const pairs = ['ECON/QUAL', 'ECON/TEMP', 'QUAL/INNO', 'TEMP/INNO'];
  const pair = pairs[Math.floor(Math.random() * pairs.length)];
  
  // Market order
  const orderPayload = {
    type: 'market',
    pair: pair,
    side: Math.random() > 0.5 ? 'buy' : 'sell',
    amount: Math.floor(Math.random() * 500) + 10,
    walletId: getRandomWallet().id
  };
  
  const response = http.post(
    `${baseUrl}/api/v1/exchange/orders`,
    JSON.stringify(orderPayload),
    {
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getServiceToken()}`
      },
      tags: { operation: 'exchange' }
    }
  );
  
  check(response, {
    'order placed': (r) => r.status === 201,
    'order executed': (r) => r.json('status') === 'filled',
    'price reasonable': (r) => {
      const price = parseFloat(r.json('executionPrice'));
      return price > 0 && price < 1000;
    }
  });
}
```

### 3. Team Formation Load Test

```typescript
export const teamFormationTest = {
  executor: 'ramping-vus',
  startVUs: 0,
  stages: [
    { duration: '5m', target: 100 },  // Simulate 100 concurrent team formations
    { duration: '10m', target: 500 }, // Peak team formation load
    { duration: '5m', target: 200 },  // Sustained load
    { duration: '5m', target: 0 },    // Ramp down
  ],
  thresholds: {
    'team_formation_time': ['p(95)<5000'], // 95% complete in 5s
    'synergy_calculation': ['avg>1.5'],    // Average synergy > 1.5
    'team_size_optimal': ['avg<6'],       // Average team size < 6
  },
};

export default function teamFormation() {
  // Create a contract requiring team formation
  const contract = createComplexContract();
  
  const startTime = Date.now();
  
  // Trigger team formation
  const formationResponse = http.post(
    `${baseUrl}/api/v1/team-formation`,
    JSON.stringify({
      contractId: contract.id,
      strategy: 'optimize_synergy',
      constraints: {
        maxTeamSize: 7,
        minQuality: 1.5,
        budgetLimit: contract.budget.economic
      }
    }),
    {
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getServiceToken()}`
      }
    }
  );
  
  const formationTime = Date.now() - startTime;
  
  check(formationResponse, {
    'team formed': (r) => r.status === 200,
    'all requirements covered': (r) => {
      const coverage = r.json('requirementCoverage');
      return coverage === 1.0;
    },
    'synergy achieved': (r) => {
      const synergy = parseFloat(r.json('synergy'));
      return synergy >= 1.5;
    },
    'within budget': (r) => {
      const cost = r.json('totalCost.economic');
      return cost <= contract.budget.economic;
    }
  });
  
  // Record metrics
  if (formationResponse.status === 200) {
    const result = formationResponse.json();
    
    __ENV.K6_METRICS && http.post(
      `${baseUrl}/api/v1/metrics`,
      JSON.stringify({
        metric: 'team_formation_time',
        value: formationTime,
        tags: { agents: result.agents.length }
      })
    );
    
    __ENV.K6_METRICS && http.post(
      `${baseUrl}/api/v1/metrics`,
      JSON.stringify({
        metric: 'synergy_calculation',
        value: result.synergy
      })
    );
  }
}
```

### 4. Market Efficiency Under Load

```typescript
export const marketEfficiencyTest = {
  scenarios: {
    // Continuous contract creation
    contractCreation: {
      executor: 'constant-arrival-rate',
      rate: 100,
      timeUnit: '1s',
      duration: '30m',
      preAllocatedVUs: 200,
    },
    // Burst bidding patterns
    bidding: {
      executor: 'ramping-arrival-rate',
      startRate: 50,
      timeUnit: '1s',
      stages: [
        { target: 500, duration: '5m' },   // Ramp up
        { target: 1000, duration: '10m' }, // Peak
        { target: 2000, duration: '5m' },  // Spike
        { target: 500, duration: '10m' },  // Normal
      ],
      preAllocatedVUs: 1000,
    },
    // Market monitoring
    monitoring: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30m',
    },
  },
};

export function monitoring() {
  // Continuously monitor market efficiency
  const metricsResponse = http.get(
    `${baseUrl}/api/v1/market/metrics`,
    {
      headers: { 'Authorization': `Bearer ${getMonitoringToken()}` }
    }
  );
  
  if (metricsResponse.status === 200) {
    const metrics = metricsResponse.json();
    
    check(metrics, {
      'efficiency maintained': () => metrics.efficiency >= 0.95,
      'liquidity sufficient': () => metrics.bidToContractRatio >= 3,
      'price discovery working': () => metrics.priceConvergence < 0.1,
      'no bottlenecks': () => metrics.bottlenecks.length === 0,
    });
    
    // Alert on efficiency drop
    if (metrics.efficiency < 0.95) {
      console.warn(`Market efficiency dropped to ${metrics.efficiency}`);
    }
  }
  
  sleep(5); // Check every 5 seconds
}
```

## Performance Baselines

### Target Metrics
| Operation | Target TPS | P95 Latency | P99 Latency |
|-----------|------------|-------------|-------------|
| Contract Creation | 1,000 | 25ms | 50ms |
| Bid Submission | 5,000 | 10ms | 20ms |
| Currency Transfer | 10,000 | 5ms | 10ms |
| Team Formation | 100 | 2s | 5s |
| Market Query | 10,000 | 5ms | 10ms |

### Load Progression Strategy
1. **Baseline** (Week 1): Establish performance baseline
2. **Optimization** (Week 2-3): Identify and fix bottlenecks
3. **Scaling** (Week 4): Test horizontal scaling
4. **Stress** (Week 5): Find breaking points
5. **Endurance** (Week 6): 24-hour sustained load

## Running Load Tests

### Local Development
```bash
# Install k6
brew install k6

# Run baseline test
k6 run --vus 100 --duration 10m load-testing/baseline.js

# Run with detailed metrics
k6 run --out influxdb=http://localhost:8086/k6 load-testing/baseline.js

# Run specific scenario
k6 run --env SCENARIO=currency load-testing/scenarios.js
```

### CI/CD Integration
```yaml
load-test:
  stage: performance
  script:
    - k6 run --summary-export=summary.json load-testing/baseline.js
    - k6 run --threshold 'http_req_duration{type:critical}<20' load-testing/critical-paths.js
  artifacts:
    reports:
      performance: summary.json
```

### Cloud Execution
```bash
# Run on k6 Cloud
k6 cloud load-testing/baseline.js

# Run distributed test
k6 run --execution-segment "0:1/4" load-testing/distributed.js
```

## Monitoring During Tests

### Real-time Metrics
- Request rate and throughput
- Response time distribution
- Error rate and types
- System resource usage
- Database connection pool
- Queue depths and processing rates

### Business Metrics
- Market efficiency score
- Contract fulfillment rate
- Average team synergy
- Currency conservation checks
- Economic law compliance

## Analysis and Reporting

### Performance Report Template
```markdown
## Load Test Report - [Date]

### Test Configuration
- Duration: 30 minutes
- Peak Load: 10,000 concurrent users
- Total Requests: 15,234,567

### Results Summary
- Success Rate: 99.7%
- Avg Response Time: 12ms
- P95 Response Time: 23ms
- P99 Response Time: 45ms
- Market Efficiency: 96.2%

### Bottlenecks Identified
1. Database connection pooling at 8,000+ TPS
2. Redis memory usage above 80%
3. CPU throttling on market-engine service

### Recommendations
1. Increase connection pool size to 200
2. Implement Redis cluster mode
3. Add 2 more market-engine replicas
```

Remember: Load testing reveals the true capacity of the system. Push it until it breaks, then make it stronger!