# Service Integration Tests

## Overview

Service integration tests verify that VibeLaunch's microservices communicate correctly, maintain data consistency, handle failures gracefully, and achieve 95%+ efficiency across the entire system. These tests validate end-to-end workflows and service orchestration.

## Test Categories

### 1. Service Communication Tests

```typescript
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { ServiceMesh } from '../test-infrastructure';
import { EventBus } from '@vibelaunch/event-bus';
import { ContractService, MarketEngine, AgentCoordinator } from '@vibelaunch/services';

describe('Service Communication Integration', () => {
  let mesh: ServiceMesh;
  let eventBus: EventBus;
  
  beforeAll(async () => {
    mesh = await ServiceMesh.create({
      services: [
        'contract-manager',
        'market-engine', 
        'agent-coordinator',
        'currency-service',
        'settlement-service'
      ],
      eventBus: true,
      database: true
    });
    
    eventBus = mesh.getEventBus();
  });
  
  afterAll(async () => {
    await mesh.teardown();
  });
  
  test('should propagate events across services', async () => {
    const events: any[] = [];
    
    // Subscribe to all events
    await eventBus.subscribe('*', (event) => {
      events.push(event);
    });
    
    // Create contract through contract service
    const contractService = mesh.getService('contract-manager');
    const contract = await contractService.createContract({
      organizationId: 'test-org',
      title: 'Integration Test Contract',
      requirements: ['content_creation', 'seo'],
      budget: {
        economic: 10000n,
        quality: 1.6,
        temporal: 500n
      }
    });
    
    // Wait for event propagation
    await mesh.waitForEvents(3, 5000);
    
    // Verify event chain
    expect(events).toContainEqual(
      expect.objectContaining({
        type: 'contract.created',
        data: expect.objectContaining({
          contractId: contract.id
        })
      })
    );
    
    expect(events).toContainEqual(
      expect.objectContaining({
        type: 'market.analysis_requested',
        data: expect.objectContaining({
          contractId: contract.id
        })
      })
    );
    
    expect(events).toContainEqual(
      expect.objectContaining({
        type: 'agents.notification_sent',
        data: expect.objectContaining({
          contractId: contract.id,
          notifiedAgents: expect.any(Number)
        })
      })
    );
  });
  
  test('should handle service discovery', async () => {
    const marketEngine = mesh.getService('market-engine');
    
    // Discover available services
    const services = await marketEngine.discoverServices();
    
    expect(services).toMatchObject({
      'contract-manager': {
        status: 'healthy',
        endpoints: expect.any(Array),
        version: expect.any(String)
      },
      'agent-coordinator': {
        status: 'healthy',
        endpoints: expect.any(Array),
        version: expect.any(String)
      },
      'currency-service': {
        status: 'healthy',
        endpoints: expect.any(Array),
        version: expect.any(String)
      }
    });
    
    // Test service communication
    const contractManager = await marketEngine.getServiceClient('contract-manager');
    const contracts = await contractManager.listContracts({ status: 'published' });
    
    expect(contracts).toBeDefined();
  });
  
  test('should maintain data consistency across services', async () => {
    // Create initial state
    const org = await mesh.createOrganization({
      name: 'Test Org',
      initialBalance: { economic: 50000n }
    });
    
    const contract = await mesh.createContract({
      organizationId: org.id,
      budget: { economic: 10000n }
    });
    
    // Verify wallet balance after escrow
    const orgWallet = await mesh.getWallet(org.walletId);
    expect(orgWallet.balances.economic).toBe(40000n); // 50000 - 10000
    
    // Create agents and submit bids
    const agents = await mesh.createAgents(3);
    const bids = await Promise.all(
      agents.map(agent => 
        mesh.submitBid({
          agentId: agent.id,
          contractId: contract.id,
          price: { economic: 8000n }
        })
      )
    );
    
    // Select winner
    const result = await mesh.selectWinningBid(contract.id, bids[0].id);
    
    // Verify state consistency across services
    const states = await Promise.all([
      mesh.getService('contract-manager').getContract(contract.id),
      mesh.getService('market-engine').getMarketState(contract.id),
      mesh.getService('agent-coordinator').getAgentState(agents[0].id),
      mesh.getService('currency-service').getWallet(agents[0].walletId)
    ]);
    
    const [contractState, marketState, agentState, walletState] = states;
    
    expect(contractState.status).toBe('assigned');
    expect(contractState.winningBid).toBe(bids[0].id);
    
    expect(marketState.efficiency).toBeGreaterThan(0.95);
    expect(marketState.status).toBe('closed');
    
    expect(agentState.activeContracts).toContain(contract.id);
    expect(agentState.status).toBe('busy');
    
    expect(walletState.balances.economic).toBe(8000n);
  });
  
  test('should handle distributed transactions', async () => {
    const org = await mesh.createOrganization({
      name: 'Distributed Test',
      initialBalance: { economic: 20000n }
    });
    
    // Start distributed transaction
    const txId = await mesh.beginDistributedTransaction();
    
    try {
      // Create multiple contracts atomically
      const contracts = await Promise.all([
        mesh.createContract({
          organizationId: org.id,
          budget: { economic: 5000n },
          transactionId: txId
        }),
        mesh.createContract({
          organizationId: org.id,
          budget: { economic: 5000n },
          transactionId: txId
        }),
        mesh.createContract({
          organizationId: org.id,
          budget: { economic: 5000n },
          transactionId: txId
        })
      ]);
      
      // Verify all succeeded
      expect(contracts).toHaveLength(3);
      
      // Commit transaction
      await mesh.commitDistributedTransaction(txId);
      
      // Verify final state
      const finalWallet = await mesh.getWallet(org.walletId);
      expect(finalWallet.balances.economic).toBe(5000n); // 20000 - 15000
      
    } catch (error) {
      // Rollback on failure
      await mesh.rollbackDistributedTransaction(txId);
      
      // Verify no changes
      const wallet = await mesh.getWallet(org.walletId);
      expect(wallet.balances.economic).toBe(20000n);
    }
  });
});
```

### 2. Circuit Breaker Tests

```typescript
describe('Circuit Breaker Integration', () => {
  test('should open circuit on service failures', async () => {
    const circuitBreaker = mesh.getCircuitBreaker('market-engine');
    
    // Simulate service failures
    await mesh.simulateServiceFailure('market-engine', {
      type: 'timeout',
      duration: 5000
    });
    
    // Make requests that will fail
    const requests = Array(10).fill(null).map(() =>
      mesh.getService('contract-manager').analyzeMarket('contract-001')
        .catch(e => e)
    );
    
    const results = await Promise.all(requests);
    const failures = results.filter(r => r instanceof Error);
    
    // Circuit should open after threshold
    expect(failures.length).toBeGreaterThan(5);
    expect(circuitBreaker.state).toBe('open');
    
    // Fast fail while open
    const start = Date.now();
    const fastFail = await mesh.getService('contract-manager')
      .analyzeMarket('contract-002')
      .catch(e => e);
    const duration = Date.now() - start;
    
    expect(fastFail).toBeInstanceOf(Error);
    expect(fastFail.message).toContain('Circuit breaker is open');
    expect(duration).toBeLessThan(100); // Fast fail
    
    // Wait for half-open state
    await mesh.wait(circuitBreaker.options.resetTimeout);
    expect(circuitBreaker.state).toBe('half-open');
    
    // Restore service
    await mesh.restoreService('market-engine');
    
    // Test recovery
    const recovery = await mesh.getService('contract-manager')
      .analyzeMarket('contract-003');
    
    expect(recovery).toBeDefined();
    expect(circuitBreaker.state).toBe('closed');
  });
  
  test('should handle cascading failures', async () => {
    // Create dependency chain
    const dependencies = {
      'contract-manager': ['market-engine', 'currency-service'],
      'market-engine': ['agent-coordinator'],
      'agent-coordinator': ['currency-service']
    };
    
    // Fail currency service (bottom of chain)
    await mesh.simulateServiceFailure('currency-service', {
      type: 'crash'
    });
    
    // Monitor cascade
    const healthChecks = await mesh.monitorHealth(5000);
    
    expect(healthChecks).toMatchObject({
      'currency-service': { status: 'unhealthy', reason: 'crashed' },
      'agent-coordinator': { status: 'degraded', reason: 'dependency_failure' },
      'market-engine': { status: 'degraded', reason: 'dependency_failure' },
      'contract-manager': { status: 'degraded', reason: 'multiple_dependencies_failed' }
    });
    
    // Verify graceful degradation
    const contract = await mesh.getService('contract-manager')
      .createContract({
        organizationId: 'test-org',
        title: 'Degraded Mode Contract',
        budget: { economic: 1000n }
      });
    
    expect(contract).toBeDefined();
    expect(contract.warnings).toContain('Created in degraded mode');
    expect(contract.features.disabled).toContain('automatic_pricing');
  });
});
```

### 3. End-to-End Workflow Tests

```typescript
describe('End-to-End Service Workflows', () => {
  test('should complete full contract lifecycle', async () => {
    // 1. Organization setup
    const org = await mesh.executeWorkflow('organization.onboarding', {
      name: 'Acme Corp',
      industry: 'Technology',
      size: 'medium',
      initialBudget: { economic: 100000n }
    });
    
    expect(org.status).toBe('active');
    expect(org.wallet).toBeDefined();
    expect(org.capabilities).toContain('contract_creation');
    
    // 2. Contract creation with market analysis
    const contractWorkflow = await mesh.executeWorkflow('contract.creation', {
      organizationId: org.id,
      title: 'Q1 Marketing Campaign',
      objectives: ['brand_awareness', 'lead_generation'],
      requirements: ['content_strategy', 'content_creation', 'seo', 'social_media'],
      budget: {
        economic: 25000n,
        quality: 1.7,
        temporal: 2000n,
        reliability: 0.85,
        innovation: 500n
      },
      timeline: {
        start: new Date(),
        end: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 90 days
      }
    });
    
    expect(contractWorkflow.steps).toMatchObject({
      validation: { status: 'completed' },
      escrowCreation: { status: 'completed' },
      marketAnalysis: { 
        status: 'completed',
        result: {
          estimatedBids: expect.any(Number),
          marketPrice: expect.any(Object),
          recommendedAdjustments: expect.any(Array)
        }
      },
      publication: { status: 'completed' },
      agentNotification: {
        status: 'completed',
        result: {
          notifiedAgents: expect.any(Number),
          interestedAgents: expect.any(Number)
        }
      }
    });
    
    const contract = contractWorkflow.result;
    
    // 3. Bidding phase
    await mesh.wait(1000); // Allow agents to analyze
    
    const biddingPhase = await mesh.monitorBidding(contract.id, {
      duration: 5000,
      checkInterval: 500
    });
    
    expect(biddingPhase.totalBids).toBeGreaterThan(5);
    expect(biddingPhase.bidDistribution).toMatchObject({
      solo: expect.any(Number),
      team: expect.any(Number)
    });
    expect(biddingPhase.priceRange.spread).toBeLessThan(0.3); // Competitive
    
    // 4. Team formation
    const teamFormation = await mesh.executeWorkflow('team.formation', {
      contractId: contract.id,
      selectionCriteria: {
        prioritize: 'synergy',
        minQuality: 1.5,
        maxTeamSize: 5
      }
    });
    
    expect(teamFormation.result.synergy).toBeGreaterThan(1.7);
    expect(teamFormation.result.agents.length).toBeLessThanOrEqual(5);
    expect(teamFormation.result.coverage).toBe(1.0); // All requirements covered
    
    // 5. Execution monitoring
    const execution = await mesh.executeWorkflow('contract.execution', {
      contractId: contract.id,
      teamId: teamFormation.result.id,
      checkpoints: [
        { milestone: 'strategy_complete', deadline: 7 },
        { milestone: 'content_50_percent', deadline: 30 },
        { milestone: 'seo_implementation', deadline: 45 },
        { milestone: 'campaign_launch', deadline: 60 },
        { milestone: 'final_report', deadline: 90 }
      ]
    });
    
    // Simulate execution progress
    for (const checkpoint of execution.checkpoints) {
      await mesh.simulateProgress(checkpoint.id, {
        completion: 1.0,
        quality: 1.6 + Math.random() * 0.3,
        onTime: Math.random() > 0.1
      });
    }
    
    // 6. Settlement
    const settlement = await mesh.executeWorkflow('contract.settlement', {
      contractId: contract.id,
      performanceMetrics: execution.performanceMetrics
    });
    
    expect(settlement.result).toMatchObject({
      status: 'completed',
      totalDistributed: expect.any(Object),
      synergyBonus: expect.any(Object),
      performanceAdjustments: expect.any(Object),
      reputationUpdates: expect.any(Array)
    });
    
    // 7. Verify final state
    const finalStates = await Promise.all([
      mesh.getContract(contract.id),
      mesh.getOrganization(org.id),
      mesh.getTeam(teamFormation.result.id),
      ...teamFormation.result.agents.map(a => mesh.getAgent(a.id))
    ]);
    
    const [finalContract, finalOrg, finalTeam, ...finalAgents] = finalStates;
    
    expect(finalContract.status).toBe('completed');
    expect(finalContract.actualCost).toBeDefined();
    expect(finalContract.clientSatisfaction).toBeGreaterThan(0.8);
    
    expect(finalOrg.completedContracts).toContain(contract.id);
    expect(finalOrg.reputation).toBeGreaterThan(org.reputation);
    
    expect(finalTeam.performance.averageQuality).toBeGreaterThan(1.6);
    expect(finalTeam.disbandedAt).toBeDefined();
    
    finalAgents.forEach(agent => {
      expect(agent.completedContracts).toContain(contract.id);
      expect(agent.earnings.economic).toBeGreaterThan(0n);
      expect(agent.reputation.reliability).toBeGreaterThan(0.5);
    });
  });
  
  test('should handle complex multi-contract scenarios', async () => {
    // Create multiple organizations
    const orgs = await Promise.all(
      Array(5).fill(null).map((_, i) =>
        mesh.createOrganization({
          name: `Org ${i}`,
          initialBalance: { economic: 50000n }
        })
      )
    );
    
    // Create overlapping contracts
    const contracts = [];
    for (const org of orgs) {
      const orgContracts = await Promise.all(
        Array(3).fill(null).map(() =>
          mesh.createContract({
            organizationId: org.id,
            requirements: mesh.generateRandomRequirements(2, 4),
            budget: {
              economic: BigInt(5000 + Math.random() * 10000)
            }
          })
        )
      );
      contracts.push(...orgContracts);
    }
    
    // Monitor market dynamics
    const marketDynamics = await mesh.monitorMarket({
      duration: 10000,
      metrics: ['efficiency', 'liquidity', 'priceDiscovery', 'agentUtilization']
    });
    
    expect(marketDynamics.efficiency.average).toBeGreaterThan(0.95);
    expect(marketDynamics.liquidity.bidToContractRatio).toBeGreaterThan(3);
    expect(marketDynamics.priceDiscovery.convergenceTime).toBeLessThan(5000);
    expect(marketDynamics.agentUtilization.average).toBeGreaterThan(0.7);
    
    // Verify no deadlocks or resource starvation
    expect(marketDynamics.issues.deadlocks).toHaveLength(0);
    expect(marketDynamics.issues.starvation).toHaveLength(0);
    expect(marketDynamics.issues.conflicts).toHaveLength(0);
  });
});
```

### 4. Service Recovery Tests

```typescript
describe('Service Recovery and Resilience', () => {
  test('should recover from coordinator failure', async () => {
    // Start long-running operation
    const operation = mesh.startLongOperation('team.formation', {
      contractId: 'contract-001',
      estimatedDuration: 10000
    });
    
    // Simulate coordinator crash mid-operation
    await mesh.wait(2000);
    await mesh.crashService('agent-coordinator');
    
    // New coordinator should pick up
    await mesh.startService('agent-coordinator');
    await mesh.wait(1000);
    
    // Operation should complete
    const result = await operation;
    expect(result.status).toBe('completed');
    expect(result.recoveredFrom).toBe('crash');
    expect(result.handledBy).not.toBe(result.startedBy);
  });
  
  test('should handle split-brain scenarios', async () => {
    // Create network partition
    await mesh.createNetworkPartition({
      partition1: ['contract-manager', 'market-engine'],
      partition2: ['agent-coordinator', 'currency-service'],
      settlementService: 'isolated'
    });
    
    // Both partitions try to process same contract
    const contract = { id: 'contract-001', budget: { economic: 10000n } };
    
    const results = await Promise.allSettled([
      mesh.getPartition(1).processContract(contract),
      mesh.getPartition(2).processContract(contract)
    ]);
    
    // Heal partition
    await mesh.healNetworkPartition();
    
    // Verify conflict resolution
    const resolution = await mesh.getConflictResolution(contract.id);
    
    expect(resolution.conflictsDetected).toBe(true);
    expect(resolution.resolution).toBe('deterministic_winner');
    expect(resolution.dataConsistency).toBe('restored');
    
    // Only one result should be accepted
    const accepted = results.filter(r => 
      r.status === 'fulfilled' && r.value.accepted
    );
    expect(accepted).toHaveLength(1);
  });
  
  test('should maintain service mesh integrity', async () => {
    // Run chaos test
    const chaosTest = await mesh.runChaosTest({
      duration: 30000,
      scenarios: [
        { type: 'random_failures', probability: 0.1 },
        { type: 'network_delays', range: [10, 500] },
        { type: 'cpu_spikes', services: ['market-engine'] },
        { type: 'memory_pressure', services: ['agent-coordinator'] }
      ]
    });
    
    // Verify system stayed operational
    expect(chaosTest.uptime).toBeGreaterThan(0.99);
    expect(chaosTest.dataIntegrity).toBe('maintained');
    expect(chaosTest.economicLawViolations).toBe(0);
    
    // Check self-healing
    expect(chaosTest.autoRecoveries).toBeGreaterThan(0);
    expect(chaosTest.manualInterventionsRequired).toBe(0);
    
    // Performance should degrade gracefully
    expect(chaosTest.performance.degradation).toBeLessThan(0.5);
    expect(chaosTest.performance.maintained95Percentile).toBe(true);
  });
});
```

## Test Utilities

### Service Integration Test Helpers

```typescript
// test-helpers/service-integration-utils.ts
export class ServiceIntegrationUtils {
  static async createServiceMesh(config: ServiceMeshConfig): Promise<ServiceMesh> {
    const mesh = new ServiceMesh();
    
    // Start core services
    for (const service of config.services) {
      await mesh.addService(service, {
        replicas: config.replicas?.[service] || 1,
        circuitBreaker: {
          threshold: 5,
          timeout: 30000,
          resetTimeout: 60000
        }
      });
    }
    
    // Setup event bus
    if (config.eventBus) {
      await mesh.setupEventBus({
        type: 'redis-streams',
        persistence: true
      });
    }
    
    // Initialize database
    if (config.database) {
      await mesh.setupDatabase({
        migrations: true,
        seedData: config.seedData
      });
    }
    
    // Wait for mesh stability
    await mesh.waitForHealthy();
    
    return mesh;
  }
  
  static async simulateRealWorldLoad(
    mesh: ServiceMesh,
    scenario: LoadScenario
  ): Promise<LoadTestResult> {
    const simulator = new LoadSimulator(mesh);
    
    const patterns = {
      normal: {
        contractsPerHour: 100,
        agentsActive: 200,
        peakHours: [9, 14, 20]
      },
      black_friday: {
        contractsPerHour: 1000,
        agentsActive: 500,
        burstiness: 0.8
      },
      gradual_growth: {
        startingLoad: 10,
        growthRate: 1.5,
        duration: 86400000 // 24 hours
      }
    };
    
    return await simulator.run(patterns[scenario]);
  }
  
  static async validateServiceInvariants(
    mesh: ServiceMesh
  ): Promise<InvariantValidation> {
    const validators = [
      new ValueConservationValidator(),
      new DataConsistencyValidator(),
      new EventOrderingValidator(),
      new SLAComplianceValidator()
    ];
    
    const results = await Promise.all(
      validators.map(v => v.validate(mesh))
    );
    
    return {
      allValid: results.every(r => r.valid),
      violations: results.filter(r => !r.valid),
      warnings: results.flatMap(r => r.warnings)
    };
  }
  
  static async measureServiceMetrics(
    mesh: ServiceMesh,
    duration: number
  ): Promise<ServiceMetrics> {
    const collector = new MetricsCollector(mesh);
    
    const metrics = await collector.collect(duration, {
      interval: 1000,
      aggregations: ['avg', 'p50', 'p95', 'p99', 'max']
    });
    
    return {
      latency: metrics.latency,
      throughput: metrics.throughput,
      errorRates: metrics.errors,
      resourceUsage: metrics.resources,
      businessMetrics: {
        contractsProcessed: metrics.custom.contracts,
        bidsSubmitted: metrics.custom.bids,
        teamsFormed: metrics.custom.teams,
        marketEfficiency: metrics.custom.efficiency
      }
    };
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- Service Communication: 95%
- Event Propagation: 100%
- Failure Scenarios: 90%
- Recovery Procedures: 95%
- End-to-End Workflows: 85%

### Critical Integration Points
1. Event-driven choreography
2. Distributed transactions
3. Service discovery
4. Circuit breaker patterns
5. Data consistency
6. Failure recovery
7. Performance under load

## Running Service Integration Tests

```bash
# Run all service integration tests
pnpm test:integration:services

# Run specific workflow tests
pnpm test:integration:services:workflows

# Run resilience tests
pnpm test:integration:services:resilience

# Run with service mesh visualization
VISUALIZE=true pnpm test:integration:services

# Run with chaos testing
CHAOS_ENABLED=true pnpm test:integration:services

# Run long-duration soak tests
pnpm test:integration:services:soak --duration=3600000
```

Remember: Services must work together seamlessly to deliver value. Every integration test ensures reliability, efficiency, and resilience at scale!
