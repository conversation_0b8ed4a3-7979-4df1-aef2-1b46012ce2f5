# Database Integration Tests

## Overview

Database integration tests ensure that VibeLaunch's PostgreSQL database maintains ACID compliance, handles concurrent multi-currency operations, and preserves economic law invariants at scale. These tests verify data integrity across complex transactions and high-load scenarios.

## Test Categories

### 1. Transaction Integrity Tests

```typescript
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { TestDatabase } from '../test-infrastructure';
import { EconomicLawValidator } from '@vibelaunch/economic-laws';

describe('Database Transaction Tests', () => {
  let db: PrismaClient;
  let testDb: TestDatabase;
  let validator: EconomicLawValidator;
  
  beforeEach(async () => {
    testDb = await TestDatabase.create();
    db = testDb.client;
    validator = new EconomicLawValidator(db);
  });
  
  afterEach(async () => {
    await testDb.cleanup();
  });
  
  test('should maintain ACID compliance for multi-currency transfers', async () => {
    // Create test wallets
    const sender = await db.wallet.create({
      data: {
        ownerId: 'user-001',
        ownerType: 'user',
        balances: {
          economic: 10000n,
          quality: 1.5,
          temporal: 1000n,
          reliability: 0.8,
          innovation: 500n
        }
      }
    });
    
    const receiver = await db.wallet.create({
      data: {
        ownerId: 'user-002',
        ownerType: 'user',
        balances: {
          economic: 0n,
          quality: 1.0,
          temporal: 0n,
          reliability: 0.5,
          innovation: 0n
        }
      }
    });
    
    // Execute atomic multi-currency transfer
    const transfer = {
      from: sender.id,
      to: receiver.id,
      amounts: {
        economic: 5000n,
        temporal: 500n,
        innovation: 250n
      }
    };
    
    await db.$transaction(async (tx) => {
      // Deduct from sender
      await tx.wallet.update({
        where: { id: sender.id },
        data: {
          balances: {
            economic: { decrement: transfer.amounts.economic },
            temporal: { decrement: transfer.amounts.temporal },
            innovation: { decrement: transfer.amounts.innovation }
          }
        }
      });
      
      // Add to receiver
      await tx.wallet.update({
        where: { id: receiver.id },
        data: {
          balances: {
            economic: { increment: transfer.amounts.economic },
            temporal: { increment: transfer.amounts.temporal },
            innovation: { increment: transfer.amounts.innovation }
          }
        }
      });
      
      // Record transaction
      await tx.transaction.create({
        data: {
          type: 'transfer',
          fromWalletId: sender.id,
          toWalletId: receiver.id,
          amounts: transfer.amounts,
          status: 'completed'
        }
      });
    });
    
    // Verify final state
    const finalSender = await db.wallet.findUnique({ where: { id: sender.id } });
    const finalReceiver = await db.wallet.findUnique({ where: { id: receiver.id } });
    
    expect(finalSender.balances.economic).toBe(5000n);
    expect(finalSender.balances.temporal).toBe(500n);
    expect(finalSender.balances.innovation).toBe(250n);
    
    expect(finalReceiver.balances.economic).toBe(5000n);
    expect(finalReceiver.balances.temporal).toBe(500n);
    expect(finalReceiver.balances.innovation).toBe(250n);
    
    // Verify value conservation
    const conservationValid = await validator.checkValueConservation();
    expect(conservationValid).toBe(true);
  });
  
  test('should rollback on constraint violations', async () => {
    const wallet = await db.wallet.create({
      data: {
        ownerId: 'user-001',
        ownerType: 'user',
        balances: { economic: 1000n }
      }
    });
    
    // Snapshot before
    const before = { ...wallet.balances };
    
    // Attempt invalid operation
    try {
      await db.$transaction(async (tx) => {
        // First operation succeeds
        await tx.wallet.update({
          where: { id: wallet.id },
          data: {
            balances: {
              economic: { increment: 500n }
            }
          }
        });
        
        // Second operation fails (negative balance)
        await tx.wallet.update({
          where: { id: wallet.id },
          data: {
            balances: {
              economic: { decrement: 2000n } // Would result in -500
            }
          }
        });
      });
    } catch (error) {
      // Transaction should rollback
    }
    
    // Verify no changes
    const after = await db.wallet.findUnique({ where: { id: wallet.id } });
    expect(after.balances).toEqual(before);
  });
  
  test('should handle concurrent operations safely', async () => {
    const wallet = await db.wallet.create({
      data: {
        ownerId: 'concurrent-test',
        ownerType: 'user',
        balances: { economic: 10000n }
      }
    });
    
    // Simulate 100 concurrent transfers of 100 each
    const transfers = Array(100).fill(null).map((_, i) => 
      db.$transaction(async (tx) => {
        const recipient = await tx.wallet.create({
          data: {
            ownerId: `recipient-${i}`,
            ownerType: 'user',
            balances: { economic: 0n }
          }
        });
        
        await tx.wallet.update({
          where: { id: wallet.id },
          data: {
            balances: {
              economic: { decrement: 100n }
            }
          }
        });
        
        await tx.wallet.update({
          where: { id: recipient.id },
          data: {
            balances: {
              economic: { increment: 100n }
            }
          }
        });
        
        return recipient;
      })
    );
    
    // Execute concurrently
    const results = await Promise.allSettled(transfers);
    
    // Exactly 100 should succeed (10000 / 100)
    const successful = results.filter(r => r.status === 'fulfilled');
    expect(successful.length).toBe(100);
    
    // Verify final balance
    const finalWallet = await db.wallet.findUnique({ where: { id: wallet.id } });
    expect(finalWallet.balances.economic).toBe(0n);
    
    // Verify total conservation
    const allWallets = await db.wallet.findMany();
    const totalEconomic = allWallets.reduce(
      (sum, w) => sum + w.balances.economic,
      0n
    );
    expect(totalEconomic).toBe(10000n);
  });
});
```

### 2. Database Performance Tests

```typescript
describe('Database Performance Integration', () => {
  test('should handle high-volume contract creation', async () => {
    const startTime = Date.now();
    const batchSize = 1000;
    
    // Batch insert contracts
    const contracts = Array(batchSize).fill(null).map((_, i) => ({
      organizationId: `org-${i % 100}`, // 100 different orgs
      title: `Contract ${i}`,
      requirements: ['content_creation', 'seo'],
      budget: {
        economic: BigInt(Math.floor(Math.random() * 10000) + 1000),
        quality: 1.0 + Math.random() * 0.8,
        temporal: BigInt(Math.floor(Math.random() * 1000)),
        reliability: 0.5 + Math.random() * 0.5,
        innovation: BigInt(Math.floor(Math.random() * 500))
      },
      status: 'draft',
      complexityScore: Math.random() * 10
    }));
    
    await db.contract.createMany({
      data: contracts
    });
    
    const duration = Date.now() - startTime;
    
    // Should complete in reasonable time
    expect(duration).toBeLessThan(5000); // 5 seconds for 1000 contracts
    
    // Verify all created
    const count = await db.contract.count();
    expect(count).toBe(batchSize);
    
    // Test query performance
    const queryStart = Date.now();
    
    const results = await db.contract.findMany({
      where: {
        budget: {
          path: ['economic'],
          gt: 5000
        },
        status: 'draft'
      },
      orderBy: {
        complexityScore: 'desc'
      },
      take: 100
    });
    
    const queryDuration = Date.now() - queryStart;
    expect(queryDuration).toBeLessThan(100); // 100ms for complex query
    expect(results.length).toBeLessThanOrEqual(100);
  });
  
  test('should efficiently query with indexes', async () => {
    // Create test data
    await testDb.seedMarketData({
      contracts: 10000,
      agents: 1000,
      bids: 50000
    });
    
    // Test indexed queries
    const queries = [
      {
        name: 'Active contracts by organization',
        query: () => db.contract.findMany({
          where: {
            organizationId: 'org-001',
            status: 'published'
          }
        }),
        maxTime: 50
      },
      {
        name: 'Agent performance lookup',
        query: () => db.agentPerformance.findFirst({
          where: {
            agentId: 'agent-001',
            period: 'monthly'
          },
          orderBy: {
            createdAt: 'desc'
          }
        }),
        maxTime: 20
      },
      {
        name: 'Market price aggregation',
        query: () => db.bid.aggregate({
          where: {
            status: 'accepted',
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
          },
          _avg: {
            proposedPrice: true
          },
          _count: true
        }),
        maxTime: 100
      }
    ];
    
    for (const test of queries) {
      const start = Date.now();
      await test.query();
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(test.maxTime);
    }
  });
  
  test('should handle connection pooling under load', async () => {
    // Configure connection pool
    const pooledDb = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      },
      log: ['warn', 'error']
    });
    
    // Simulate concurrent connections
    const concurrentOps = 50;
    const opsPerConnection = 10;
    
    const operations = Array(concurrentOps).fill(null).map(async (_, connId) => {
      const results = [];
      
      for (let op = 0; op < opsPerConnection; op++) {
        const result = await pooledDb.wallet.findMany({
          where: {
            balances: {
              path: ['economic'],
              gt: 1000
            }
          },
          take: 10
        });
        results.push(result);
      }
      
      return results;
    });
    
    const start = Date.now();
    const allResults = await Promise.all(operations);
    const duration = Date.now() - start;
    
    // Verify all operations completed
    expect(allResults.length).toBe(concurrentOps);
    expect(allResults.every(r => r.length === opsPerConnection)).toBe(true);
    
    // Should handle load efficiently
    expect(duration).toBeLessThan(5000); // 5s for 500 total operations
    
    await pooledDb.$disconnect();
  });
});
```

### 3. Data Integrity Tests

```typescript
describe('Data Integrity and Constraints', () => {
  test('should enforce economic law constraints', async () => {
    // Create system wallet for testing
    const systemWallet = await db.wallet.create({
      data: {
        ownerId: 'system',
        ownerType: 'system',
        balances: {
          economic: 1000000n,
          quality: 1.0,
          temporal: 100000n,
          reliability: 1.0,
          innovation: 50000n
        }
      }
    });
    
    // Test value conservation trigger
    await expect(
      db.$executeRaw`
        UPDATE wallets 
        SET balances = jsonb_set(balances, '{economic}', '2000000'::jsonb)
        WHERE id = ${systemWallet.id}
      `
    ).rejects.toThrow(/value conservation violation/i);
    
    // Test quality range constraint
    await expect(
      db.wallet.update({
        where: { id: systemWallet.id },
        data: {
          balances: {
            quality: 2.5 // Above max
          }
        }
      })
    ).rejects.toThrow(/quality must be between 0 and 2/i);
    
    // Test negative value prevention
    await expect(
      db.wallet.update({
        where: { id: systemWallet.id },
        data: {
          balances: {
            economic: -100n
          }
        }
      })
    ).rejects.toThrow(/cannot be negative/i);
  });
  
  test('should maintain referential integrity', async () => {
    const org = await db.organization.create({
      data: {
        name: 'Test Org',
        walletId: 'non-existent-wallet' // Should fail
      }
    }).catch(e => e);
    
    expect(org).toBeInstanceOf(Error);
    expect(org.message).toMatch(/foreign key constraint/i);
    
    // Create proper references
    const wallet = await db.wallet.create({
      data: {
        ownerId: 'org-001',
        ownerType: 'organization',
        balances: { economic: 10000n }
      }
    });
    
    const validOrg = await db.organization.create({
      data: {
        name: 'Valid Org',
        walletId: wallet.id
      }
    });
    
    // Test cascade behavior
    const contract = await db.contract.create({
      data: {
        organizationId: validOrg.id,
        title: 'Test Contract',
        requirements: ['seo'],
        budget: { economic: 5000n },
        status: 'draft'
      }
    });
    
    // Deleting org should handle contracts appropriately
    await expect(
      db.organization.delete({
        where: { id: validOrg.id }
      })
    ).rejects.toThrow(/cannot delete organization with active contracts/i);
  });
  
  test('should validate multi-tenant isolation', async () => {
    // Create two organizations
    const [orgA, orgB] = await Promise.all([
      db.organization.create({
        data: { name: 'Org A' }
      }),
      db.organization.create({
        data: { name: 'Org B' }
      })
    ]);
    
    // Create contracts for each
    const contractA = await db.contract.create({
      data: {
        organizationId: orgA.id,
        title: 'Org A Contract',
        requirements: ['content'],
        budget: { economic: 5000n },
        status: 'published'
      }
    });
    
    const contractB = await db.contract.create({
      data: {
        organizationId: orgB.id,
        title: 'Org B Contract',
        requirements: ['seo'],
        budget: { economic: 3000n },
        status: 'published'
      }
    });
    
    // Test RLS policies (simulated)
    const orgAContext = { organizationId: orgA.id };
    const orgBContext = { organizationId: orgB.id };
    
    // Org A can only see their contracts
    const orgAContracts = await db.contract.findMany({
      where: { organizationId: orgAContext.organizationId }
    });
    
    expect(orgAContracts.length).toBe(1);
    expect(orgAContracts[0].id).toBe(contractA.id);
    
    // Org B cannot update Org A's contract
    await expect(
      db.contract.update({
        where: { 
          id: contractA.id,
          organizationId: orgBContext.organizationId // Security check
        },
        data: { title: 'Hijacked!' }
      })
    ).rejects.toThrow();
  });
});
```

### 4. Database Migration Tests

```typescript
describe('Database Migration Integration', () => {
  test('should apply migrations in correct order', async () => {
    // Create fresh database
    const migrationDb = await TestDatabase.createEmpty();
    
    // Run migrations
    const migrationResult = await migrationDb.runMigrations();
    
    expect(migrationResult.appliedMigrations).toContain('001_initial_schema');
    expect(migrationResult.appliedMigrations).toContain('002_add_wallets');
    expect(migrationResult.appliedMigrations).toContain('003_add_contracts');
    expect(migrationResult.appliedMigrations).toContain('004_add_economic_laws');
    expect(migrationResult.appliedMigrations).toContain('005_add_indexes');
    
    // Verify schema state
    const tables = await migrationDb.getTables();
    
    expect(tables).toContain('organizations');
    expect(tables).toContain('wallets');
    expect(tables).toContain('contracts');
    expect(tables).toContain('agents');
    expect(tables).toContain('bids');
    expect(tables).toContain('transactions');
    
    // Verify indexes
    const indexes = await migrationDb.getIndexes('contracts');
    
    expect(indexes).toContainEqual(
      expect.objectContaining({
        name: 'idx_contracts_organization_status',
        columns: ['organization_id', 'status']
      })
    );
    
    expect(indexes).toContainEqual(
      expect.objectContaining({
        name: 'idx_contracts_budget_economic',
        columns: ['(budget->>\'economic\')'],
        type: 'btree'
      })
    );
    
    await migrationDb.cleanup();
  });
  
  test('should handle migration rollbacks', async () => {
    const migrationDb = await TestDatabase.createWithSchema();
    
    // Apply test migration
    const testMigration = `
      -- Add test column
      ALTER TABLE contracts ADD COLUMN test_field VARCHAR(255);
      
      -- Add constraint that might fail
      ALTER TABLE contracts ADD CONSTRAINT check_test_field 
        CHECK (test_field IS NOT NULL);
    `;
    
    try {
      await migrationDb.executeMigration('999_test_migration', testMigration);
    } catch (error) {
      // Migration should fail due to constraint
    }
    
    // Verify rollback
    const columns = await migrationDb.getColumns('contracts');
    expect(columns).not.toContain('test_field');
    
    // Verify database is still functional
    const contract = await migrationDb.client.contract.create({
      data: {
        organizationId: 'test-org',
        title: 'Post-rollback Contract',
        requirements: ['content'],
        budget: { economic: 1000n },
        status: 'draft'
      }
    });
    
    expect(contract.id).toBeDefined();
  });
  
  test('should validate data migrations', async () => {
    // Create data in old schema format
    await db.$executeRaw`
      INSERT INTO contracts_old (
        id, organization_id, title, budget_amount, budget_currency
      ) VALUES (
        'old-001', 'org-001', 'Legacy Contract', 5000, 'USD'
      )
    `;
    
    // Run data migration
    await db.$executeRaw`
      INSERT INTO contracts (id, organization_id, title, budget, status)
      SELECT 
        id, 
        organization_id, 
        title,
        jsonb_build_object(
          'economic', budget_amount,
          'quality', 1.0,
          'temporal', 0,
          'reliability', 0.5,
          'innovation', 0
        ),
        'migrated'
      FROM contracts_old
    `;
    
    // Verify migrated data
    const migrated = await db.contract.findUnique({
      where: { id: 'old-001' }
    });
    
    expect(migrated).toBeDefined();
    expect(migrated.budget.economic).toBe(5000n);
    expect(migrated.status).toBe('migrated');
    
    // Verify no data loss
    const oldCount = await db.$queryRaw`SELECT COUNT(*) FROM contracts_old`;
    const newCount = await db.contract.count();
    
    expect(newCount).toBeGreaterThanOrEqual(oldCount);
  });
});
```

## Test Utilities

### Database Test Helpers

```typescript
// test-helpers/database-test-utils.ts
export class DatabaseTestUtils {
  static async createTestDatabase(): Promise<TestDatabase> {
    const dbName = `test_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    
    // Create database
    await execSync(`createdb ${dbName}`);
    
    // Run migrations
    const databaseUrl = `postgresql://test:test@localhost:5432/${dbName}`;
    await execSync(`DATABASE_URL=${databaseUrl} npx prisma migrate deploy`);
    
    // Create client
    const client = new PrismaClient({
      datasources: {
        db: { url: databaseUrl }
      }
    });
    
    return {
      name: dbName,
      url: databaseUrl,
      client,
      cleanup: async () => {
        await client.$disconnect();
        await execSync(`dropdb ${dbName}`);
      }
    };
  }
  
  static async seedTestData(db: PrismaClient, scale: 'small' | 'medium' | 'large') {
    const scales = {
      small: { orgs: 10, agents: 20, contracts: 50 },
      medium: { orgs: 100, agents: 200, contracts: 1000 },
      large: { orgs: 1000, agents: 2000, contracts: 10000 }
    };
    
    const config = scales[scale];
    
    // Batch create organizations
    const orgs = await db.organization.createMany({
      data: Array(config.orgs).fill(null).map((_, i) => ({
        name: `Test Org ${i}`,
        createdAt: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)
      }))
    });
    
    // Create agents with wallets
    const agents = [];
    for (let i = 0; i < config.agents; i++) {
      const wallet = await db.wallet.create({
        data: {
          ownerId: `agent-${i}`,
          ownerType: 'agent',
          balances: {
            economic: 0n,
            quality: 1.0 + Math.random() * 0.8,
            temporal: 0n,
            reliability: 0.5 + Math.random() * 0.45,
            innovation: 0n
          }
        }
      });
      
      const agent = await db.agent.create({
        data: {
          id: `agent-${i}`,
          name: `Test Agent ${i}`,
          walletId: wallet.id,
          capabilities: generateRandomCapabilities(),
          status: 'active'
        }
      });
      
      agents.push(agent);
    }
    
    // Create contracts
    const contracts = await db.contract.createMany({
      data: Array(config.contracts).fill(null).map((_, i) => ({
        organizationId: `org-${i % config.orgs}`,
        title: `Contract ${i}`,
        requirements: generateRandomRequirements(),
        budget: generateRandomBudget(),
        status: ['draft', 'published', 'assigned', 'completed'][i % 4],
        complexityScore: Math.random() * 10
      }))
    });
    
    return { orgs, agents, contracts };
  }
  
  static async measureQueryPerformance(
    db: PrismaClient,
    queries: QueryTest[]
  ): Promise<QueryPerformanceResult[]> {
    const results = [];
    
    for (const query of queries) {
      const times = [];
      
      // Warm up
      await query.execute(db);
      
      // Measure
      for (let i = 0; i < 10; i++) {
        const start = process.hrtime.bigint();
        await query.execute(db);
        const end = process.hrtime.bigint();
        
        times.push(Number(end - start) / 1000000); // Convert to ms
      }
      
      results.push({
        name: query.name,
        times,
        avg: average(times),
        p50: percentile(times, 50),
        p95: percentile(times, 95),
        p99: percentile(times, 99)
      });
    }
    
    return results;
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- Transaction Tests: 100%
- Constraint Tests: 100%
- Concurrency Tests: 95%
- Performance Tests: 90%
- Migration Tests: 100%

### Critical Database Operations
1. Multi-currency atomic transfers
2. Value conservation enforcement
3. Concurrent bid processing
4. Complex aggregation queries
5. Migration rollback procedures
6. Index performance validation
7. Connection pool management

## Running Database Integration Tests

```bash
# Run all database integration tests
pnpm test:integration:db

# Run transaction tests only
pnpm test:integration:db:transactions

# Run performance tests
pnpm test:integration:db:performance

# Run with specific database
DATABASE_URL=postgresql://user:pass@localhost:5432/test pnpm test:integration:db

# Run with query logging
DEBUG=prisma:query pnpm test:integration:db

# Run migration tests
pnpm test:integration:db:migrations
```

Remember: The database is the source of truth for all economic value. Every test ensures data integrity and performance at scale!
