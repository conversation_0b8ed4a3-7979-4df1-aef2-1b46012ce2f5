# API Integration Tests

## Overview

API integration tests verify that VibeLaunch's services communicate correctly through REST, GraphQL, and WebSocket interfaces. These tests ensure data flows seamlessly, contracts are honored, and the system maintains 95%+ efficiency across all endpoints.

## Test Categories

### 1. REST API Integration Tests

#### Contract Management Endpoints

```typescript
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { TestEnvironment } from '../test-infrastructure';
import { generateContract, generateMultiCurrencyBudget } from '../test-helpers';

describe('Contract API Integration', () => {
  let env: TestEnvironment;
  let authToken: string;
  let organizationId: string;
  
  beforeAll(async () => {
    env = await TestEnvironment.create();
    const auth = await env.authenticateOrganization({
      email: '<EMAIL>',
      password: 'test123'
    });
    authToken = auth.token;
    organizationId = auth.organizationId;
  });
  
  afterAll(async () => {
    await env.cleanup();
  });
  
  test('POST /contracts should create with 5D currency', async () => {
    const contractData = {
      title: 'Q1 Marketing Campaign',
      description: 'Comprehensive digital marketing for product launch',
      requirements: ['content_creation', 'seo', 'social_media', 'analytics'],
      budget: generateMultiCurrencyBudget({
        economic: 15000n,
        quality: 1.7,
        temporal: 1000n,
        reliability: 0.85,
        innovation: 200n
      }),
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      preferences: {
        teamSize: 'optimal',
        priorityDimension: 'quality'
      }
    };
    
    const response = await request(env.app)
      .post('/api/v1/contracts')
      .set('Authorization', `Bearer ${authToken}`)
      .send(contractData)
      .expect(201);
    
    expect(response.body).toMatchObject({
      id: expect.any(String),
      organizationId,
      status: 'draft',
      budget: {
        economic: '15000',
        quality: 1.7,
        temporal: '1000',
        reliability: 0.85,
        innovation: '200'
      },
      escrowId: expect.any(String),
      complexityScore: expect.any(Number),
      estimatedSynergy: expect.any(Number)
    });
    
    // Verify escrow was created
    const escrow = await env.db.escrow.findUnique({
      where: { id: response.body.escrowId }
    });
    
    expect(escrow).toBeDefined();
    expect(escrow.locked.economic).toBe(15000n);
  });
  
  test('GET /contracts/:id should return full state', async () => {
    // Create contract first
    const contract = await env.createContract({
      organizationId,
      budget: { economic: 10000n }
    });
    
    const response = await request(env.app)
      .get(`/api/v1/contracts/${contract.id}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);
    
    expect(response.body).toMatchObject({
      id: contract.id,
      organizationId,
      budget: expect.any(Object),
      bids: expect.any(Array),
      status: expect.any(String),
      marketAnalysis: {
        estimatedBids: expect.any(Number),
        marketPrice: expect.any(Object),
        competitionLevel: expect.any(String)
      }
    });
  });
  
  test('PUT /contracts/:id/publish should transition to market', async () => {
    const contract = await env.createContract({
      organizationId,
      status: 'draft',
      budget: { economic: 5000n }
    });
    
    const response = await request(env.app)
      .put(`/api/v1/contracts/${contract.id}/publish`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({ 
        biddingDuration: 86400, // 24 hours
        autoSelect: true 
      })
      .expect(200);
    
    expect(response.body.status).toBe('published');
    expect(response.body.publishedAt).toBeDefined();
    expect(response.body.biddingEndsAt).toBeDefined();
    
    // Verify event was published
    const events = await env.getEvents('contract.published');
    expect(events).toContainEqual(
      expect.objectContaining({
        contractId: contract.id,
        budget: expect.any(Object)
      })
    );
  });
  
  test('should enforce organization isolation', async () => {
    // Create contract as org A
    const contractA = await env.createContract({
      organizationId,
      budget: { economic: 5000n }
    });
    
    // Authenticate as org B
    const authB = await env.authenticateOrganization({
      email: '<EMAIL>',
      password: 'test123'
    });
    
    // Try to access org A's contract
    await request(env.app)
      .get(`/api/v1/contracts/${contractA.id}`)
      .set('Authorization', `Bearer ${authB.token}`)
      .expect(403);
    
    // Try to modify org A's contract
    await request(env.app)
      .put(`/api/v1/contracts/${contractA.id}`)
      .set('Authorization', `Bearer ${authB.token}`)
      .send({ title: 'Hijacked!' })
      .expect(403);
  });
});
```

#### Bidding System Endpoints

```typescript
describe('Bidding API Integration', () => {
  let contract: Contract;
  let agents: Agent[];
  
  beforeEach(async () => {
    // Create and publish contract
    contract = await env.createAndPublishContract({
      organizationId,
      requirements: ['content_creation', 'seo'],
      budget: { economic: 8000n, quality: 1.6 }
    });
    
    // Create test agents
    agents = await env.createAgents([
      { 
        name: 'Content Pro',
        capabilities: ['content_creation'],
        wallet: { economic: 0n }
      },
      {
        name: 'SEO Master',
        capabilities: ['seo'],
        wallet: { economic: 0n }
      },
      {
        name: 'Full Stack Marketer',
        capabilities: ['content_creation', 'seo', 'social_media'],
        wallet: { economic: 0n }
      }
    ]);
  });
  
  test('POST /contracts/:id/bids should accept valid bids', async () => {
    const bidData = {
      agentId: agents[0].id,
      proposedPrice: {
        economic: 4000,
        quality: 1.8
      },
      estimatedDelivery: 5, // days
      approach: 'Focus on long-form content with SEO optimization',
      teamMembers: [agents[0].id, agents[1].id]
    };
    
    const response = await request(env.app)
      .post(`/api/v1/contracts/${contract.id}/bids`)
      .set('Authorization', `Bearer ${agents[0].apiKey}`)
      .send(bidData)
      .expect(201);
    
    expect(response.body).toMatchObject({
      id: expect.any(String),
      contractId: contract.id,
      agentId: agents[0].id,
      status: 'pending',
      score: expect.any(Number),
      projectedSynergy: expect.any(Number)
    });
    
    // Verify bid appears in contract
    const updatedContract = await env.getContract(contract.id);
    expect(updatedContract.bids).toHaveLength(1);
  });
  
  test('GET /contracts/:id/bids should return ranked bids', async () => {
    // Submit multiple bids
    const bids = await Promise.all([
      env.submitBid({
        contractId: contract.id,
        agentId: agents[0].id,
        price: { economic: 6000n },
        quality: 1.5
      }),
      env.submitBid({
        contractId: contract.id,
        agentId: agents[1].id,
        price: { economic: 5500n },
        quality: 1.6
      }),
      env.submitBid({
        contractId: contract.id,
        agentId: agents[2].id,
        price: { economic: 7000n },
        quality: 1.8
      })
    ]);
    
    const response = await request(env.app)
      .get(`/api/v1/contracts/${contract.id}/bids`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);
    
    expect(response.body.bids).toHaveLength(3);
    expect(response.body.bids[0].rank).toBe(1);
    expect(response.body.efficiency).toBeGreaterThan(0.95);
    
    // Verify ranking considers multiple factors
    const scores = response.body.bids.map(b => b.score);
    expect(scores[0]).toBeGreaterThan(scores[1]);
    expect(scores[1]).toBeGreaterThan(scores[2]);
  });
  
  test('PUT /contracts/:id/select-bid should form team', async () => {
    // Submit team bid
    const teamBid = await env.submitBid({
      contractId: contract.id,
      agentIds: [agents[0].id, agents[1].id],
      price: { economic: 7500n },
      quality: 1.75
    });
    
    const response = await request(env.app)
      .put(`/api/v1/contracts/${contract.id}/select-bid`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({ bidId: teamBid.id })
      .expect(200);
    
    expect(response.body).toMatchObject({
      contract: {
        status: 'assigned',
        winningBid: teamBid.id
      },
      team: {
        id: expect.any(String),
        agents: expect.arrayContaining([
          expect.objectContaining({ id: agents[0].id }),
          expect.objectContaining({ id: agents[1].id })
        ]),
        synergy: expect.any(Number),
        estimatedCompletion: expect.any(String)
      }
    });
    
    // Verify escrow transfer to team
    const teamWallet = await env.getWallet(response.body.team.walletId);
    expect(teamWallet.balances.economic).toBe(7500n);
  });
});
```

### 2. GraphQL Integration Tests

```typescript
describe('GraphQL API Integration', () => {
  let client: GraphQLClient;
  
  beforeAll(() => {
    client = new GraphQLClient(env.graphqlEndpoint, {
      headers: {
        authorization: `Bearer ${authToken}`
      }
    });
  });
  
  test('should query contract with nested data', async () => {
    const contract = await env.createFullContract();
    
    const query = gql`
      query GetContract($id: ID!) {
        contract(id: $id) {
          id
          title
          status
          budget {
            economic
            quality
            temporal
            reliability
            innovation
          }
          organization {
            id
            name
            wallet {
              balances {
                economic
              }
            }
          }
          bids {
            edges {
              node {
                id
                agent {
                  name
                  capabilities
                }
                proposedPrice {
                  economic
                }
                score
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
          marketAnalysis {
            efficiency
            priceRange {
              min
              max
              median
            }
            recommendedAction
          }
        }
      }
    `;
    
    const response = await client.request(query, { id: contract.id });
    
    expect(response.contract).toBeDefined();
    expect(response.contract.budget).toMatchObject({
      economic: expect.any(String),
      quality: expect.any(Number),
      temporal: expect.any(String),
      reliability: expect.any(Number),
      innovation: expect.any(String)
    });
    expect(response.contract.marketAnalysis.efficiency).toBeGreaterThan(0.9);
  });
  
  test('should handle mutations with complex inputs', async () => {
    const mutation = gql`
      mutation CreateContract($input: CreateContractInput!) {
        createContract(input: $input) {
          contract {
            id
            title
            status
            complexityScore
          }
          escrow {
            id
            locked {
              economic
            }
          }
          errors {
            field
            message
          }
        }
      }
    `;
    
    const input = {
      title: 'Enterprise Marketing Suite',
      requirements: ['strategy', 'content', 'seo', 'social', 'analytics'],
      budget: {
        economic: '50000',
        quality: 1.8,
        temporal: '2000',
        reliability: 0.9,
        innovation: '500'
      },
      constraints: {
        maxTeamSize: 7,
        minQuality: 1.6,
        requiredCertifications: ['google_ads', 'hubspot']
      }
    };
    
    const response = await client.request(mutation, { input });
    
    expect(response.createContract.contract).toBeDefined();
    expect(response.createContract.errors).toBeNull();
    expect(response.createContract.contract.complexityScore).toBeGreaterThan(7);
    expect(response.createContract.escrow.locked.economic).toBe('50000');
  });
  
  test('should subscribe to real-time market updates', async (done) => {
    const subscription = gql`
      subscription MarketUpdates($contractId: ID!) {
        contractUpdates(contractId: $contractId) {
          type
          contract {
            id
            status
            bids {
              totalCount
            }
          }
          bid {
            id
            agent {
              name
            }
            proposedPrice {
              economic
            }
          }
          timestamp
        }
      }
    `;
    
    const contract = await env.createAndPublishContract({
      budget: { economic: 10000n }
    });
    
    let updateCount = 0;
    const unsubscribe = client.subscribe(
      { query: subscription, variables: { contractId: contract.id } },
      {
        next: (data) => {
          updateCount++;
          
          expect(data.contractUpdates).toBeDefined();
          expect(data.contractUpdates.type).toMatch(/bid_submitted|bid_updated|status_changed/);
          
          if (updateCount === 3) {
            unsubscribe();
            done();
          }
        },
        error: (err) => done(err)
      }
    );
    
    // Trigger updates
    await env.submitBid({ contractId: contract.id, price: { economic: 8000n } });
    await env.submitBid({ contractId: contract.id, price: { economic: 7500n } });
    await env.submitBid({ contractId: contract.id, price: { economic: 9000n } });
  });
});
```

### 3. WebSocket Integration Tests

```typescript
describe('WebSocket Integration', () => {
  let ws: WebSocket;
  let wsClient: WebSocketClient;
  
  beforeEach(async () => {
    wsClient = new WebSocketClient(env.wsEndpoint);
    await wsClient.connect(authToken);
  });
  
  afterEach(async () => {
    await wsClient.disconnect();
  });
  
  test('should stream market events in real-time', async () => {
    const events: MarketEvent[] = [];
    
    await wsClient.subscribe('market:events', (event) => {
      events.push(event);
    });
    
    // Generate market activity
    const contract = await env.createAndPublishContract({
      budget: { economic: 5000n }
    });
    
    await env.submitBid({
      contractId: contract.id,
      price: { economic: 4500n }
    });
    
    await env.selectWinningBid(contract.id);
    
    // Wait for events
    await sleep(1000);
    
    expect(events).toContainEqual(
      expect.objectContaining({
        type: 'contract.published',
        data: expect.objectContaining({
          contractId: contract.id
        })
      })
    );
    
    expect(events).toContainEqual(
      expect.objectContaining({
        type: 'bid.submitted',
        data: expect.objectContaining({
          contractId: contract.id
        })
      })
    );
    
    expect(events).toContainEqual(
      expect.objectContaining({
        type: 'contract.assigned',
        data: expect.objectContaining({
          contractId: contract.id,
          efficiency: expect.any(Number)
        })
      })
    );
  });
  
  test('should handle agent communication protocol', async () => {
    const agent1 = await env.createAgent({ name: 'Agent 1' });
    const agent2 = await env.createAgent({ name: 'Agent 2' });
    
    const client1 = new WebSocketClient(env.wsEndpoint);
    const client2 = new WebSocketClient(env.wsEndpoint);
    
    await client1.connect(agent1.apiKey);
    await client2.connect(agent2.apiKey);
    
    const received: Message[] = [];
    
    await client2.subscribe(`agent:${agent2.id}:messages`, (msg) => {
      received.push(msg);
    });
    
    // Send message from agent1 to agent2
    await client1.send('agent:message', {
      to: agent2.id,
      type: 'collaboration_request',
      content: {
        contractId: 'contract-123',
        proposal: 'Let\'s team up!'
      }
    });
    
    await sleep(500);
    
    expect(received).toHaveLength(1);
    expect(received[0]).toMatchObject({
      from: agent1.id,
      to: agent2.id,
      type: 'collaboration_request'
    });
  });
  
  test('should maintain connection with heartbeat', async () => {
    let heartbeats = 0;
    
    wsClient.on('heartbeat', () => {
      heartbeats++;
    });
    
    // Wait for 3 heartbeats (assuming 1s interval)
    await sleep(3500);
    
    expect(heartbeats).toBeGreaterThanOrEqual(3);
    expect(wsClient.isConnected()).toBe(true);
  });
});
```

### 4. Error Scenario Tests

```typescript
describe('API Error Handling', () => {
  test('should handle invalid request data', async () => {
    const invalidContract = {
      title: '', // Empty title
      requirements: [], // No requirements
      budget: {
        economic: -1000, // Negative value
        quality: 3.0, // Out of range
        temporal: 'invalid' // Wrong type
      }
    };
    
    const response = await request(env.app)
      .post('/api/v1/contracts')
      .set('Authorization', `Bearer ${authToken}`)
      .send(invalidContract)
      .expect(400);
    
    expect(response.body).toMatchObject({
      error: 'ValidationError',
      details: expect.arrayContaining([
        expect.objectContaining({
          field: 'title',
          message: 'Title is required'
        }),
        expect.objectContaining({
          field: 'requirements',
          message: 'At least one requirement is needed'
        }),
        expect.objectContaining({
          field: 'budget.economic',
          message: 'Economic value must be positive'
        }),
        expect.objectContaining({
          field: 'budget.quality',
          message: 'Quality must be between 0 and 2'
        })
      ])
    });
  });
  
  test('should handle rate limiting', async () => {
    // Make rapid requests
    const requests = Array(100).fill(null).map(() =>
      request(env.app)
        .get('/api/v1/contracts')
        .set('Authorization', `Bearer ${authToken}`)
    );
    
    const responses = await Promise.all(requests);
    
    const rateLimited = responses.filter(r => r.status === 429);
    expect(rateLimited.length).toBeGreaterThan(0);
    
    const limitedResponse = rateLimited[0];
    expect(limitedResponse.headers['x-ratelimit-limit']).toBeDefined();
    expect(limitedResponse.headers['x-ratelimit-remaining']).toBe('0');
    expect(limitedResponse.headers['retry-after']).toBeDefined();
  });
  
  test('should handle authentication failures', async () => {
    // Invalid token
    await request(env.app)
      .get('/api/v1/contracts')
      .set('Authorization', 'Bearer invalid-token')
      .expect(401);
    
    // Expired token
    const expiredToken = await env.generateToken({
      organizationId,
      expiresIn: -1 // Already expired
    });
    
    await request(env.app)
      .get('/api/v1/contracts')
      .set('Authorization', `Bearer ${expiredToken}`)
      .expect(401);
    
    // Missing token
    await request(env.app)
      .get('/api/v1/contracts')
      .expect(401);
  });
  
  test('should handle partial failures gracefully', async () => {
    // Create contract with multiple operations
    const response = await request(env.app)
      .post('/api/v1/contracts/batch')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        contracts: [
          {
            title: 'Valid Contract 1',
            budget: { economic: 5000 },
            requirements: ['seo']
          },
          {
            title: 'Invalid Contract',
            budget: { economic: -1000 }, // Invalid
            requirements: ['content']
          },
          {
            title: 'Valid Contract 2',
            budget: { economic: 3000 },
            requirements: ['social']
          }
        ]
      })
      .expect(207); // Multi-status
    
    expect(response.body.results).toHaveLength(3);
    expect(response.body.results[0].status).toBe(201);
    expect(response.body.results[1].status).toBe(400);
    expect(response.body.results[2].status).toBe(201);
    
    expect(response.body.summary).toMatchObject({
      total: 3,
      successful: 2,
      failed: 1
    });
  });
});
```

## Test Utilities

### API Test Helpers

```typescript
// test-helpers/api-test-utils.ts
export class APITestUtils {
  static async authenticateAs(role: 'organization' | 'agent' | 'admin') {
    const credentials = {
      organization: { email: '<EMAIL>', password: 'test123' },
      agent: { apiKey: 'agent-test-key' },
      admin: { email: '<EMAIL>', password: 'admin123' }
    };
    
    const auth = await authenticate(credentials[role]);
    return auth.token;
  }
  
  static async createTestScenario(scenario: 'simple' | 'complex' | 'enterprise') {
    const scenarios = {
      simple: {
        contracts: 1,
        agents: 3,
        bidsPerContract: 2
      },
      complex: {
        contracts: 10,
        agents: 20,
        bidsPerContract: 5
      },
      enterprise: {
        contracts: 100,
        agents: 50,
        bidsPerContract: 10
      }
    };
    
    return await generateMarketScenario(scenarios[scenario]);
  }
  
  static validateAPIResponse(response: any, schema: Schema) {
    const validation = schema.validate(response);
    if (!validation.valid) {
      throw new Error(`Invalid API response: ${validation.errors.join(', ')}`);
    }
  }
  
  static async measureAPIPerformance(
    endpoint: string,
    method: string,
    iterations: number = 100
  ) {
    const times: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
      const start = Date.now();
      await makeRequest(endpoint, method);
      times.push(Date.now() - start);
    }
    
    return {
      avg: average(times),
      p50: percentile(times, 50),
      p95: percentile(times, 95),
      p99: percentile(times, 99),
      max: Math.max(...times)
    };
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- Endpoint Coverage: 100%
- Method Coverage: 100% (GET, POST, PUT, DELETE)
- Error Case Coverage: 90%
- Edge Case Coverage: 85%

### Critical Integration Points
1. Authentication flow
2. Multi-currency operations
3. Real-time subscriptions
4. Cross-service data consistency
5. Rate limiting behavior
6. Error propagation
7. Transaction atomicity

## Running API Integration Tests

```bash
# Run all API integration tests
pnpm test:integration:api

# Run REST API tests only
pnpm test:integration:api:rest

# Run GraphQL tests only
pnpm test:integration:api:graphql

# Run WebSocket tests only
pnpm test:integration:api:websocket

# Run with verbose logging
pnpm test:integration:api --verbose

# Run against staging environment
API_ENV=staging pnpm test:integration:api
```

Remember: APIs are the gateway to VibeLaunch's value. Every integration test ensures seamless, efficient, and secure communication!
