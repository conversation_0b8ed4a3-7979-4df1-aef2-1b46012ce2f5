# Agent Unit Tests

## Overview

Agents are the workforce of VibeLaunch, specialized AI entities that collaborate to deliver marketing services. These tests ensure agents operate efficiently, form optimal teams achieving 194.4% synergy, and maintain high performance standards.

## Test Categories

### 1. Agent Registration and Validation

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { AgentService, Agent, AgentCapability } from '@vibelaunch/agent-service';
import { ValidationError } from '@vibelaunch/errors';

describe('Agent Registration', () => {
  let agentService: AgentService;
  
  beforeEach(async () => {
    agentService = new AgentService();
  });
  
  test('should validate agent capabilities', async () => {
    const validAgent = {
      name: 'Content Creator Pro',
      type: 'specialized',
      capabilities: [
        {
          skill: 'content_creation',
          proficiency: 0.9,
          experience: 1000, // tasks completed
          specializations: ['blog_posts', 'social_media', 'newsletters']
        },
        {
          skill: 'copywriting',
          proficiency: 0.85,
          experience: 500,
          specializations: ['sales_copy', 'email_campaigns']
        }
      ],
      performance: {
        reliability: 0.92,
        quality: 1.7,
        speed: 1.2,
        innovation: 0.8
      },
      availability: {
        status: 'available',
        maxConcurrentTasks: 5,
        currentLoad: 0
      }
    };
    
    const agent = await agentService.registerAgent(validAgent);
    
    expect(agent.id).toBeDefined();
    expect(agent.status).toBe('active');
    expect(agent.wallet).toBeDefined();
    expect(agent.reputationScore).toBe(0); // New agent
  });
  
  test('should enforce capability constraints', async () => {
    // Too many capabilities
    const overloadedAgent = {
      name: 'Jack of All Trades',
      capabilities: Array(20).fill(null).map((_, i) => ({
        skill: `skill_${i}`,
        proficiency: 0.5
      }))
    };
    
    await expect(
      agentService.registerAgent(overloadedAgent)
    ).rejects.toThrow('Agent cannot have more than 10 capabilities');
    
    // Invalid proficiency range
    const invalidProficiency = {
      name: 'Invalid Agent',
      capabilities: [{
        skill: 'content_creation',
        proficiency: 1.5 // Above 1.0
      }]
    };
    
    await expect(
      agentService.registerAgent(invalidProficiency)
    ).rejects.toThrow('Proficiency must be between 0 and 1');
    
    // Conflicting capabilities
    const conflictingAgent = {
      name: 'Conflicted Agent',
      capabilities: [
        { skill: 'fast_delivery', proficiency: 0.9 },
        { skill: 'perfectionist', proficiency: 0.9 }
      ]
    };
    
    await expect(
      agentService.registerAgent(conflictingAgent)
    ).rejects.toThrow('Conflicting capabilities detected');
  });
  
  test('should initialize performance metrics', async () => {
    const agent = await agentService.registerAgent({
      name: 'New Agent',
      capabilities: [{ skill: 'seo', proficiency: 0.8 }]
    });
    
    expect(agent.performance).toMatchObject({
      tasksCompleted: 0,
      tasksAssigned: 0,
      successRate: 0,
      averageRating: 0,
      totalEarnings: {
        economic: 0n,
        quality: 0,
        temporal: 0n,
        reliability: 0,
        innovation: 0n
      },
      historicalData: {
        daily: [],
        weekly: [],
        monthly: []
      }
    });
    
    // Verify initial wallet state
    expect(agent.wallet.balances).toMatchObject({
      economic: 0n,
      quality: 1.0, // Base quality
      temporal: 0n,
      reliability: 0.5, // Starting reliability
      innovation: 0n
    });
  });
  
  test('should calculate agent capability score', async () => {
    const agents = [
      {
        name: 'Specialist',
        capabilities: [
          { skill: 'content_creation', proficiency: 0.95, experience: 5000 }
        ],
        expectedScore: 0.95 // High proficiency, single skill
      },
      {
        name: 'Versatile',
        capabilities: [
          { skill: 'content_creation', proficiency: 0.8, experience: 1000 },
          { skill: 'seo', proficiency: 0.75, experience: 800 },
          { skill: 'social_media', proficiency: 0.7, experience: 600 }
        ],
        expectedScore: 0.82 // Weighted average with versatility bonus
      },
      {
        name: 'Master',
        capabilities: [
          { skill: 'strategy', proficiency: 0.98, experience: 10000 },
          { skill: 'analytics', proficiency: 0.95, experience: 8000 }
        ],
        expectedScore: 0.99 // Master-level with experience bonus
      }
    ];
    
    for (const testCase of agents) {
      const agent = await agentService.registerAgent(testCase);
      const score = agentService.calculateCapabilityScore(agent);
      
      expect(score).toBeCloseTo(testCase.expectedScore, 2);
    }
  });
});
```

### 2. Team Formation Tests

```typescript
describe('Team Formation', () => {
  let agentService: AgentService;
  let teamBuilder: TeamBuilder;
  let agents: Agent[];
  
  beforeEach(async () => {
    agentService = new AgentService();
    teamBuilder = new TeamBuilder(agentService);
    
    // Create diverse agent pool
    agents = await createAgentPool();
  });
  
  test('should calculate 194.4% synergy for optimal team', async () => {
    // Create optimal 5-agent team with complementary skills
    const optimalAgents = [
      await agentService.registerAgent({
        name: 'Content Strategist',
        capabilities: [
          { skill: 'content_strategy', proficiency: 0.95 },
          { skill: 'market_research', proficiency: 0.85 }
        ],
        performance: { reliability: 0.92, quality: 1.8 }
      }),
      await agentService.registerAgent({
        name: 'Creative Writer',
        capabilities: [
          { skill: 'content_creation', proficiency: 0.93 },
          { skill: 'storytelling', proficiency: 0.90 }
        ],
        performance: { reliability: 0.90, quality: 1.85 }
      }),
      await agentService.registerAgent({
        name: 'SEO Expert',
        capabilities: [
          { skill: 'seo', proficiency: 0.91 },
          { skill: 'keyword_research', proficiency: 0.88 }
        ],
        performance: { reliability: 0.88, quality: 1.75 }
      }),
      await agentService.registerAgent({
        name: 'Social Media Guru',
        capabilities: [
          { skill: 'social_media', proficiency: 0.89 },
          { skill: 'community_management', proficiency: 0.87 }
        ],
        performance: { reliability: 0.91, quality: 1.7 }
      }),
      await agentService.registerAgent({
        name: 'Analytics Master',
        capabilities: [
          { skill: 'data_analytics', proficiency: 0.94 },
          { skill: 'reporting', proficiency: 0.89 }
        ],
        performance: { reliability: 0.93, quality: 1.82 }
      })
    ];
    
    const team = await teamBuilder.formTeam({
      agents: optimalAgents,
      requirements: [
        'content_strategy',
        'content_creation',
        'seo',
        'social_media',
        'data_analytics'
      ]
    });
    
    const synergy = teamBuilder.calculateSynergy(team);
    
    expect(synergy.total).toBeCloseTo(1.944, 3);
    expect(synergy.breakdown).toMatchObject({
      skillCoverage: 1.0, // All skills covered
      skillDiversity: expect.closeTo(0.95, 0.05),
      experienceSynergy: expect.closeTo(0.92, 0.05),
      communicationEfficiency: expect.closeTo(0.90, 0.05),
      innovationPotential: expect.closeTo(0.88, 0.05)
    });
    
    // Verify mathematical formula
    const calculatedSynergy = 
      synergy.breakdown.skillCoverage * 
      synergy.breakdown.skillDiversity * 
      synergy.breakdown.experienceSynergy * 
      synergy.breakdown.communicationEfficiency * 
      synergy.breakdown.innovationPotential * 
      2.5; // Base multiplier for 5-agent team
    
    expect(calculatedSynergy).toBeCloseTo(synergy.total, 2);
  });
  
  test('should enforce team size limits', async () => {
    const tooManyAgents = agents.slice(0, 8); // 8 agents
    
    await expect(
      teamBuilder.formTeam({
        agents: tooManyAgents,
        requirements: ['content_creation']
      })
    ).rejects.toThrow('Team size cannot exceed 7 agents');
    
    const tooFewAgents = [agents[0]]; // 1 agent for team formation
    
    await expect(
      teamBuilder.formTeam({
        agents: tooFewAgents,
        requirements: ['content_creation', 'seo'] // Requires team
      })
    ).rejects.toThrow('Multiple requirements need at least 2 agents');
  });
  
  test('should optimize team composition for requirements', async () => {
    const requirements = [
      'content_creation',
      'seo',
      'social_media',
      'email_marketing',
      'analytics'
    ];
    
    const team = await teamBuilder.optimizeTeam({
      agentPool: agents,
      requirements,
      constraints: {
        maxBudget: { economic: 10000n },
        minQuality: 1.5,
        maxTeamSize: 5
      }
    });
    
    // Verify all requirements covered
    const teamSkills = new Set(
      team.agents.flatMap(a => 
        a.capabilities.map(c => c.skill)
      )
    );
    
    requirements.forEach(req => {
      expect(teamSkills.has(req)).toBe(true);
    });
    
    // Verify constraints met
    expect(team.agents.length).toBeLessThanOrEqual(5);
    expect(team.totalCost.economic).toBeLessThanOrEqual(10000n);
    expect(team.averageQuality).toBeGreaterThanOrEqual(1.5);
    
    // Verify near-optimal synergy
    expect(team.synergy).toBeGreaterThan(1.5);
  });
  
  test('should handle team collaboration history', async () => {
    const agentA = agents[0];
    const agentB = agents[1];
    const agentC = agents[2];
    
    // Record successful collaboration
    await teamBuilder.recordCollaboration({
      agents: [agentA.id, agentB.id],
      contractId: 'contract-001',
      outcome: 'success',
      performance: {
        quality: 1.8,
        timeliness: 0.95,
        communication: 0.9
      }
    });
    
    // Form new team with history
    const teamWithHistory = await teamBuilder.formTeam({
      agents: [agentA, agentB, agentC],
      requirements: ['content_creation', 'seo']
    });
    
    const synergyWithHistory = teamBuilder.calculateSynergy(teamWithHistory);
    
    // Form team without history
    const freshAgents = await createAgentPool();
    const teamWithoutHistory = await teamBuilder.formTeam({
      agents: freshAgents.slice(0, 3),
      requirements: ['content_creation', 'seo']
    });
    
    const synergyWithoutHistory = teamBuilder.calculateSynergy(teamWithoutHistory);
    
    // Team with history should have better synergy
    expect(synergyWithHistory.total).toBeGreaterThan(synergyWithoutHistory.total);
    expect(synergyWithHistory.breakdown.communicationEfficiency).toBeGreaterThan(
      synergyWithoutHistory.breakdown.communicationEfficiency
    );
  });
});
```

### 3. Agent Performance Tests

```typescript
describe('Agent Performance Tracking', () => {
  let agentService: AgentService;
  let performanceTracker: PerformanceTracker;
  let agent: Agent;
  
  beforeEach(async () => {
    agentService = new AgentService();
    performanceTracker = new PerformanceTracker(agentService);
    
    agent = await agentService.registerAgent({
      name: 'Test Agent',
      capabilities: [{ skill: 'content_creation', proficiency: 0.8 }]
    });
  });
  
  test('should update performance metrics correctly', async () => {
    const taskResults = [
      {
        taskId: 'task-001',
        contractId: 'contract-001',
        quality: 1.8,
        completionTime: 3, // days
        clientRating: 4.5,
        earned: { economic: 1000n }
      },
      {
        taskId: 'task-002',
        contractId: 'contract-002',
        quality: 1.6,
        completionTime: 5,
        clientRating: 4.0,
        earned: { economic: 1500n }
      },
      {
        taskId: 'task-003',
        contractId: 'contract-003',
        quality: 1.9,
        completionTime: 2,
        clientRating: 5.0,
        earned: { economic: 2000n }
      }
    ];
    
    // Record task completions
    for (const result of taskResults) {
      await performanceTracker.recordTaskCompletion(agent.id, result);
    }
    
    // Fetch updated metrics
    const metrics = await performanceTracker.getAgentMetrics(agent.id);
    
    expect(metrics.tasksCompleted).toBe(3);
    expect(metrics.successRate).toBe(1.0); // All successful
    expect(metrics.averageQuality).toBeCloseTo(1.77, 2);
    expect(metrics.averageRating).toBeCloseTo(4.5, 1);
    expect(metrics.totalEarnings.economic).toBe(4500n);
    
    // Verify reputation update
    const updatedAgent = await agentService.getAgent(agent.id);
    expect(updatedAgent.wallet.balances.reliability).toBeGreaterThan(0.5);
  });
  
  test('should calculate performance trends', async () => {
    // Simulate performance over time
    const performanceData = generatePerformanceHistory(30); // 30 days
    
    for (const dayData of performanceData) {
      await performanceTracker.recordDailyMetrics(agent.id, dayData);
    }
    
    const trends = await performanceTracker.analyzeTrends(agent.id);
    
    expect(trends).toMatchObject({
      qualityTrend: expect.any(String), // 'improving', 'stable', 'declining'
      efficiencyTrend: expect.any(String),
      reliabilityScore: expect.any(Number),
      predictedPerformance: {
        nextWeek: expect.any(Object),
        nextMonth: expect.any(Object)
      },
      recommendations: expect.any(Array)
    });
    
    // Verify trend detection
    if (trends.qualityTrend === 'improving') {
      expect(trends.recommendations).toContain('Consider more complex tasks');
    }
    
    if (trends.efficiencyTrend === 'declining') {
      expect(trends.recommendations).toContain('Review workload distribution');
    }
  });
  
  test('should apply performance-based rewards and penalties', async () => {
    const initialReliability = agent.wallet.balances.reliability;
    
    // Successful streak
    for (let i = 0; i < 5; i++) {
      await performanceTracker.recordTaskCompletion(agent.id, {
        taskId: `success-${i}`,
        quality: 1.8,
        completionTime: 3,
        clientRating: 5.0,
        onTime: true
      });
    }
    
    let updated = await agentService.getAgent(agent.id);
    expect(updated.wallet.balances.reliability).toBeGreaterThan(initialReliability);
    const improvedReliability = updated.wallet.balances.reliability;
    
    // Failed task
    await performanceTracker.recordTaskCompletion(agent.id, {
      taskId: 'failure-001',
      quality: 0.8,
      completionTime: 10,
      clientRating: 2.0,
      onTime: false
    });
    
    updated = await agentService.getAgent(agent.id);
    expect(updated.wallet.balances.reliability).toBeLessThan(improvedReliability);
    
    // Verify innovation bonus for creative solutions
    await performanceTracker.recordTaskCompletion(agent.id, {
      taskId: 'innovative-001',
      quality: 2.0,
      innovation: true,
      clientRating: 5.0,
      earned: { 
        economic: 3000n,
        innovation: 100n // Bonus
      }
    });
    
    updated = await agentService.getAgent(agent.id);
    expect(updated.wallet.balances.innovation).toBeGreaterThan(0n);
  });
});
```

### 4. Agent Specialization Tests

```typescript
describe('Agent Specialization System', () => {
  let specializationService: SpecializationService;
  
  beforeEach(() => {
    specializationService = new SpecializationService();
  });
  
  test('should track and evolve specializations', async () => {
    const agent = await createAgent({
      capabilities: [
        { skill: 'content_creation', proficiency: 0.7 }
      ]
    });
    
    // Complete tasks in specific domains
    const blogTasks = Array(20).fill(null).map((_, i) => ({
      taskId: `blog-${i}`,
      type: 'blog_post',
      performance: { quality: 1.7 + (i * 0.01) } // Improving
    }));
    
    for (const task of blogTasks) {
      await specializationService.recordTaskType(agent.id, task);
    }
    
    // Check specialization evolution
    const specializations = await specializationService.getSpecializations(agent.id);
    
    expect(specializations).toContainEqual({
      domain: 'blog_writing',
      level: 'intermediate',
      tasksCompleted: 20,
      averageQuality: expect.any(Number),
      proficiencyBonus: 0.1 // 10% bonus for specialization
    });
    
    // Verify capability improvement
    const updatedAgent = await agentService.getAgent(agent.id);
    const contentCapability = updatedAgent.capabilities.find(
      c => c.skill === 'content_creation'
    );
    
    expect(contentCapability.proficiency).toBeGreaterThan(0.7);
    expect(contentCapability.specializations).toContain('blog_writing');
  });
  
  test('should match agents to tasks based on specialization', async () => {
    const agents = [
      await createAgent({
        name: 'Blog Specialist',
        specializations: ['blog_writing'],
        blogTasksCompleted: 100
      }),
      await createAgent({
        name: 'Social Media Expert',
        specializations: ['social_media_content'],
        socialTasksCompleted: 150
      }),
      await createAgent({
        name: 'Generalist',
        specializations: [],
        tasksCompleted: 50
      })
    ];
    
    const blogTask = {
      type: 'blog_post',
      requirements: ['content_creation'],
      preferences: ['blog_writing']
    };
    
    const matches = await specializationService.matchAgentsToTask(
      agents,
      blogTask
    );
    
    // Blog specialist should rank first
    expect(matches[0].agent.name).toBe('Blog Specialist');
    expect(matches[0].matchScore).toBeGreaterThan(matches[1].matchScore);
    expect(matches[0].specializationBonus).toBeGreaterThan(0);
  });
  
  test('should handle multi-domain specialization', async () => {
    const versatileAgent = await createAgent({
      name: 'Multi-Specialist',
      capabilities: [
        { skill: 'content_creation', proficiency: 0.85 },
        { skill: 'seo', proficiency: 0.80 },
        { skill: 'analytics', proficiency: 0.75 }
      ]
    });
    
    // Complete diverse tasks
    const diverseTasks = [
      ...Array(30).fill({ type: 'blog_post', skill: 'content_creation' }),
      ...Array(25).fill({ type: 'seo_optimization', skill: 'seo' }),
      ...Array(20).fill({ type: 'report_generation', skill: 'analytics' })
    ];
    
    for (const task of diverseTasks) {
      await specializationService.recordTaskType(versatileAgent.id, {
        ...task,
        performance: { quality: 1.6 + Math.random() * 0.3 }
      });
    }
    
    const specializations = await specializationService.getSpecializations(
      versatileAgent.id
    );
    
    expect(specializations).toHaveLength(3);
    expect(specializations.map(s => s.domain)).toContain('blog_writing');
    expect(specializations.map(s => s.domain)).toContain('seo_optimization');
    expect(specializations.map(s => s.domain)).toContain('data_analytics');
    
    // Verify T-shaped skill development
    const profile = await specializationService.getSkillProfile(versatileAgent.id);
    expect(profile.type).toBe('t-shaped');
    expect(profile.primarySpecialization).toBe('blog_writing'); // Most tasks
    expect(profile.secondarySpecializations).toHaveLength(2);
  });
});
```

### 5. Agent Communication Tests

```typescript
describe('Agent Communication Protocol', () => {
  let communicationService: CommunicationService;
  let agents: Agent[];
  
  beforeEach(async () => {
    communicationService = new CommunicationService();
    agents = await createAgentTeam(5);
  });
  
  test('should handle inter-agent communication', async () => {
    const message = {
      from: agents[0].id,
      to: agents[1].id,
      type: 'task_coordination',
      content: {
        taskId: 'task-001',
        request: 'need_seo_keywords',
        urgency: 'high'
      }
    };
    
    const response = await communicationService.sendMessage(message);
    
    expect(response.delivered).toBe(true);
    expect(response.acknowledgedAt).toBeDefined();
    
    // Verify message in recipient's queue
    const inbox = await communicationService.getInbox(agents[1].id);
    expect(inbox.unread).toContainEqual(
      expect.objectContaining({
        from: agents[0].id,
        type: 'task_coordination'
      })
    );
  });
  
  test('should coordinate team communication efficiently', async () => {
    const team = {
      id: 'team-001',
      agents: agents,
      leaderId: agents[0].id
    };
    
    // Broadcast from team lead
    const broadcast = await communicationService.broadcastToTeam({
      teamId: team.id,
      from: team.leaderId,
      message: {
        type: 'status_update',
        content: {
          phase: 'execution',
          progress: 0.3,
          blockers: []
        }
      }
    });
    
    expect(broadcast.delivered).toBe(agents.length - 1); // All except sender
    expect(broadcast.failures).toHaveLength(0);
    
    // Verify team communication patterns
    const patterns = await communicationService.analyzeTeamCommunication(team.id);
    
    expect(patterns).toMatchObject({
      centralityScore: expect.any(Number), // Lead should have high centrality
      responsiveness: expect.any(Object),
      communicationEfficiency: expect.any(Number),
      bottlenecks: expect.any(Array)
    });
  });
  
  test('should handle communication protocols', async () => {
    // Test request-response protocol
    const request = await communicationService.createRequest({
      from: agents[0].id,
      to: agents[1].id,
      type: 'capability_check',
      requiresResponse: true,
      timeout: 5000 // 5 seconds
    });
    
    // Simulate response
    const response = await communicationService.respond({
      requestId: request.id,
      from: agents[1].id,
      content: {
        available: true,
        estimatedTime: 2 // hours
      }
    });
    
    expect(response.protocol).toBe('request-response');
    expect(response.responseTime).toBeLessThan(5000);
    
    // Test publish-subscribe protocol
    await communicationService.subscribe({
      agentId: agents[2].id,
      topics: ['contract_updates', 'team_announcements']
    });
    
    const publication = await communicationService.publish({
      topic: 'contract_updates',
      content: {
        contractId: 'contract-001',
        update: 'requirements_changed'
      }
    });
    
    expect(publication.deliveredTo).toContain(agents[2].id);
  });
});
```

## Test Utilities

### Agent Test Helpers

```typescript
// test-helpers/agent-test-utils.ts
export class AgentTestUtils {
  static async createAgentPool(size: number = 20): Promise<Agent[]> {
    const agentTypes = [
      { 
        type: 'content_specialist',
        skills: ['content_creation', 'copywriting', 'storytelling'],
        qualityRange: [1.6, 1.9]
      },
      {
        type: 'seo_expert',
        skills: ['seo', 'keyword_research', 'link_building'],
        qualityRange: [1.5, 1.8]
      },
      {
        type: 'social_media_manager',
        skills: ['social_media', 'community_management', 'engagement'],
        qualityRange: [1.4, 1.7]
      },
      {
        type: 'data_analyst',
        skills: ['analytics', 'reporting', 'data_visualization'],
        qualityRange: [1.5, 1.8]
      },
      {
        type: 'designer',
        skills: ['graphic_design', 'ui_ux', 'branding'],
        qualityRange: [1.6, 1.9]
      }
    ];
    
    const agents = [];
    
    for (let i = 0; i < size; i++) {
      const type = faker.helpers.arrayElement(agentTypes);
      const capabilities = faker.helpers.arrayElements(
        type.skills,
        { min: 1, max: 3 }
      ).map(skill => ({
        skill,
        proficiency: faker.number.float({ min: 0.7, max: 0.95 }),
        experience: faker.number.int({ min: 10, max: 1000 })
      }));
      
      const agent = await agentService.registerAgent({
        name: `${faker.person.firstName()} ${type.type}`,
        type: type.type,
        capabilities,
        performance: {
          reliability: faker.number.float({ min: 0.75, max: 0.95 }),
          quality: faker.number.float({ 
            min: type.qualityRange[0], 
            max: type.qualityRange[1] 
          })
        }
      });
      
      agents.push(agent);
    }
    
    return agents;
  }
  
  static generateSynergyMatrix(agents: Agent[]): number[][] {
    const matrix: number[][] = [];
    
    for (let i = 0; i < agents.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < agents.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0;
        } else {
          // Calculate pairwise synergy based on complementary skills
          const synergy = this.calculatePairwiseSynergy(agents[i], agents[j]);
          matrix[i][j] = synergy;
        }
      }
    }
    
    return matrix;
  }
  
  static async simulateAgentPerformance(
    agent: Agent,
    days: number
  ): Promise<PerformanceHistory> {
    const history: PerformanceHistory = [];
    
    for (let day = 0; day < days; day++) {
      const tasksToday = faker.number.int({ min: 0, max: 5 });
      const dayPerformance = {
        date: new Date(Date.now() - (days - day) * 24 * 60 * 60 * 1000),
        tasksCompleted: tasksToday,
        averageQuality: agent.performance.quality + faker.number.float({ min: -0.2, max: 0.2 }),
        earnings: {
          economic: BigInt(tasksToday * faker.number.int({ min: 500, max: 2000 }))
        },
        clientRatings: Array(tasksToday).fill(null).map(() => 
          faker.number.float({ min: 3.5, max: 5.0 })
        )
      };
      
      history.push(dayPerformance);
    }
    
    return history;
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- Statement Coverage: 95%
- Branch Coverage: 95%
- Function Coverage: 95%
- Line Coverage: 95%

### Critical Paths (Must Test)
1. Agent registration and validation
2. Capability constraints and scoring
3. Team formation algorithm
4. Synergy calculations (194.4% target)
5. Performance tracking and updates
6. Specialization evolution
7. Communication protocols

## Running Agent Tests

```bash
# Run all agent tests
pnpm test:unit:agents

# Run with coverage
pnpm test:unit:agents --coverage

# Run specific test suite
pnpm test:unit:agents team-formation

# Run in watch mode
pnpm test:unit:agents --watch

# Run synergy calculation tests specifically
pnpm test:unit:agents --grep "synergy"
```

Remember: Agents are the heart of VibeLaunch. Their collaboration and synergy create exponential value!
