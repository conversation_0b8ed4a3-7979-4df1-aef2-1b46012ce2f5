# Marketplace Unit Tests

## Overview

The marketplace is the core engine of VibeLaunch, where contracts meet agents through an efficient bidding system. These tests ensure the marketplace achieves 95%+ efficiency while maintaining fairness and economic law compliance.

## Test Categories

### 1. Contract Creation and Validation

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { MarketplaceService, Contract } from '@vibelaunch/marketplace';
import { ValidationError } from '@vibelaunch/errors';

describe('Contract Creation', () => {
  let marketplace: MarketplaceService;
  let organizationId: string;
  
  beforeEach(async () => {
    marketplace = new MarketplaceService();
    organizationId = 'test-org-001';
  });
  
  test('should validate multi-currency budgets', async () => {
    const validContract = {
      organizationId,
      title: 'Marketing Campaign Q1 2024',
      description: 'Comprehensive digital marketing campaign',
      requirements: ['content_creation', 'seo', 'social_media'],
      budget: {
        economic: 10000n,
        quality: 1.5,
        temporal: 500n,
        reliability: 0.8,
        innovation: 100n
      },
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    };
    
    const contract = await marketplace.createContract(validContract);
    
    expect(contract.id).toBeDefined();
    expect(contract.status).toBe('draft');
    expect(contract.budget).toEqual(validContract.budget);
    
    // Verify escrow created
    expect(contract.escrowId).toBeDefined();
    expect(contract.escrowBalance.economic).toBe(10000n);
  });
  
  test('should enforce minimum viability requirements', async () => {
    // Contract with insufficient budget
    const lowBudgetContract = {
      organizationId,
      title: 'Small Task',
      requirements: ['content_creation'],
      budget: {
        economic: 50n, // Below minimum
        quality: 1.0,
        temporal: 10n,
        reliability: 0.5,
        innovation: 0n
      }
    };
    
    await expect(
      marketplace.createContract(lowBudgetContract)
    ).rejects.toThrow('Budget below minimum viability threshold');
    
    // Contract with unrealistic timeline
    const rushContract = {
      organizationId,
      title: 'Rush Job',
      requirements: ['content_creation', 'seo', 'design'],
      budget: { economic: 5000n },
      deadline: new Date(Date.now() + 1 * 60 * 60 * 1000) // 1 hour
    };
    
    await expect(
      marketplace.createContract(rushContract)
    ).rejects.toThrow('Timeline insufficient for requirements');
  });
  
  test('should validate requirement combinations', async () => {
    // Conflicting requirements
    const conflictingContract = {
      organizationId,
      title: 'Conflicting Requirements',
      requirements: ['speed_optimization', 'heavy_graphics'], // Conflicts
      budget: { economic: 5000n }
    };
    
    await expect(
      marketplace.createContract(conflictingContract)
    ).rejects.toThrow('Conflicting requirements detected');
    
    // Synergistic requirements
    const synergisticContract = {
      organizationId,
      title: 'Synergistic Campaign',
      requirements: ['content_creation', 'seo', 'content_distribution'],
      budget: { economic: 8000n }
    };
    
    const contract = await marketplace.createContract(synergisticContract);
    expect(contract.synergyBonus).toBeGreaterThan(0);
  });
  
  test('should calculate contract complexity score', async () => {
    const contracts = [
      {
        title: 'Simple Blog Post',
        requirements: ['content_creation'],
        budget: { economic: 500n },
        expectedComplexity: 1
      },
      {
        title: 'Multi-Channel Campaign',
        requirements: ['content_creation', 'seo', 'social_media', 'email_marketing'],
        budget: { economic: 10000n },
        expectedComplexity: 4
      },
      {
        title: 'Enterprise Transformation',
        requirements: ['strategy', 'content', 'seo', 'social', 'analytics', 'automation'],
        budget: { economic: 50000n, innovation: 1000n },
        expectedComplexity: 8
      }
    ];
    
    for (const testCase of contracts) {
      const contract = await marketplace.createContract({
        organizationId,
        ...testCase
      });
      
      expect(contract.complexityScore).toBeCloseTo(testCase.expectedComplexity, 1);
    }
  });
});
```

### 2. Bidding System Tests

```typescript
describe('Bidding System', () => {
  let marketplace: MarketplaceService;
  let contract: Contract;
  let agents: Agent[];
  
  beforeEach(async () => {
    marketplace = new MarketplaceService();
    
    // Create test contract
    contract = await marketplace.createContract({
      organizationId: 'test-org',
      title: 'Test Campaign',
      requirements: ['content_creation', 'seo'],
      budget: { economic: 5000n, quality: 1.5 },
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    });
    
    // Publish contract
    await marketplace.publishContract(contract.id);
    
    // Create test agents
    agents = await Promise.all([
      marketplace.createAgent({
        name: 'Content Specialist',
        capabilities: ['content_creation'],
        quality: 1.8,
        reliability: 0.9
      }),
      marketplace.createAgent({
        name: 'SEO Expert',
        capabilities: ['seo'],
        quality: 1.6,
        reliability: 0.85
      }),
      marketplace.createAgent({
        name: 'Full Service Agency',
        capabilities: ['content_creation', 'seo', 'social_media'],
        quality: 1.4,
        reliability: 0.8
      })
    ]);
  });
  
  test('should select optimal bid for 95%+ efficiency', async () => {
    // Submit bids
    const bids = [
      {
        agentId: agents[0].id,
        contractId: contract.id,
        proposedPrice: { economic: 3000n },
        estimatedQuality: 1.8,
        completionTime: 5 // days
      },
      {
        agentId: agents[1].id,
        contractId: contract.id,
        proposedPrice: { economic: 2500n },
        estimatedQuality: 1.6,
        completionTime: 4
      },
      {
        agentId: agents[2].id,
        contractId: contract.id,
        proposedPrice: { economic: 4500n },
        estimatedQuality: 1.4,
        completionTime: 3
      }
    ];
    
    const submittedBids = await Promise.all(
      bids.map(bid => marketplace.submitBid(bid))
    );
    
    // Select winning bid
    const selection = await marketplace.selectWinningBid(contract.id);
    
    // Verify efficiency
    expect(selection.efficiency).toBeGreaterThanOrEqual(0.95);
    expect(selection.reasoning).toContain('optimal value');
    
    // Verify selection considers multiple factors
    expect(selection.factors).toMatchObject({
      priceScore: expect.any(Number),
      qualityScore: expect.any(Number),
      timeScore: expect.any(Number),
      reliabilityScore: expect.any(Number)
    });
  });
  
  test('should prevent bid manipulation', async () => {
    // Attempt to submit multiple bids from same agent
    const agent = agents[0];
    
    await marketplace.submitBid({
      agentId: agent.id,
      contractId: contract.id,
      proposedPrice: { economic: 3000n }
    });
    
    await expect(
      marketplace.submitBid({
        agentId: agent.id,
        contractId: contract.id,
        proposedPrice: { economic: 2500n } // Lower second bid
      })
    ).rejects.toThrow('Agent already has active bid');
    
    // Attempt to bid below cost
    await expect(
      marketplace.submitBid({
        agentId: agents[1].id,
        contractId: contract.id,
        proposedPrice: { economic: 100n } // Unrealistically low
      })
    ).rejects.toThrow('Bid below minimum viable cost');
    
    // Attempt to bid after deadline
    const expiredContract = await marketplace.createContract({
      organizationId: 'test-org',
      title: 'Expired Contract',
      budget: { economic: 1000n },
      biddingDeadline: new Date(Date.now() - 1000) // Past
    });
    
    await expect(
      marketplace.submitBid({
        agentId: agents[0].id,
        contractId: expiredContract.id,
        proposedPrice: { economic: 800n }
      })
    ).rejects.toThrow('Bidding period has ended');
  });
  
  test('should handle competitive bidding dynamics', async () => {
    // Enable real-time bidding
    const biddingSession = await marketplace.startBiddingSession(contract.id);
    
    // Agent 1 submits initial bid
    const bid1 = await biddingSession.submitBid({
      agentId: agents[0].id,
      proposedPrice: { economic: 4000n }
    });
    
    // Market should notify other agents
    const notifications = await biddingSession.getNotifications();
    expect(notifications).toHaveLength(agents.length - 1);
    
    // Agent 2 submits competitive bid
    const bid2 = await biddingSession.submitBid({
      agentId: agents[1].id,
      proposedPrice: { economic: 3800n }
    });
    
    // Verify bid ranking updates
    const ranking = await biddingSession.getCurrentRanking();
    expect(ranking[0].agentId).toBe(agents[1].id);
    
    // Test bid increment rules
    await expect(
      biddingSession.submitBid({
        agentId: agents[2].id,
        proposedPrice: { economic: 3799n } // Below minimum increment
      })
    ).rejects.toThrow('Bid increment too small');
  });
  
  test('should calculate bid scores accurately', async () => {
    const bid = {
      agentId: agents[0].id,
      contractId: contract.id,
      proposedPrice: { economic: 3000n },
      estimatedQuality: 1.8,
      completionTime: 5,
      teamComposition: [agents[0].id] // Solo
    };
    
    const score = await marketplace.calculateBidScore(bid, contract);
    
    // Verify score components
    expect(score.total).toBeGreaterThan(0);
    expect(score.breakdown).toMatchObject({
      economic: expect.any(Number),
      quality: expect.any(Number),
      temporal: expect.any(Number),
      reliability: expect.any(Number),
      innovation: expect.any(Number)
    });
    
    // Verify scoring formula
    const manualCalculation = 
      (score.breakdown.economic * 0.3) +
      (score.breakdown.quality * 0.25) +
      (score.breakdown.temporal * 0.2) +
      (score.breakdown.reliability * 0.15) +
      (score.breakdown.innovation * 0.1);
    
    expect(score.total).toBeCloseTo(manualCalculation, 2);
  });
});
```

### 3. Market Efficiency Tests

```typescript
describe('Market Efficiency Metrics', () => {
  let marketplace: MarketplaceService;
  let efficiencyCalculator: EfficiencyCalculator;
  
  beforeEach(() => {
    marketplace = new MarketplaceService();
    efficiencyCalculator = new EfficiencyCalculator(marketplace);
  });
  
  test('should calculate overall market efficiency', async () => {
    // Create diverse market conditions
    const contracts = await createTestMarket({
      contracts: 100,
      agents: 50,
      averageBidsPerContract: 5
    });
    
    const efficiency = await efficiencyCalculator.calculateMarketEfficiency();
    
    expect(efficiency.overall).toBeGreaterThanOrEqual(0.95);
    expect(efficiency.metrics).toMatchObject({
      priceDiscovery: expect.any(Number), // How quickly prices converge
      allocationEfficiency: expect.any(Number), // Best agent for each job
      completionRate: expect.any(Number), // Successful completions
      timeToMatch: expect.any(Number), // Speed of matching
      liquidityRatio: expect.any(Number) // Bids per contract
    });
  });
  
  test('should detect and report bottlenecks', async () => {
    // Create bottleneck scenario
    await createBottleneckScenario({
      manyContracts: 100,
      fewAgentsWithSkill: 2,
      requiredSkill: 'blockchain_development'
    });
    
    const analysis = await efficiencyCalculator.analyzeBottlenecks();
    
    expect(analysis.bottlenecks).toContainEqual({
      type: 'skill_shortage',
      skill: 'blockchain_development',
      severity: 'high',
      impact: expect.objectContaining({
        unmatchedContracts: expect.any(Number),
        priceInflation: expect.any(Number),
        delayedProjects: expect.any(Number)
      }),
      recommendations: expect.arrayContaining([
        'Recruit more blockchain developers',
        'Increase training incentives',
        'Consider skill substitution'
      ])
    });
  });
  
  test('should optimize for different market conditions', async () => {
    const scenarios = [
      {
        name: 'High Demand',
        contracts: 200,
        agents: 50,
        expectedStrategy: 'agent_incentivization'
      },
      {
        name: 'Oversupply',
        contracts: 50,
        agents: 200,
        expectedStrategy: 'quality_differentiation'
      },
      {
        name: 'Balanced',
        contracts: 100,
        agents: 100,
        expectedStrategy: 'efficiency_maximization'
      }
    ];
    
    for (const scenario of scenarios) {
      await clearMarket();
      await createMarketCondition(scenario);
      
      const optimization = await efficiencyCalculator.optimizeMarket();
      
      expect(optimization.strategy).toBe(scenario.expectedStrategy);
      expect(optimization.projectedEfficiency).toBeGreaterThan(0.95);
    }
  });
  
  test('should track efficiency trends over time', async () => {
    const periods = 10;
    const efficiencyHistory = [];
    
    for (let period = 0; period < periods; period++) {
      // Simulate market activity
      await simulateMarketPeriod({
        duration: '1 hour',
        contractRate: 10, // per hour
        completionRate: 0.9
      });
      
      const efficiency = await efficiencyCalculator.calculateMarketEfficiency();
      efficiencyHistory.push(efficiency);
    }
    
    // Analyze trends
    const trend = efficiencyCalculator.analyzeTrend(efficiencyHistory);
    
    expect(trend.direction).toBe('improving');
    expect(trend.volatility).toBeLessThan(0.05); // Stable
    expect(trend.forecast.nextPeriod).toBeGreaterThan(0.95);
  });
});
```

### 4. Contract Matching Algorithm Tests

```typescript
describe('Contract Matching Algorithm', () => {
  let matchingEngine: MatchingEngine;
  
  beforeEach(() => {
    matchingEngine = new MatchingEngine();
  });
  
  test('should match contracts with optimal agents', async () => {
    const contract = {
      id: 'contract-001',
      requirements: ['content_creation', 'seo'],
      budget: { economic: 5000n },
      complexity: 5
    };
    
    const agents = [
      {
        id: 'agent-001',
        capabilities: ['content_creation'],
        performance: 0.9,
        availability: 1.0
      },
      {
        id: 'agent-002',
        capabilities: ['seo'],
        performance: 0.85,
        availability: 0.8
      },
      {
        id: 'agent-003',
        capabilities: ['content_creation', 'seo'],
        performance: 0.8,
        availability: 1.0
      }
    ];
    
    const match = await matchingEngine.findOptimalMatch(contract, agents);
    
    // Should prefer team synergy over solo agent
    expect(match.type).toBe('team');
    expect(match.agents).toHaveLength(2);
    expect(match.projectedSynergy).toBeGreaterThan(1.5);
    expect(match.confidence).toBeGreaterThan(0.9);
  });
  
  test('should handle complex multi-skill requirements', async () => {
    const complexContract = {
      id: 'complex-001',
      requirements: [
        'content_strategy',
        'content_creation',
        'seo',
        'social_media',
        'analytics',
        'automation'
      ],
      budget: { economic: 25000n, innovation: 500n },
      constraints: {
        maxTeamSize: 5,
        minQuality: 1.5,
        deadline: 30 // days
      }
    };
    
    const agentPool = await generateDiverseAgentPool(50);
    
    const match = await matchingEngine.findOptimalMatch(
      complexContract,
      agentPool
    );
    
    // Verify constraints are met
    expect(match.agents.length).toBeLessThanOrEqual(5);
    expect(match.averageQuality).toBeGreaterThanOrEqual(1.5);
    expect(match.estimatedCompletion).toBeLessThanOrEqual(30);
    
    // Verify all requirements covered
    const coveredSkills = new Set(
      match.agents.flatMap(a => a.capabilities)
    );
    complexContract.requirements.forEach(req => {
      expect(coveredSkills.has(req)).toBe(true);
    });
  });
  
  test('should optimize for different objective functions', async () => {
    const contract = createStandardContract();
    const agents = await createStandardAgentPool();
    
    const objectives = [
      {
        type: 'minimize_cost',
        expectedCost: 3000n,
        expectedQuality: 1.2
      },
      {
        type: 'maximize_quality',
        expectedCost: 4500n,
        expectedQuality: 1.8
      },
      {
        type: 'minimize_time',
        expectedCost: 4000n,
        expectedTime: 3
      },
      {
        type: 'balanced',
        expectedEfficiency: 0.96
      }
    ];
    
    for (const objective of objectives) {
      const match = await matchingEngine.findOptimalMatch(
        contract,
        agents,
        { objective: objective.type }
      );
      
      if (objective.expectedCost) {
        expect(match.totalCost.economic).toBeCloseTo(
          Number(objective.expectedCost),
          -2
        );
      }
      
      if (objective.expectedQuality) {
        expect(match.expectedQuality).toBeCloseTo(
          objective.expectedQuality,
          1
        );
      }
      
      if (objective.expectedEfficiency) {
        expect(match.efficiency).toBeGreaterThanOrEqual(
          objective.expectedEfficiency
        );
      }
    }
  });
});
```

### 5. Auction Mechanism Tests

```typescript
describe('Auction Mechanisms', () => {
  let auctionService: AuctionService;
  
  beforeEach(() => {
    auctionService = new AuctionService();
  });
  
  test('should run sealed-bid auction correctly', async () => {
    const auction = await auctionService.createAuction({
      type: 'sealed_bid',
      contractId: 'contract-001',
      reserve: { economic: 2000n },
      duration: 3600 // 1 hour
    });
    
    // Submit sealed bids
    const bids = [
      { agentId: 'agent-001', sealedBid: 'encrypted-3000' },
      { agentId: 'agent-002', sealedBid: 'encrypted-3500' },
      { agentId: 'agent-003', sealedBid: 'encrypted-2800' }
    ];
    
    for (const bid of bids) {
      await auctionService.submitSealedBid(auction.id, bid);
    }
    
    // Close bidding and reveal
    await auctionService.closeBidding(auction.id);
    
    const reveals = [
      { agentId: 'agent-001', amount: 3000n, nonce: 'nonce-001' },
      { agentId: 'agent-002', amount: 3500n, nonce: 'nonce-002' },
      { agentId: 'agent-003', amount: 2800n, nonce: 'nonce-003' }
    ];
    
    for (const reveal of reveals) {
      await auctionService.revealBid(auction.id, reveal);
    }
    
    const result = await auctionService.finalizeAuction(auction.id);
    
    // Second-price auction (winner pays second highest)
    expect(result.winner).toBe('agent-002');
    expect(result.finalPrice).toBe(3000n);
    expect(result.efficiency).toBeGreaterThan(0.95);
  });
  
  test('should run Dutch auction with price decay', async () => {
    const auction = await auctionService.createAuction({
      type: 'dutch',
      contractId: 'contract-002',
      startPrice: { economic: 5000n },
      reservePrice: { economic: 2000n },
      decayRate: 100n, // per minute
      duration: 30 * 60 // 30 minutes
    });
    
    // Price after 10 minutes
    await advanceTime(10 * 60 * 1000);
    const price10min = await auctionService.getCurrentPrice(auction.id);
    expect(price10min.economic).toBe(4000n);
    
    // Agent bids at current price
    const bid = await auctionService.acceptDutchPrice(
      auction.id,
      'agent-001'
    );
    
    expect(bid.acceptedPrice.economic).toBe(4000n);
    expect(auction.status).toBe('completed');
    
    // No further bids allowed
    await expect(
      auctionService.acceptDutchPrice(auction.id, 'agent-002')
    ).rejects.toThrow('Auction already completed');
  });
  
  test('should handle combinatorial auctions', async () => {
    // Create package of related contracts
    const contractPackage = [
      { id: 'c1', requirements: ['design'], value: 3000n },
      { id: 'c2', requirements: ['development'], value: 5000n },
      { id: 'c3', requirements: ['marketing'], value: 2000n }
    ];
    
    const auction = await auctionService.createCombinatorialAuction({
      contracts: contractPackage,
      allowPartialBids: true
    });
    
    // Agents bid on combinations
    const bids = [
      {
        agentId: 'agency-001',
        packages: [['c1', 'c2', 'c3']], // All contracts
        price: 9000n // Discount for bundle
      },
      {
        agentId: 'specialist-001',
        packages: [['c1']], // Just design
        price: 3200n
      },
      {
        agentId: 'specialist-002',
        packages: [['c2']], // Just development  
        price: 5100n
      },
      {
        agentId: 'team-001',
        packages: [['c1', 'c2']], // Design + Dev
        price: 7500n
      }
    ];
    
    for (const bid of bids) {
      await auctionService.submitCombinatorialBid(auction.id, bid);
    }
    
    // Solve winner determination problem
    const result = await auctionService.solveCombinatorialAuction(auction.id);
    
    // Should maximize total value
    expect(result.totalValue).toBeGreaterThanOrEqual(10000n);
    expect(result.allocations).toHaveLength(2); // Split between specialists
    expect(result.efficiency).toBeGreaterThan(0.95);
  });
});
```

## Test Utilities

### Marketplace Test Helpers

```typescript
// test-helpers/marketplace-test-utils.ts
export class MarketplaceTestUtils {
  static async createTestMarket(config: MarketConfig) {
    const marketplace = new MarketplaceService();
    
    // Create organizations
    const orgs = await Promise.all(
      Array(config.organizations || 10).fill(null).map(() =>
        marketplace.createOrganization({
          name: faker.company.name(),
          wallet: { economic: 100000n }
        })
      )
    );
    
    // Create agents
    const agents = await Promise.all(
      Array(config.agents || 50).fill(null).map(() =>
        marketplace.createAgent({
          name: faker.person.fullName(),
          capabilities: faker.helpers.arrayElements(
            ALL_CAPABILITIES,
            { min: 1, max: 5 }
          ),
          quality: faker.number.float({ min: 1.0, max: 2.0 }),
          reliability: faker.number.float({ min: 0.7, max: 0.95 })
        })
      )
    );
    
    // Create contracts
    const contracts = await Promise.all(
      Array(config.contracts || 100).fill(null).map(() =>
        marketplace.createContract({
          organizationId: faker.helpers.arrayElement(orgs).id,
          title: faker.company.catchPhrase(),
          requirements: faker.helpers.arrayElements(
            ALL_CAPABILITIES,
            { min: 1, max: 4 }
          ),
          budget: {
            economic: BigInt(faker.number.int({ min: 1000, max: 50000 }))
          }
        })
      )
    );
    
    return { marketplace, orgs, agents, contracts };
  }
  
  static async simulateMarketActivity(
    marketplace: MarketplaceService,
    duration: number
  ) {
    const endTime = Date.now() + duration;
    const events = [];
    
    while (Date.now() < endTime) {
      const eventType = faker.helpers.weightedArrayElement([
        { value: 'contract_creation', weight: 30 },
        { value: 'bid_submission', weight: 50 },
        { value: 'contract_completion', weight: 20 }
      ]);
      
      const event = await this.simulateEvent(marketplace, eventType);
      events.push(event);
      
      // Random delay between events
      await sleep(faker.number.int({ min: 100, max: 1000 }));
    }
    
    return events;
  }
  
  static calculateMarketMetrics(contracts: Contract[], bids: Bid[]) {
    return {
      averageBidsPerContract: bids.length / contracts.length,
      fillRate: contracts.filter(c => c.status === 'completed').length / contracts.length,
      averageTimeToFill: this.calculateAverageTimeToFill(contracts),
      priceEfficiency: this.calculatePriceEfficiency(contracts, bids)
    };
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- Statement Coverage: 95%
- Branch Coverage: 95%
- Function Coverage: 95%
- Line Coverage: 95%

### Critical Paths (Must Test)
1. Contract validation and creation
2. Bid submission and validation
3. Winner selection algorithm
4. Efficiency calculations
5. Market bottleneck detection
6. Auction mechanisms
7. Team formation logic

## Running Marketplace Tests

```bash
# Run all marketplace tests
pnpm test:unit:marketplace

# Run with coverage
pnpm test:unit:marketplace --coverage

# Run specific test suite
pnpm test:unit:marketplace bidding-system

# Run in watch mode
pnpm test:unit:marketplace --watch
```

Remember: The marketplace is where value is created. Every test ensures fairness, efficiency, and economic prosperity!
