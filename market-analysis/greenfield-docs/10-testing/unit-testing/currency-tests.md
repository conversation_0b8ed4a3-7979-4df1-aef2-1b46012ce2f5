# Currency Unit Tests

## Overview

Currency operations form the foundation of VibeLaunch's economic system. These tests ensure mathematical precision, atomic operations, and economic law compliance across all five currency dimensions.

## Test Categories

### 1. Basic Currency Operations

#### Economic Currency (₥) Tests

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { CurrencyService, Wallet } from '@vibelaunch/currency-service';
import { Decimal } from 'decimal.js';

describe('Economic Currency (₥)', () => {
  let currencyService: CurrencyService;
  let wallet: Wallet;
  
  beforeEach(async () => {
    currencyService = new CurrencyService();
    wallet = await currencyService.createWallet({
      ownerId: 'test-user-001',
      ownerType: 'user',
      initialBalance: { economic: 1000n }
    });
  });
  
  test('should maintain value conservation law', async () => {
    const sender = wallet;
    const receiver = await currencyService.createWallet({
      ownerId: 'test-user-002',
      ownerType: 'user'
    });
    
    const transferAmount = 500n;
    const totalBefore = sender.balances.economic + receiver.balances.economic;
    
    await currencyService.transfer({
      from: sender.id,
      to: receiver.id,
      currency: 'economic',
      amount: transferAmount
    });
    
    const totalAfter = sender.balances.economic + receiver.balances.economic;
    expect(totalAfter).toBe(totalBefore);
  });
  
  test('should handle decimal precision correctly', async () => {
    // Test sub-unit precision (₥ has 6 decimal places)
    const amount = new Decimal('123.456789');
    const stored = currencyService.toStorage('economic', amount);
    const retrieved = currencyService.fromStorage('economic', stored);
    
    expect(retrieved.toString()).toBe('123.456789');
  });
  
  test('should reject negative values', async () => {
    await expect(
      currencyService.transfer({
        from: wallet.id,
        to: 'invalid',
        currency: 'economic',
        amount: -100n
      })
    ).rejects.toThrow('Negative amounts not allowed');
  });
  
  test('should handle maximum value constraints', async () => {
    const maxEconomic = 2n ** 63n - 1n; // PostgreSQL BIGINT max
    
    await expect(
      currencyService.updateBalance({
        walletId: wallet.id,
        currency: 'economic',
        amount: maxEconomic
      })
    ).resolves.toBeTruthy();
    
    // Should reject overflow
    await expect(
      currencyService.updateBalance({
        walletId: wallet.id,
        currency: 'economic',
        amount: 1n
      })
    ).rejects.toThrow('Value overflow');
  });
});
```

#### Quality Currency (◈) Tests

```typescript
describe('Quality Currency (◈)', () => {
  test('should apply multiplier correctly (0-2x range)', async () => {
    const baseValue = 1000n;
    const qualityMultipliers = [0, 0.5, 1.0, 1.5, 2.0];
    
    for (const multiplier of qualityMultipliers) {
      const result = currencyService.applyQualityMultiplier(baseValue, multiplier);
      expect(result).toBe(BigInt(Number(baseValue) * multiplier));
    }
  });
  
  test('should enforce multiplier boundaries', async () => {
    const wallet = await currencyService.createWallet({
      ownerId: 'agent-001',
      ownerType: 'agent'
    });
    
    // Valid range
    await expect(
      currencyService.setQuality(wallet.id, 1.5)
    ).resolves.toBeTruthy();
    
    // Below minimum
    await expect(
      currencyService.setQuality(wallet.id, -0.1)
    ).rejects.toThrow('Quality must be between 0 and 2');
    
    // Above maximum
    await expect(
      currencyService.setQuality(wallet.id, 2.1)
    ).rejects.toThrow('Quality must be between 0 and 2');
  });
  
  test('should calculate quality-adjusted rewards', async () => {
    const agent = await currencyService.createAgent({
      specialization: 'content_creator',
      quality: 1.8
    });
    
    const baseReward = 1000n;
    const qualityReward = currencyService.calculateQualityReward(
      baseReward,
      agent.wallet.balances.quality
    );
    
    expect(qualityReward).toBe(1800n); // 1000 * 1.8
  });
});
```

#### Temporal Currency (⧗) Tests

```typescript
describe('Temporal Currency (⧗)', () => {
  test('should decay according to formula', async () => {
    const initialAmount = 1000n;
    const wallet = await currencyService.createWallet({
      ownerId: 'test-user',
      ownerType: 'user',
      initialBalance: { temporal: initialAmount }
    });
    
    // Simulate time passing
    const daysPassed = [1, 7, 30, 90, 365];
    
    for (const days of daysPassed) {
      const decayed = currencyService.calculateTemporalDecay(
        initialAmount,
        days
      );
      
      // Verify exponential decay
      const expectedDecay = Number(initialAmount) * Math.exp(-0.01 * days);
      expect(Number(decayed)).toBeCloseTo(expectedDecay, 0);
    }
  });
  
  test('should handle decay refresh on activity', async () => {
    const wallet = await currencyService.createWallet({
      ownerId: 'active-user',
      ownerType: 'user',
      initialBalance: { temporal: 1000n }
    });
    
    // Simulate 30 days of inactivity
    await currencyService.applyTemporalDecay(wallet.id, 30);
    const decayedBalance = wallet.balances.temporal;
    
    // User performs activity
    await currencyService.refreshTemporal(wallet.id, 'contract_completion');
    
    // Temporal should be partially restored
    expect(wallet.balances.temporal).toBeGreaterThan(decayedBalance);
    expect(wallet.balances.temporal).toBeLessThanOrEqual(1000n);
  });
  
  test('should apply time-based bonuses', async () => {
    const contract = await currencyService.createContract({
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      temporalBonus: 200n
    });
    
    // Complete early (3 days)
    const completionTime = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000);
    const bonus = currencyService.calculateTemporalBonus(
      contract,
      completionTime
    );
    
    expect(bonus).toBe(200n); // Full bonus for early completion
    
    // Complete late (10 days)
    const lateCompletion = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000);
    const penalty = currencyService.calculateTemporalBonus(
      contract,
      lateCompletion
    );
    
    expect(penalty).toBe(0n); // No bonus for late completion
  });
});
```

### 2. Multi-Currency Transaction Tests

```typescript
describe('Multi-Currency Transactions', () => {
  test('should maintain atomicity across dimensions', async () => {
    const sender = await currencyService.createWallet({
      ownerId: 'sender',
      ownerType: 'user',
      initialBalance: {
        economic: 1000n,
        quality: 1.5,
        temporal: 500n,
        reliability: 0.9,
        innovation: 100n
      }
    });
    
    const receiver = await currencyService.createWallet({
      ownerId: 'receiver',
      ownerType: 'user'
    });
    
    const transfer = {
      from: sender.id,
      to: receiver.id,
      amounts: {
        economic: 500n,
        temporal: 250n,
        innovation: 50n
      }
    };
    
    // Snapshot before
    const senderBefore = { ...sender.balances };
    const receiverBefore = { ...receiver.balances };
    
    // Execute atomic transfer
    await currencyService.atomicTransfer(transfer);
    
    // Verify all changes applied
    expect(sender.balances.economic).toBe(senderBefore.economic - 500n);
    expect(sender.balances.temporal).toBe(senderBefore.temporal - 250n);
    expect(sender.balances.innovation).toBe(senderBefore.innovation - 50n);
    
    expect(receiver.balances.economic).toBe(receiverBefore.economic + 500n);
    expect(receiver.balances.temporal).toBe(receiverBefore.temporal + 250n);
    expect(receiver.balances.innovation).toBe(receiverBefore.innovation + 50n);
  });
  
  test('should rollback on partial failure', async () => {
    const sender = await currencyService.createWallet({
      ownerId: 'sender',
      ownerType: 'user',
      initialBalance: {
        economic: 1000n,
        temporal: 100n // Not enough for transfer
      }
    });
    
    const receiver = await currencyService.createWallet({
      ownerId: 'receiver',
      ownerType: 'user'
    });
    
    const transfer = {
      from: sender.id,
      to: receiver.id,
      amounts: {
        economic: 500n,
        temporal: 250n // Will fail
      }
    };
    
    // Snapshot before
    const senderBefore = { ...sender.balances };
    const receiverBefore = { ...receiver.balances };
    
    // Attempt transfer
    await expect(
      currencyService.atomicTransfer(transfer)
    ).rejects.toThrow('Insufficient temporal balance');
    
    // Verify no changes (complete rollback)
    expect(sender.balances).toEqual(senderBefore);
    expect(receiver.balances).toEqual(receiverBefore);
  });
  
  test('should handle concurrent transfers safely', async () => {
    const wallet = await currencyService.createWallet({
      ownerId: 'concurrent-test',
      ownerType: 'user',
      initialBalance: { economic: 1000n }
    });
    
    const recipients = await Promise.all(
      Array(10).fill(null).map((_, i) => 
        currencyService.createWallet({
          ownerId: `recipient-${i}`,
          ownerType: 'user'
        })
      )
    );
    
    // Attempt 10 concurrent transfers of 200 each (total 2000)
    const transfers = recipients.map(recipient => 
      currencyService.transfer({
        from: wallet.id,
        to: recipient.id,
        currency: 'economic',
        amount: 200n
      })
    );
    
    const results = await Promise.allSettled(transfers);
    
    // Exactly 5 should succeed (1000 / 200)
    const successful = results.filter(r => r.status === 'fulfilled');
    expect(successful.length).toBe(5);
    
    // Wallet should be empty
    expect(wallet.balances.economic).toBe(0n);
    
    // Total distributed should equal original balance
    const totalDistributed = recipients.reduce(
      (sum, r) => sum + r.balances.economic,
      0n
    );
    expect(totalDistributed).toBe(1000n);
  });
});
```

### 3. Exchange Rate Testing

```typescript
describe('Exchange Rate Calculations', () => {
  let exchangeService: ExchangeService;
  
  beforeEach(() => {
    exchangeService = new ExchangeService();
  });
  
  test('should calculate accurate exchange rates', async () => {
    // Set market conditions
    await exchangeService.updateMarketDepth({
      'ECON/TEMP': {
        bids: [[100, 1000], [99, 2000], [98, 3000]],
        asks: [[101, 1000], [102, 2000], [103, 3000]]
      }
    });
    
    const rate = await exchangeService.getExchangeRate('ECON', 'TEMP');
    expect(rate.bid).toBe(100);
    expect(rate.ask).toBe(101);
    expect(rate.spread).toBe(0.01); // 1%
  });
  
  test('should prevent arbitrage opportunities', async () => {
    // Set up triangular arbitrage test
    await exchangeService.updateRates({
      'ECON/TEMP': 2.0,
      'TEMP/INNO': 3.0,
      'INNO/ECON': 0.15 // Should be 0.167 for no arbitrage
    });
    
    const arbitrage = await exchangeService.detectArbitrage([
      'ECON', 'TEMP', 'INNO', 'ECON'
    ]);
    
    expect(arbitrage.exists).toBe(true);
    expect(arbitrage.profit).toBeCloseTo(0.1, 2); // 10% profit
    
    // Auto-correct rates
    await exchangeService.eliminateArbitrage();
    
    const corrected = await exchangeService.detectArbitrage([
      'ECON', 'TEMP', 'INNO', 'ECON'
    ]);
    
    expect(corrected.exists).toBe(false);
  });
  
  test('should handle market maker spreads', async () => {
    const marketMaker = await exchangeService.createMarketMaker({
      pairs: ['ECON/QUAL', 'QUAL/TEMP'],
      spread: 0.002, // 0.2%
      depth: 100000n
    });
    
    // Small order should get tight spread
    const smallQuote = await marketMaker.quote({
      pair: 'ECON/QUAL',
      amount: 100n
    });
    expect(smallQuote.spread).toBeLessThanOrEqual(0.002);
    
    // Large order should have wider spread
    const largeQuote = await marketMaker.quote({
      pair: 'ECON/QUAL',
      amount: 50000n
    });
    expect(largeQuote.spread).toBeGreaterThan(0.002);
  });
  
  test('should calculate slippage correctly', async () => {
    const orderBook = {
      asks: [
        [100, 1000],
        [101, 2000],
        [102, 3000],
        [105, 5000]
      ]
    };
    
    // Small order - no slippage
    const small = exchangeService.calculateSlippage(orderBook, 500);
    expect(small.averagePrice).toBe(100);
    expect(small.slippage).toBe(0);
    
    // Medium order - some slippage
    const medium = exchangeService.calculateSlippage(orderBook, 2500);
    expect(medium.averagePrice).toBeCloseTo(100.6, 1);
    expect(medium.slippage).toBeCloseTo(0.006, 3);
    
    // Large order - significant slippage
    const large = exchangeService.calculateSlippage(orderBook, 8000);
    expect(large.averagePrice).toBeCloseTo(102.125, 1);
    expect(large.slippage).toBeCloseTo(0.021, 3);
  });
});
```

### 4. Edge Cases and Error Handling

```typescript
describe('Currency Edge Cases', () => {
  test('should handle zero values correctly', async () => {
    const wallet = await currencyService.createWallet({
      ownerId: 'zero-test',
      ownerType: 'user'
    });
    
    // Zero transfer should be rejected
    await expect(
      currencyService.transfer({
        from: wallet.id,
        to: 'any',
        currency: 'economic',
        amount: 0n
      })
    ).rejects.toThrow('Transfer amount must be positive');
    
    // Zero balance should prevent transfers
    expect(wallet.balances.economic).toBe(0n);
    await expect(
      currencyService.transfer({
        from: wallet.id,
        to: 'any',
        currency: 'economic',
        amount: 1n
      })
    ).rejects.toThrow('Insufficient balance');
  });
  
  test('should handle precision limits', async () => {
    // Test maximum precision for each currency
    const precisionTests = [
      { currency: 'economic', decimals: 6, value: '0.000001' },
      { currency: 'quality', decimals: 3, value: '0.001' },
      { currency: 'temporal', decimals: 6, value: '0.000001' },
      { currency: 'reliability', decimals: 3, value: '0.001' },
      { currency: 'innovation', decimals: 6, value: '0.000001' }
    ];
    
    for (const test of precisionTests) {
      const decimal = new Decimal(test.value);
      const stored = currencyService.toStorage(test.currency, decimal);
      const retrieved = currencyService.fromStorage(test.currency, stored);
      
      expect(retrieved.toString()).toBe(test.value);
      
      // Test sub-precision rejection
      const subPrecision = new Decimal(test.value).div(10);
      expect(() => 
        currencyService.toStorage(test.currency, subPrecision)
      ).toThrow('Exceeds precision limit');
    }
  });
  
  test('should handle database constraints', async () => {
    const wallet = await currencyService.createWallet({
      ownerId: 'constraint-test',
      ownerType: 'user'
    });
    
    // Test unique constraint
    await expect(
      currencyService.createWallet({
        ownerId: 'constraint-test',
        ownerType: 'user'
      })
    ).rejects.toThrow('Wallet already exists');
    
    // Test foreign key constraint
    await expect(
      currencyService.transfer({
        from: wallet.id,
        to: 'non-existent-wallet',
        currency: 'economic',
        amount: 100n
      })
    ).rejects.toThrow('Recipient wallet not found');
  });
  
  test('should validate currency types', async () => {
    const wallet = await currencyService.createWallet({
      ownerId: 'type-test',
      ownerType: 'user',
      initialBalance: { economic: 1000n }
    });
    
    // Invalid currency type
    await expect(
      currencyService.transfer({
        from: wallet.id,
        to: 'any',
        currency: 'invalid-currency',
        amount: 100n
      })
    ).rejects.toThrow('Invalid currency type');
    
    // Wrong data type for currency
    await expect(
      currencyService.updateBalance({
        walletId: wallet.id,
        currency: 'quality',
        amount: 100n // Should be float
      })
    ).rejects.toThrow('Invalid data type for quality currency');
  });
});
```

## Test Utilities

### Currency Test Helpers

```typescript
// test-helpers/currency-test-utils.ts
export class CurrencyTestUtils {
  static createTestWallet(overrides?: Partial<Wallet>): Wallet {
    return {
      id: faker.datatype.uuid(),
      ownerId: faker.datatype.uuid(),
      ownerType: 'user',
      balances: {
        economic: 0n,
        quality: 1.0,
        temporal: 0n,
        reliability: 0.5,
        innovation: 0n
      },
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    };
  }
  
  static async assertConservation(
    before: WalletState[],
    after: WalletState[],
    tolerance = 0n
  ): Promise<void> {
    const sumBefore = this.sumAllBalances(before);
    const sumAfter = this.sumAllBalances(after);
    
    for (const currency of ['economic', 'temporal', 'innovation']) {
      const diff = sumAfter[currency] - sumBefore[currency];
      expect(Math.abs(Number(diff))).toBeLessThanOrEqual(Number(tolerance));
    }
  }
  
  static generateRandomTransfer(): Transfer {
    return {
      from: faker.datatype.uuid(),
      to: faker.datatype.uuid(),
      amounts: {
        economic: BigInt(faker.datatype.number({ min: 1, max: 1000 })),
        temporal: BigInt(faker.datatype.number({ min: 0, max: 100 })),
        innovation: BigInt(faker.datatype.number({ min: 0, max: 50 }))
      }
    };
  }
}
```

## Coverage Requirements

### Minimum Coverage Targets
- Statement Coverage: 100%
- Branch Coverage: 100%
- Function Coverage: 100%
- Line Coverage: 100%

### Critical Paths (Must Test)
1. All currency operations
2. Multi-currency atomicity
3. Exchange rate calculations
4. Precision handling
5. Error conditions
6. Boundary values
7. Concurrent operations

## Running Currency Tests

```bash
# Run all currency tests
pnpm test:unit:currency

# Run with coverage
pnpm test:unit:currency --coverage

# Run specific test file
pnpm test:unit:currency currency-operations.test.ts

# Run in watch mode
pnpm test:unit:currency --watch
```

Remember: Currency operations are the foundation of our economic system. A single precision error could cascade into millions in losses!
