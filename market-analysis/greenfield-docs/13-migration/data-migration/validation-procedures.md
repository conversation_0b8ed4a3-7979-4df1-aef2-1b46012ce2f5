# Data Migration Validation Procedures

## Overview

This document outlines comprehensive validation procedures to ensure data integrity, accuracy, and completeness during the VibeLaunch to Genesis migration. Every validation step includes automated scripts, success criteria, and remediation procedures.

## Data Integrity Checks

### Row Count Validations

#### Automated Count Verification Script

```sql
-- validation/row_count_check.sql
CREATE OR REPLACE FUNCTION validate_row_counts()
RETURNS TABLE (
    table_name TEXT,
    legacy_count BIGINT,
    genesis_count BIGINT,
    difference BIGINT,
    percentage_match NUMERIC,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH table_counts AS (
        SELECT 
            'organisations' as tbl,
            (SELECT COUNT(*) FROM public.organisations) as legacy_cnt,
            (SELECT COUNT(*) FROM genesis.organisations) as genesis_cnt
        UNION ALL
        SELECT 
            'profiles',
            (SELECT COUNT(*) FROM public.profiles),
            (SELECT COUNT(*) FROM genesis.profiles)
        UNION ALL
        SELECT 
            'wallets',
            (SELECT COUNT(*) FROM public.wallets),
            (SELECT COUNT(*) FROM genesis.wallets)
        UNION ALL
        SELECT 
            'contracts',
            (SELECT COUNT(*) FROM public.contracts),
            (SELECT COUNT(*) FROM genesis.contracts)
        UNION ALL
        SELECT 
            'bids',
            (SELECT COUNT(*) FROM public.bids),
            (SELECT COUNT(*) FROM genesis.bids)
        UNION ALL
        SELECT 
            'agent_registry',
            (SELECT COUNT(*) FROM public.agent_registry),
            (SELECT COUNT(*) FROM genesis.agent_registry)
        UNION ALL
        SELECT 
            'agent_performance',
            (SELECT COUNT(*) FROM public.agent_performance),
            (SELECT COUNT(*) FROM genesis.agent_performance)
        UNION ALL
        SELECT 
            'tasks',
            (SELECT COUNT(*) FROM public.tasks),
            (SELECT COUNT(*) FROM genesis.tasks)
    )
    SELECT 
        tbl,
        legacy_cnt,
        genesis_cnt,
        legacy_cnt - genesis_cnt,
        ROUND((genesis_cnt::numeric / NULLIF(legacy_cnt, 0)) * 100, 2),
        CASE 
            WHEN legacy_cnt = genesis_cnt THEN 'PASSED'
            WHEN genesis_cnt > legacy_cnt * 0.999 THEN 'WARNING'
            ELSE 'FAILED'
        END
    FROM table_counts
    ORDER BY 
        CASE 
            WHEN legacy_cnt != genesis_cnt THEN 0
            ELSE 1
        END,
        tbl;
END;
$$ LANGUAGE plpgsql;

-- Execute validation
SELECT * FROM validate_row_counts();
```

#### Detailed Record Comparison

```python
# validation/detailed_count_validation.py
import psycopg2
import pandas as pd
from datetime import datetime
import json

class RowCountValidator:
    def __init__(self, legacy_conn_str, genesis_conn_str):
        self.legacy_conn = psycopg2.connect(legacy_conn_str)
        self.genesis_conn = psycopg2.connect(genesis_conn_str)
        self.validation_results = []
        
    def validate_all_tables(self):
        """Comprehensive row count validation with detailed analysis"""
        tables = [
            'organisations', 'profiles', 'wallets', 'contracts',
            'bids', 'agent_registry', 'agent_performance', 'tasks'
        ]
        
        for table in tables:
            result = self.validate_table(table)
            self.validation_results.append(result)
            
        return self.generate_report()
    
    def validate_table(self, table_name):
        """Validate single table with detailed metrics"""
        # Get counts
        legacy_count = self.get_count(self.legacy_conn, f"public.{table_name}")
        genesis_count = self.get_count(self.genesis_conn, f"genesis.{table_name}")
        
        # Get date ranges
        legacy_dates = self.get_date_range(self.legacy_conn, f"public.{table_name}")
        genesis_dates = self.get_date_range(self.genesis_conn, f"genesis.{table_name}")
        
        # Find missing records
        missing_records = self.find_missing_records(table_name)
        
        return {
            'table': table_name,
            'legacy_count': legacy_count,
            'genesis_count': genesis_count,
            'difference': legacy_count - genesis_count,
            'match_percentage': (genesis_count / legacy_count * 100) if legacy_count > 0 else 0,
            'legacy_date_range': legacy_dates,
            'genesis_date_range': genesis_dates,
            'missing_records': missing_records[:10],  # First 10 missing
            'status': 'PASSED' if legacy_count == genesis_count else 'FAILED',
            'validated_at': datetime.now().isoformat()
        }
    
    def find_missing_records(self, table_name):
        """Identify specific missing records"""
        query = f"""
        SELECT id FROM public.{table_name}
        EXCEPT
        SELECT id FROM genesis.{table_name}
        LIMIT 100
        """
        
        with self.legacy_conn.cursor() as cur:
            cur.execute(query)
            return [row[0] for row in cur.fetchall()]
    
    def generate_report(self):
        """Generate comprehensive validation report"""
        df = pd.DataFrame(self.validation_results)
        
        report = {
            'summary': {
                'total_tables': len(df),
                'passed': len(df[df['status'] == 'PASSED']),
                'failed': len(df[df['status'] == 'FAILED']),
                'total_missing_records': df['difference'].sum(),
                'validation_timestamp': datetime.now().isoformat()
            },
            'details': self.validation_results,
            'critical_issues': df[df['difference'] != 0].to_dict('records')
        }
        
        # Save report
        with open(f'validation_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
            json.dump(report, f, indent=2)
            
        return report
```

### Financial Reconciliation

#### Currency Balance Validation

```sql
-- validation/currency_reconciliation.sql
CREATE OR REPLACE FUNCTION validate_currency_conservation()
RETURNS TABLE (
    check_name TEXT,
    legacy_value NUMERIC,
    genesis_value NUMERIC,
    difference NUMERIC,
    status TEXT,
    details JSONB
) AS $$
BEGIN
    -- Total Economic Value Check
    RETURN QUERY
    SELECT 
        'Total Economic Value'::TEXT,
        SUM(w1.balance)::NUMERIC,
        SUM((w2.balances_5d->>'economic')::NUMERIC),
        SUM(w1.balance) - SUM((w2.balances_5d->>'economic')::NUMERIC),
        CASE 
            WHEN ABS(SUM(w1.balance) - SUM((w2.balances_5d->>'economic')::NUMERIC)) < 0.01 
            THEN 'PASSED'
            ELSE 'FAILED'
        END,
        jsonb_build_object(
            'total_wallets', COUNT(*),
            'wallets_checked', COUNT(w2.id),
            'null_balances', COUNT(*) FILTER (WHERE w2.balances_5d IS NULL)
        )
    FROM public.wallets w1
    LEFT JOIN genesis.wallets w2 ON w1.id = w2.id;
    
    -- Contract Budget Conservation
    RETURN QUERY
    SELECT 
        'Contract Budget Conservation'::TEXT,
        SUM(c1.budget)::NUMERIC,
        SUM((c2.budget_5d->>'economic')::NUMERIC),
        SUM(c1.budget) - SUM((c2.budget_5d->>'economic')::NUMERIC),
        CASE 
            WHEN ABS(SUM(c1.budget) - SUM((c2.budget_5d->>'economic')::NUMERIC)) < 0.01 
            THEN 'PASSED'
            ELSE 'FAILED'
        END,
        jsonb_build_object(
            'total_contracts', COUNT(*),
            'contracts_checked', COUNT(c2.id)
        )
    FROM public.contracts c1
    LEFT JOIN genesis.contracts c2 ON c1.id = c2.id;
    
    -- Agent Earnings Conservation
    RETURN QUERY
    SELECT 
        'Agent Earnings Conservation'::TEXT,
        SUM(ap1.total_earnings)::NUMERIC,
        SUM(ap2.economic_earnings)::NUMERIC,
        SUM(ap1.total_earnings) - SUM(ap2.economic_earnings),
        CASE 
            WHEN ABS(SUM(ap1.total_earnings) - SUM(ap2.economic_earnings)) < 0.01 
            THEN 'PASSED'
            ELSE 'FAILED'
        END,
        jsonb_build_object(
            'agents_checked', COUNT(DISTINCT ap1.agent_id),
            'performance_records', COUNT(*)
        )
    FROM public.agent_performance ap1
    LEFT JOIN genesis.agent_performance_5d ap2 ON ap1.id = ap2.id;
END;
$$ LANGUAGE plpgsql;

-- Execute financial reconciliation
SELECT * FROM validate_currency_conservation();
```

#### Transaction-Level Reconciliation

```sql
-- validation/transaction_reconciliation.sql
CREATE OR REPLACE FUNCTION validate_transaction_integrity()
RETURNS TABLE (
    validation_type TEXT,
    records_checked INTEGER,
    discrepancies INTEGER,
    status TEXT,
    sample_issues JSONB
) AS $$
DECLARE
    v_discrepancies JSONB;
BEGIN
    -- Validate wallet transactions
    WITH transaction_check AS (
        SELECT 
            w1.id,
            w1.user_id,
            w1.balance as legacy_balance,
            (w2.balances_5d->>'economic')::NUMERIC as genesis_economic,
            w1.balance - (w2.balances_5d->>'economic')::NUMERIC as difference
        FROM public.wallets w1
        JOIN genesis.wallets w2 ON w1.id = w2.id
        WHERE ABS(w1.balance - (w2.balances_5d->>'economic')::NUMERIC) > 0.01
    )
    SELECT 
        jsonb_agg(
            jsonb_build_object(
                'wallet_id', id,
                'user_id', user_id,
                'legacy_balance', legacy_balance,
                'genesis_economic', genesis_economic,
                'difference', difference
            )
        ) INTO v_discrepancies
    FROM transaction_check
    LIMIT 10;
    
    RETURN QUERY
    SELECT 
        'Wallet Balance Integrity'::TEXT,
        COUNT(*)::INTEGER,
        COUNT(*) FILTER (WHERE ABS(w1.balance - (w2.balances_5d->>'economic')::NUMERIC) > 0.01)::INTEGER,
        CASE 
            WHEN COUNT(*) FILTER (WHERE ABS(w1.balance - (w2.balances_5d->>'economic')::NUMERIC) > 0.01) = 0
            THEN 'PASSED'
            ELSE 'FAILED'
        END,
        v_discrepancies
    FROM public.wallets w1
    JOIN genesis.wallets w2 ON w1.id = w2.id;
    
    -- Validate bid pricing
    WITH bid_check AS (
        SELECT 
            b1.id,
            b1.contract_id,
            b1.price as legacy_price,
            (b2.price_5d->>'economic')::NUMERIC as genesis_price,
            b1.price - (b2.price_5d->>'economic')::NUMERIC as difference
        FROM public.bids b1
        JOIN genesis.bids b2 ON b1.id = b2.id
        WHERE ABS(b1.price - (b2.price_5d->>'economic')::NUMERIC) > 0.01
    )
    SELECT 
        jsonb_agg(
            jsonb_build_object(
                'bid_id', id,
                'contract_id', contract_id,
                'legacy_price', legacy_price,
                'genesis_price', genesis_price,
                'difference', difference
            )
        ) INTO v_discrepancies
    FROM bid_check
    LIMIT 10;
    
    RETURN QUERY
    SELECT 
        'Bid Pricing Integrity'::TEXT,
        COUNT(*)::INTEGER,
        COUNT(*) FILTER (WHERE ABS(b1.price - (b2.price_5d->>'economic')::NUMERIC) > 0.01)::INTEGER,
        CASE 
            WHEN COUNT(*) FILTER (WHERE ABS(b1.price - (b2.price_5d->>'economic')::NUMERIC) > 0.01) = 0
            THEN 'PASSED'
            ELSE 'FAILED'
        END,
        v_discrepancies
    FROM public.bids b1
    JOIN genesis.bids b2 ON b1.id = b2.id;
END;
$$ LANGUAGE plpgsql;
```

### Relationship Integrity

#### Foreign Key Validation

```sql
-- validation/relationship_integrity.sql
CREATE OR REPLACE FUNCTION validate_relationships()
RETURNS TABLE (
    relationship TEXT,
    orphaned_records INTEGER,
    status TEXT,
    sample_orphans JSONB
) AS $$
BEGIN
    -- Check bids -> contracts
    RETURN QUERY
    WITH orphaned_bids AS (
        SELECT b.id, b.contract_id
        FROM genesis.bids b
        LEFT JOIN genesis.contracts c ON b.contract_id = c.id
        WHERE c.id IS NULL
        LIMIT 10
    )
    SELECT 
        'Bids -> Contracts'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(jsonb_build_object('bid_id', id, 'missing_contract_id', contract_id))
    FROM orphaned_bids;
    
    -- Check tasks -> contracts
    RETURN QUERY
    WITH orphaned_tasks AS (
        SELECT t.id, t.contract_id
        FROM genesis.tasks t
        LEFT JOIN genesis.contracts c ON t.contract_id = c.id
        WHERE c.id IS NULL
        LIMIT 10
    )
    SELECT 
        'Tasks -> Contracts'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(jsonb_build_object('task_id', id, 'missing_contract_id', contract_id))
    FROM orphaned_tasks;
    
    -- Check agent_performance -> agent_registry
    RETURN QUERY
    WITH orphaned_performance AS (
        SELECT ap.id, ap.agent_id
        FROM genesis.agent_performance_5d ap
        LEFT JOIN genesis.agent_registry ar ON ap.agent_id = ar.id
        WHERE ar.id IS NULL
        LIMIT 10
    )
    SELECT 
        'Agent Performance -> Agent Registry'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(jsonb_build_object('performance_id', id, 'missing_agent_id', agent_id))
    FROM orphaned_performance;
    
    -- Check wallets -> profiles
    RETURN QUERY
    WITH orphaned_wallets AS (
        SELECT w.id, w.user_id
        FROM genesis.wallets w
        LEFT JOIN genesis.profiles p ON w.user_id = p.id
        WHERE p.id IS NULL
        LIMIT 10
    )
    SELECT 
        'Wallets -> Profiles'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(jsonb_build_object('wallet_id', id, 'missing_user_id', user_id))
    FROM orphaned_wallets;
END;
$$ LANGUAGE plpgsql;
```

#### Cross-Table Consistency

```python
# validation/cross_table_consistency.py
import psycopg2
from typing import Dict, List, Tuple

class CrossTableValidator:
    def __init__(self, genesis_conn_str):
        self.conn = psycopg2.connect(genesis_conn_str)
        
    def validate_user_consistency(self) -> Dict:
        """Ensure user data is consistent across all tables"""
        checks = []
        
        # Check 1: Every profile has a wallet
        check1 = self.execute_check("""
            SELECT COUNT(*) as profiles_without_wallets
            FROM genesis.profiles p
            LEFT JOIN genesis.wallets w ON p.id = w.user_id
            WHERE w.id IS NULL
        """)
        checks.append({
            'check': 'Profiles without wallets',
            'count': check1[0][0],
            'status': 'PASSED' if check1[0][0] == 0 else 'FAILED'
        })
        
        # Check 2: Agent registry entries have performance records
        check2 = self.execute_check("""
            SELECT ar.id, ar.name, COUNT(ap.id) as perf_records
            FROM genesis.agent_registry ar
            LEFT JOIN genesis.agent_performance_5d ap ON ar.id = ap.agent_id
            GROUP BY ar.id, ar.name
            HAVING COUNT(ap.id) = 0
        """)
        checks.append({
            'check': 'Agents without performance records',
            'count': len(check2),
            'status': 'WARNING' if len(check2) > 0 else 'PASSED',
            'details': [{'agent_id': r[0], 'agent_name': r[1]} for r in check2[:5]]
        })
        
        # Check 3: Currency balance consistency
        check3 = self.execute_check("""
            WITH wallet_totals AS (
                SELECT 
                    user_id,
                    (balances_5d->>'economic')::NUMERIC as economic,
                    (balances_5d->>'quality')::NUMERIC as quality,
                    (balances_5d->>'reliability')::NUMERIC as reliability
                FROM genesis.wallets
            ),
            invalid_balances AS (
                SELECT *
                FROM wallet_totals
                WHERE economic < 0 
                   OR quality < 0 
                   OR quality > 2.0
                   OR reliability < 0 
                   OR reliability > 1.0
            )
            SELECT COUNT(*) FROM invalid_balances
        """)
        checks.append({
            'check': 'Invalid currency balances',
            'count': check3[0][0],
            'status': 'PASSED' if check3[0][0] == 0 else 'FAILED'
        })
        
        return {
            'validation': 'Cross-table consistency',
            'total_checks': len(checks),
            'passed': sum(1 for c in checks if c['status'] == 'PASSED'),
            'failed': sum(1 for c in checks if c['status'] == 'FAILED'),
            'warnings': sum(1 for c in checks if c['status'] == 'WARNING'),
            'checks': checks
        }
    
    def execute_check(self, query: str) -> List[Tuple]:
        with self.conn.cursor() as cur:
            cur.execute(query)
            return cur.fetchall()
```

### Currency Conversion Accuracy

#### Multi-Dimensional Currency Validation

```sql
-- validation/currency_dimension_validation.sql
CREATE OR REPLACE FUNCTION validate_currency_dimensions()
RETURNS TABLE (
    dimension TEXT,
    valid_count INTEGER,
    invalid_count INTEGER,
    min_value NUMERIC,
    max_value NUMERIC,
    avg_value NUMERIC,
    status TEXT,
    invalid_samples JSONB
) AS $$
BEGIN
    -- Validate Economic dimension
    RETURN QUERY
    WITH economic_check AS (
        SELECT 
            id,
            user_id,
            (balances_5d->>'economic')::NUMERIC as value
        FROM genesis.wallets
        WHERE (balances_5d->>'economic')::NUMERIC < 0
    )
    SELECT 
        'Economic (₥)'::TEXT,
        COUNT(*) FILTER (WHERE (balances_5d->>'economic')::NUMERIC >= 0)::INTEGER,
        COUNT(*) FILTER (WHERE (balances_5d->>'economic')::NUMERIC < 0)::INTEGER,
        MIN((balances_5d->>'economic')::NUMERIC),
        MAX((balances_5d->>'economic')::NUMERIC),
        AVG((balances_5d->>'economic')::NUMERIC),
        CASE 
            WHEN COUNT(*) FILTER (WHERE (balances_5d->>'economic')::NUMERIC < 0) = 0 
            THEN 'PASSED' 
            ELSE 'FAILED' 
        END,
        (SELECT jsonb_agg(jsonb_build_object('wallet_id', id, 'value', value)) 
         FROM economic_check LIMIT 5)
    FROM genesis.wallets;
    
    -- Validate Quality dimension
    RETURN QUERY
    WITH quality_check AS (
        SELECT 
            id,
            user_id,
            (balances_5d->>'quality')::NUMERIC as value
        FROM genesis.wallets
        WHERE (balances_5d->>'quality')::NUMERIC < 0.5 
           OR (balances_5d->>'quality')::NUMERIC > 2.0
    )
    SELECT 
        'Quality (◈)'::TEXT,
        COUNT(*) FILTER (WHERE (balances_5d->>'quality')::NUMERIC BETWEEN 0.5 AND 2.0)::INTEGER,
        COUNT(*) FILTER (WHERE (balances_5d->>'quality')::NUMERIC NOT BETWEEN 0.5 AND 2.0)::INTEGER,
        MIN((balances_5d->>'quality')::NUMERIC),
        MAX((balances_5d->>'quality')::NUMERIC),
        AVG((balances_5d->>'quality')::NUMERIC),
        CASE 
            WHEN COUNT(*) FILTER (WHERE (balances_5d->>'quality')::NUMERIC NOT BETWEEN 0.5 AND 2.0) = 0 
            THEN 'PASSED' 
            ELSE 'FAILED' 
        END,
        (SELECT jsonb_agg(jsonb_build_object('wallet_id', id, 'value', value)) 
         FROM quality_check LIMIT 5)
    FROM genesis.wallets;
    
    -- Validate Reliability dimension
    RETURN QUERY
    WITH reliability_check AS (
        SELECT 
            id,
            user_id,
            (balances_5d->>'reliability')::NUMERIC as value
        FROM genesis.wallets
        WHERE (balances_5d->>'reliability')::NUMERIC < 0 
           OR (balances_5d->>'reliability')::NUMERIC > 1.0
    )
    SELECT 
        'Reliability (☆)'::TEXT,
        COUNT(*) FILTER (WHERE (balances_5d->>'reliability')::NUMERIC BETWEEN 0 AND 1.0)::INTEGER,
        COUNT(*) FILTER (WHERE (balances_5d->>'reliability')::NUMERIC NOT BETWEEN 0 AND 1.0)::INTEGER,
        MIN((balances_5d->>'reliability')::NUMERIC),
        MAX((balances_5d->>'reliability')::NUMERIC),
        AVG((balances_5d->>'reliability')::NUMERIC),
        CASE 
            WHEN COUNT(*) FILTER (WHERE (balances_5d->>'reliability')::NUMERIC NOT BETWEEN 0 AND 1.0) = 0 
            THEN 'PASSED' 
            ELSE 'FAILED' 
        END,
        (SELECT jsonb_agg(jsonb_build_object('wallet_id', id, 'value', value)) 
         FROM reliability_check LIMIT 5)
    FROM genesis.wallets;
    
    -- Validate Temporal dimension
    RETURN QUERY
    SELECT 
        'Temporal (⧗)'::TEXT,
        COUNT(*)::INTEGER,
        0::INTEGER, -- All should be 0 initially
        MIN((balances_5d->>'temporal')::NUMERIC),
        MAX((balances_5d->>'temporal')::NUMERIC),
        AVG((balances_5d->>'temporal')::NUMERIC),
        CASE 
            WHEN MAX((balances_5d->>'temporal')::NUMERIC) = 0 
            THEN 'PASSED' 
            ELSE 'WARNING' 
        END,
        NULL::JSONB
    FROM genesis.wallets;
    
    -- Validate Innovation dimension
    RETURN QUERY
    SELECT 
        'Innovation (◊)'::TEXT,
        COUNT(*)::INTEGER,
        0::INTEGER, -- All should be 0 initially
        MIN((balances_5d->>'innovation')::NUMERIC),
        MAX((balances_5d->>'innovation')::NUMERIC),
        AVG((balances_5d->>'innovation')::NUMERIC),
        CASE 
            WHEN MAX((balances_5d->>'innovation')::NUMERIC) = 0 
            THEN 'PASSED' 
            ELSE 'WARNING' 
        END,
        NULL::JSONB
    FROM genesis.wallets;
END;
$$ LANGUAGE plpgsql;
```

## Business Logic Validation

### Contract State Consistency

```sql
-- validation/contract_state_validation.sql
CREATE OR REPLACE FUNCTION validate_contract_states()
RETURNS TABLE (
    validation TEXT,
    issue_count INTEGER,
    status TEXT,
    details JSONB
) AS $$
BEGIN
    -- Check contract status transitions
    RETURN QUERY
    WITH status_check AS (
        SELECT 
            c1.id,
            c1.status as legacy_status,
            c2.status as genesis_status,
            c1.created_at,
            c1.updated_at
        FROM public.contracts c1
        JOIN genesis.contracts c2 ON c1.id = c2.id
        WHERE c1.status != c2.status
    )
    SELECT 
        'Contract Status Consistency'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(
            jsonb_build_object(
                'contract_id', id,
                'legacy_status', legacy_status,
                'genesis_status', genesis_status
            )
        )
    FROM status_check;
    
    -- Validate synergy potential calculations
    RETURN QUERY
    WITH synergy_check AS (
        SELECT 
            c.id,
            c.complexity,
            c.synergy_potential,
            CASE 
                WHEN c.complexity = 'high' THEN 1.944
                WHEN c.complexity = 'medium' THEN 1.5
                ELSE 1.2
            END as expected_synergy
        FROM genesis.contracts c
        WHERE ABS(c.synergy_potential - 
            CASE 
                WHEN c.complexity = 'high' THEN 1.944
                WHEN c.complexity = 'medium' THEN 1.5
                ELSE 1.2
            END) > 0.001
    )
    SELECT 
        'Synergy Potential Calculations'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(
            jsonb_build_object(
                'contract_id', id,
                'complexity', complexity,
                'actual_synergy', synergy_potential,
                'expected_synergy', expected_synergy
            )
        )
    FROM synergy_check;
    
    -- Validate team eligibility logic
    RETURN QUERY
    WITH team_check AS (
        SELECT 
            c.id,
            c.budget_5d->>'economic' as budget,
            c.optimal_team_size,
            c.team_eligible,
            CASE 
                WHEN (c.budget_5d->>'economic')::NUMERIC > 10000 THEN 5
                WHEN (c.budget_5d->>'economic')::NUMERIC > 5000 THEN 3
                ELSE 1
            END as expected_team_size
        FROM genesis.contracts c
        WHERE c.optimal_team_size != 
            CASE 
                WHEN (c.budget_5d->>'economic')::NUMERIC > 10000 THEN 5
                WHEN (c.budget_5d->>'economic')::NUMERIC > 5000 THEN 3
                ELSE 1
            END
    )
    SELECT 
        'Team Size Calculations'::TEXT,
        COUNT(*)::INTEGER,
        CASE WHEN COUNT(*) = 0 THEN 'PASSED' ELSE 'FAILED' END,
        jsonb_agg(
            jsonb_build_object(
                'contract_id', id,
                'budget', budget,
                'actual_team_size', optimal_team_size,
                'expected_team_size', expected_team_size
            )
        )
    FROM team_check;
END;
$$ LANGUAGE plpgsql;
```

### Agent Performance History

```python
# validation/agent_performance_validation.py
import numpy as np
from decimal import Decimal

class AgentPerformanceValidator:
    def __init__(self, legacy_conn, genesis_conn):
        self.legacy_conn = legacy_conn
        self.genesis_conn = genesis_conn
        
    def validate_performance_transformation(self):
        """Validate agent performance metric transformations"""
        
        # Get sample of agents to validate
        agents = self.get_agent_sample()
        validation_results = []
        
        for agent_id in agents:
            legacy_data = self.get_legacy_performance(agent_id)
            genesis_data = self.get_genesis_performance(agent_id)
            
            # Validate economic earnings match
            economic_valid = self.validate_economic(legacy_data, genesis_data)
            
            # Validate quality score calculation
            quality_valid = self.validate_quality_score(legacy_data, genesis_data)
            
            # Validate reliability calculation
            reliability_valid = self.validate_reliability(legacy_data, genesis_data)
            
            validation_results.append({
                'agent_id': agent_id,
                'economic_valid': economic_valid,
                'quality_valid': quality_valid,
                'reliability_valid': reliability_valid,
                'overall_status': 'PASSED' if all([economic_valid, quality_valid, reliability_valid]) else 'FAILED'
            })
            
        return self.generate_performance_report(validation_results)
    
    def validate_quality_score(self, legacy_data, genesis_data):
        """Validate quality score transformation logic"""
        if not legacy_data:
            return True
            
        avg_success_rate = np.mean([d['success_rate'] for d in legacy_data])
        
        expected_quality = 1.5 if avg_success_rate > 0.9 else \
                          1.3 if avg_success_rate > 0.8 else \
                          1.1 if avg_success_rate > 0.7 else 1.0
                          
        actual_quality = float(genesis_data.get('quality_multiplier', 0))
        
        return abs(expected_quality - actual_quality) < 0.01
```

### Transaction Completeness

```sql
-- validation/transaction_completeness.sql
CREATE OR REPLACE FUNCTION validate_transaction_completeness()
RETURNS TABLE (
    check_name TEXT,
    total_transactions INTEGER,
    complete_transactions INTEGER,
    incomplete_transactions INTEGER,
    completion_rate NUMERIC,
    status TEXT
) AS $$
BEGIN
    -- Check bid-to-task completion
    RETURN QUERY
    WITH bid_task_check AS (
        SELECT 
            b.id as bid_id,
            b.contract_id,
            b.status as bid_status,
            t.id as task_id,
            t.status as task_status
        FROM genesis.bids b
        LEFT JOIN genesis.tasks t ON b.contract_id = t.contract_id AND b.agent_id = t.assigned_agent_id
        WHERE b.status = 'accepted'
    )
    SELECT 
        'Accepted Bids with Tasks'::TEXT,
        COUNT(*)::INTEGER,
        COUNT(task_id)::INTEGER,
        COUNT(*) - COUNT(task_id)::INTEGER,
        ROUND((COUNT(task_id)::NUMERIC / NULLIF(COUNT(*), 0)) * 100, 2),
        CASE 
            WHEN COUNT(*) = COUNT(task_id) THEN 'PASSED'
            WHEN COUNT(task_id)::NUMERIC / COUNT(*) > 0.95 THEN 'WARNING'
            ELSE 'FAILED'
        END
    FROM bid_task_check;
    
    -- Check task completion status
    RETURN QUERY
    SELECT 
        'Task Completion Status'::TEXT,
        COUNT(*)::INTEGER,
        COUNT(*) FILTER (WHERE status = 'completed')::INTEGER,
        COUNT(*) FILTER (WHERE status != 'completed')::INTEGER,
        ROUND((COUNT(*) FILTER (WHERE status = 'completed')::NUMERIC / NULLIF(COUNT(*), 0)) * 100, 2),
        'INFO'::TEXT
    FROM genesis.tasks
    WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;
```

## Performance Validation

### Query Performance Comparison

```python
# validation/performance_validation.py
import time
import psycopg2
import statistics

class PerformanceValidator:
    def __init__(self, legacy_conn_str, genesis_conn_str):
        self.legacy_conn = psycopg2.connect(legacy_conn_str)
        self.genesis_conn = psycopg2.connect(genesis_conn_str)
        self.test_queries = [
            {
                'name': 'Wallet lookup by user',
                'legacy': 'SELECT * FROM public.wallets WHERE user_id = %s',
                'genesis': 'SELECT * FROM genesis.wallets WHERE user_id = %s',
                'params': ['00000000-0000-0000-0000-000000000001']
            },
            {
                'name': 'Contract aggregation',
                'legacy': '''
                    SELECT organisation_id, COUNT(*), SUM(budget)
                    FROM public.contracts
                    GROUP BY organisation_id
                ''',
                'genesis': '''
                    SELECT organisation_id, COUNT(*), 
                           SUM((budget_5d->>'economic')::NUMERIC)
                    FROM genesis.contracts
                    GROUP BY organisation_id
                '''
            },
            {
                'name': 'Agent performance ranking',
                'legacy': '''
                    SELECT agent_id, SUM(total_earnings), AVG(success_rate)
                    FROM public.agent_performance
                    GROUP BY agent_id
                    ORDER BY SUM(total_earnings) DESC
                    LIMIT 100
                ''',
                'genesis': '''
                    SELECT agent_id, SUM(economic_earnings), AVG(quality_multiplier)
                    FROM genesis.agent_performance_5d
                    GROUP BY agent_id
                    ORDER BY SUM(economic_earnings) DESC
                    LIMIT 100
                '''
            }
        ]
    
    def run_performance_tests(self, iterations=10):
        results = []
        
        for query in self.test_queries:
            legacy_times = []
            genesis_times = []
            
            for _ in range(iterations):
                # Test legacy query
                start = time.time()
                with self.legacy_conn.cursor() as cur:
                    if 'params' in query:
                        cur.execute(query['legacy'], query['params'])
                    else:
                        cur.execute(query['legacy'])
                    cur.fetchall()
                legacy_times.append(time.time() - start)
                
                # Test genesis query
                start = time.time()
                with self.genesis_conn.cursor() as cur:
                    if 'params' in query:
                        cur.execute(query['genesis'], query['params'])
                    else:
                        cur.execute(query['genesis'])
                    cur.fetchall()
                genesis_times.append(time.time() - start)
            
            results.append({
                'query': query['name'],
                'legacy_avg_ms': statistics.mean(legacy_times) * 1000,
                'genesis_avg_ms': statistics.mean(genesis_times) * 1000,
                'legacy_p95_ms': sorted(legacy_times)[int(len(legacy_times) * 0.95)] * 1000,
                'genesis_p95_ms': sorted(genesis_times)[int(len(genesis_times) * 0.95)] * 1000,
                'performance_ratio': statistics.mean(genesis_times) / statistics.mean(legacy_times),
                'status': 'PASSED' if statistics.mean(genesis_times) <= statistics.mean(legacy_times) * 1.2 else 'DEGRADED'
            })
        
        return self.generate_performance_report(results)
```

### Load Testing

```bash
#!/bin/bash
# validation/load_test_validation.sh

# Run pgbench load test on both systems
echo "Running load test on legacy system..."
pgbench -h legacy-host -p 5432 -U postgres -d vibelaunch \
    -c 10 -j 2 -T 60 -f legacy_workload.sql > legacy_load_results.txt

echo "Running load test on Genesis system..."
pgbench -h genesis-host -p 5432 -U postgres -d vibelaunch_genesis \
    -c 10 -j 2 -T 60 -f genesis_workload.sql > genesis_load_results.txt

# Compare results
echo "Load Test Comparison:"
echo "Legacy TPS: $(grep 'tps' legacy_load_results.txt | tail -1)"
echo "Genesis TPS: $(grep 'tps' genesis_load_results.txt | tail -1)"
```

## Validation Scripts

### Master Validation Script

```python
# validation/run_all_validations.py
#!/usr/bin/env python3

import json
import sys
from datetime import datetime
import argparse

def run_all_validations(legacy_conn_str, genesis_conn_str):
    """Execute all validation procedures"""
    
    print("Starting comprehensive data migration validation...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("-" * 80)
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'validations': []
    }
    
    # 1. Row Count Validation
    print("\n1. Running row count validation...")
    from row_count_validator import RowCountValidator
    rcv = RowCountValidator(legacy_conn_str, genesis_conn_str)
    row_count_result = rcv.validate_all_tables()
    results['validations'].append(row_count_result)
    print(f"   Status: {row_count_result['summary']['status']}")
    
    # 2. Financial Reconciliation
    print("\n2. Running financial reconciliation...")
    # Execute SQL validations
    
    # 3. Relationship Integrity
    print("\n3. Validating relationship integrity...")
    from cross_table_validator import CrossTableValidator
    ctv = CrossTableValidator(genesis_conn_str)
    relationship_result = ctv.validate_user_consistency()
    results['validations'].append(relationship_result)
    
    # 4. Performance Validation
    print("\n4. Running performance validation...")
    from performance_validator import PerformanceValidator
    pv = PerformanceValidator(legacy_conn_str, genesis_conn_str)
    perf_result = pv.run_performance_tests()
    results['validations'].append(perf_result)
    
    # Generate summary
    all_passed = all(
        v.get('summary', {}).get('status') == 'PASSED' 
        for v in results['validations']
    )
    
    results['overall_status'] = 'PASSED' if all_passed else 'FAILED'
    
    # Save results
    with open(f'validation_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("\n" + "=" * 80)
    print(f"OVERALL VALIDATION STATUS: {results['overall_status']}")
    print("=" * 80)
    
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run migration validation')
    parser.add_argument('--legacy-db', required=True, help='Legacy database connection string')
    parser.add_argument('--genesis-db', required=True, help='Genesis database connection string')
    
    args = parser.parse_args()
    
    results = run_all_validations(args.legacy_db, args.genesis_db)
    
    sys.exit(0 if results['overall_status'] == 'PASSED' else 1)
```

### Continuous Validation Monitor

```python
# validation/continuous_monitor.py
import time
import psycopg2
from datetime import datetime
import smtplib
from email.mime.text import MIMEText

class ContinuousValidator:
    def __init__(self, config):
        self.config = config
        self.legacy_conn = psycopg2.connect(config['legacy_db'])
        self.genesis_conn = psycopg2.connect(config['genesis_db'])
        
    def monitor_sync_lag(self):
        """Monitor replication lag between systems"""
        query = """
        SELECT 
            EXTRACT(EPOCH FROM (
                MAX(public.wallets.updated_at) - MAX(genesis.wallets.updated_at)
            )) as lag_seconds
        FROM public.wallets, genesis.wallets
        """
        
        with self.legacy_conn.cursor() as cur:
            cur.execute(query)
            lag = cur.fetchone()[0] or 0
            
        return {
            'metric': 'sync_lag',
            'value': lag,
            'threshold': 5.0,
            'status': 'OK' if lag < 5.0 else 'CRITICAL',
            'timestamp': datetime.now().isoformat()
        }
    
    def monitor_value_conservation(self):
        """Continuous value conservation check"""
        query = """
        SELECT 
            ABS(
                (SELECT SUM(balance) FROM public.wallets) -
                (SELECT SUM((balances_5d->>'economic')::NUMERIC) FROM genesis.wallets)
            ) as difference
        """
        
        with self.genesis_conn.cursor() as cur:
            cur.execute(query)
            diff = cur.fetchone()[0] or 0
            
        return {
            'metric': 'value_conservation',
            'value': float(diff),
            'threshold': 1.0,
            'status': 'OK' if diff < 1.0 else 'CRITICAL',
            'timestamp': datetime.now().isoformat()
        }
    
    def alert_if_needed(self, check_result):
        """Send alert if validation fails"""
        if check_result['status'] != 'OK':
            msg = MIMEText(f"""
            Migration Validation Alert
            
            Metric: {check_result['metric']}
            Status: {check_result['status']}
            Value: {check_result['value']}
            Threshold: {check_result['threshold']}
            Time: {check_result['timestamp']}
            
            Please investigate immediately.
            """)
            
            msg['Subject'] = f"CRITICAL: Migration validation failure - {check_result['metric']}"
            msg['From'] = self.config['alert_from']
            msg['To'] = self.config['alert_to']
            
            # Send email alert
            # Implementation depends on SMTP configuration
    
    def run_continuous_monitoring(self, interval=60):
        """Run monitoring loop"""
        print(f"Starting continuous validation monitoring (interval: {interval}s)")
        
        while True:
            try:
                # Run all checks
                checks = [
                    self.monitor_sync_lag(),
                    self.monitor_value_conservation()
                ]
                
                # Process results
                for check in checks:
                    print(f"{check['timestamp']} - {check['metric']}: {check['status']} (value: {check['value']})")
                    self.alert_if_needed(check)
                
                # Sleep until next check
                time.sleep(interval)
                
            except KeyboardInterrupt:
                print("\nMonitoring stopped by user")
                break
            except Exception as e:
                print(f"Error during monitoring: {e}")
                time.sleep(interval)
```

## Validation Success Criteria

### Critical Validations (Must Pass)
1. **Row Counts**: 100% match (±0.01% tolerance for active systems)
2. **Value Conservation**: Total economic value preserved to 0.01 precision
3. **Relationship Integrity**: Zero orphaned records
4. **Data Types**: All currency values within valid ranges

### Important Validations (Should Pass)
1. **Performance**: No more than 20% degradation
2. **Business Logic**: Contract states and calculations correct
3. **Currency Dimensions**: Valid ranges for all dimensions

### Monitoring Validations (Continuous)
1. **Sync Lag**: < 5 seconds
2. **Error Rate**: < 0.1% of transactions
3. **Value Drift**: < $1.00 cumulative

## Validation Reporting

### Dashboard Queries

```sql
-- validation/dashboard_view.sql
CREATE VIEW migration_validation_dashboard AS
WITH validation_summary AS (
    SELECT 
        'Row Counts' as category,
        (SELECT COUNT(*) FROM validate_row_counts() WHERE status = 'PASSED') as passed,
        (SELECT COUNT(*) FROM validate_row_counts() WHERE status = 'FAILED') as failed,
        (SELECT COUNT(*) FROM validate_row_counts() WHERE status = 'WARNING') as warnings
    UNION ALL
    SELECT 
        'Currency Conservation',
        (SELECT COUNT(*) FROM validate_currency_conservation() WHERE status = 'PASSED'),
        (SELECT COUNT(*) FROM validate_currency_conservation() WHERE status = 'FAILED'),
        0
    UNION ALL
    SELECT 
        'Relationships',
        (SELECT COUNT(*) FROM validate_relationships() WHERE status = 'PASSED'),
        (SELECT COUNT(*) FROM validate_relationships() WHERE status = 'FAILED'),
        0
)
SELECT 
    category,
    passed,
    failed,
    warnings,
    CASE 
        WHEN failed = 0 AND warnings = 0 THEN 'PASSED'
        WHEN failed = 0 THEN 'WARNING'
        ELSE 'FAILED'
    END as overall_status,
    NOW() as last_validated
FROM validation_summary;
```

## Conclusion

These comprehensive validation procedures ensure the integrity, accuracy, and completeness of the VibeLaunch to Genesis migration. Regular execution of these validations throughout the migration process provides confidence in data quality and enables rapid identification and resolution of any issues that arise.