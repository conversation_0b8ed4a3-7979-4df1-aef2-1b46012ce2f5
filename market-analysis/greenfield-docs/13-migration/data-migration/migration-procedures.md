# Data Migration Procedures

## Overview

This document provides detailed procedures for migrating data from the current VibeLaunch single-currency system to the Genesis five-dimensional currency system. The migration ensures zero data loss, maintains full auditability, and enables bi-directional synchronization during the transition period.

## Current State Analysis

### Database Inventory

Based on analysis of `supabase/migrations/` and current schema:

#### Core Tables to Migrate
1. **organisations** (125 records estimated)
   - No transformation needed
   - Add genesis_enabled flag
   
2. **profiles** (2,500 users estimated)
   - Add multi-currency wallet initialization
   - Calculate initial reputation scores
   
3. **wallets** (2,500 records)
   - Transform single balance to 5D currency JSONB
   - Initialize temporal decay parameters
   
4. **contracts** (15,000 records)
   - Convert budget to multi-currency format
   - Add synergy_potential calculations
   
5. **bids** (45,000 records)
   - Transform price to 5D pricing
   - Add team_composition data
   
6. **agent_registry** (50 agents)
   - Extend with team collaboration metrics
   - Add synergy compatibility matrix
   
7. **agent_performance** (150,000 metrics)
   - Convert to multi-dimensional performance
   - Calculate quality and reliability scores
   
8. **tasks** (8,000 records)
   - Add efficiency tracking
   - Link to value creation metrics

#### System Tables
- chat_log (500,000 messages)
- bus_events (2M events)
- webhook_queue (100K processed)
- thoughts (250K reasoning traces)

### Data Volume Estimates
- **Total Records**: ~3M across all tables
- **Storage Size**: ~50GB including indexes
- **Daily Growth**: ~10K new records
- **Peak Transaction Rate**: 100 TPS

## Migration Approach

### Single Currency → Five-Dimensional Currency Mapping

#### Currency Transformation Algorithm

```sql
-- Step 1: Create new currency columns
ALTER TABLE wallets ADD COLUMN balances_5d JSONB;
ALTER TABLE wallets ADD COLUMN temporal_last_update TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE wallets ADD COLUMN temporal_decay_rate DECIMAL(5,4) DEFAULT 0.1;

-- Step 2: Initialize 5D balances from single currency
UPDATE wallets SET balances_5d = jsonb_build_object(
    'economic', balance::text,
    'quality', CASE 
        WHEN EXISTS (
            SELECT 1 FROM agent_performance ap 
            WHERE ap.agent_id = wallets.user_id 
            AND ap.success_rate > 0.8
        ) THEN '1.2'
        ELSE '1.0'
    END,
    'temporal', '0',
    'reliability', CASE
        WHEN created_at < NOW() - INTERVAL '6 months' THEN '0.8'
        WHEN created_at < NOW() - INTERVAL '3 months' THEN '0.6'
        ELSE '0.5'
    END,
    'innovation', '0'
);

-- Step 3: Create value conservation tracking
CREATE TABLE currency_migrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID REFERENCES wallets(id),
    old_balance DECIMAL(20,8),
    new_balances JSONB,
    migration_timestamp TIMESTAMPTZ DEFAULT NOW(),
    verification_hash TEXT,
    CONSTRAINT value_conservation CHECK (
        (new_balances->>'economic')::decimal = old_balance
    )
);
```

#### Agent Data Transformation

```sql
-- Transform agent performance to multi-dimensional metrics
CREATE TABLE agent_performance_5d AS
SELECT 
    ap.id,
    ap.agent_id,
    ap.created_at,
    -- Economic performance (existing)
    ap.total_earnings as economic_earnings,
    -- Quality score (derived from success rate)
    CASE 
        WHEN ap.success_rate > 0.9 THEN 1.5
        WHEN ap.success_rate > 0.8 THEN 1.3
        WHEN ap.success_rate > 0.7 THEN 1.1
        ELSE 1.0
    END as quality_multiplier,
    -- Temporal value (based on recency)
    EXTRACT(EPOCH FROM (NOW() - ap.last_task_completed)) / 86400 as days_since_last_task,
    -- Reliability (based on consistency)
    STDDEV(task_completion_times) as reliability_variance,
    -- Innovation (based on unique approaches)
    COUNT(DISTINCT task_types_completed) as innovation_score
FROM agent_performance ap
GROUP BY ap.id, ap.agent_id, ap.created_at, ap.total_earnings, ap.success_rate, ap.last_task_completed;
```

#### Contract History Preservation

```sql
-- Preserve contract history with multi-currency enhancement
CREATE TABLE contracts_genesis AS
SELECT 
    c.*,
    jsonb_build_object(
        'economic', c.budget,
        'quality', 1.0,
        'temporal', 0,
        'reliability', 0.5,
        'innovation', 0
    ) as budget_5d,
    -- Calculate potential team synergy
    CASE 
        WHEN c.complexity = 'high' THEN 1.944  -- 194.4% synergy potential
        WHEN c.complexity = 'medium' THEN 1.5
        ELSE 1.2
    END as synergy_potential
FROM contracts c;

-- Add team formation possibility
ALTER TABLE contracts_genesis 
ADD COLUMN team_eligible BOOLEAN DEFAULT true,
ADD COLUMN optimal_team_size INTEGER DEFAULT 3;
```

## ETL Procedures

### Extract Scripts from PostgreSQL

```bash
#!/bin/bash
# extract_vibelaunch_data.sh

# Configuration
SOURCE_DB="********************************/vibelaunch"
EXPORT_DIR="/data/migration/extract"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create export directory
mkdir -p $EXPORT_DIR/$TIMESTAMP

# Export schema
pg_dump $SOURCE_DB --schema-only > $EXPORT_DIR/$TIMESTAMP/schema.sql

# Export data with COPY for performance
tables=(
    "organisations"
    "profiles" 
    "wallets"
    "contracts"
    "bids"
    "agent_registry"
    "agent_performance"
    "tasks"
)

for table in "${tables[@]}"; do
    echo "Exporting $table..."
    psql $SOURCE_DB -c "\COPY (SELECT * FROM $table) TO '$EXPORT_DIR/$TIMESTAMP/$table.csv' WITH CSV HEADER"
done

# Create checksums for verification
cd $EXPORT_DIR/$TIMESTAMP
sha256sum *.csv > checksums.txt

echo "Export completed to $EXPORT_DIR/$TIMESTAMP"
```

### Transform to New Schema

```python
# transform_to_genesis.py
import pandas as pd
import json
from datetime import datetime
from decimal import Decimal

class GenesisTransformer:
    def __init__(self, source_dir, target_dir):
        self.source_dir = source_dir
        self.target_dir = target_dir
        
    def transform_wallets(self):
        """Transform single currency wallets to 5D currency system"""
        df = pd.read_csv(f"{self.source_dir}/wallets.csv")
        
        # Calculate performance history for initial scores
        perf_df = pd.read_csv(f"{self.source_dir}/agent_performance.csv")
        
        def calculate_5d_balance(row):
            # Get performance metrics
            agent_perf = perf_df[perf_df['agent_id'] == row['user_id']]
            
            # Economic currency (direct transfer)
            economic = str(row['balance'])
            
            # Quality multiplier based on historical performance
            if not agent_perf.empty:
                avg_success = agent_perf['success_rate'].mean()
                quality = "1.5" if avg_success > 0.9 else "1.2" if avg_success > 0.7 else "1.0"
            else:
                quality = "1.0"
                
            # Temporal (initially 0)
            temporal = "0"
            
            # Reliability based on account age and consistency
            account_age_days = (datetime.now() - pd.to_datetime(row['created_at'])).days
            reliability = "0.8" if account_age_days > 180 else "0.6" if account_age_days > 90 else "0.5"
            
            # Innovation (initially 0, will grow with unique contributions)
            innovation = "0"
            
            return json.dumps({
                "economic": economic,
                "quality": quality,
                "temporal": temporal,
                "reliability": reliability,
                "innovation": innovation
            })
        
        df['balances_5d'] = df.apply(calculate_5d_balance, axis=1)
        df['temporal_last_update'] = datetime.now().isoformat()
        df['temporal_decay_rate'] = 0.1
        
        # Save transformed data
        df.to_csv(f"{self.target_dir}/wallets_genesis.csv", index=False)
        
        # Create audit log
        audit = {
            'table': 'wallets',
            'records_processed': len(df),
            'transformation_time': datetime.now().isoformat(),
            'value_conservation': self.verify_value_conservation(df)
        }
        
        return audit
    
    def verify_value_conservation(self, df):
        """Verify total economic value is preserved"""
        original_total = df['balance'].sum()
        
        transformed_total = sum(
            Decimal(json.loads(row)['economic']) 
            for row in df['balances_5d']
        )
        
        return {
            'original_total': str(original_total),
            'transformed_total': str(transformed_total),
            'difference': str(abs(original_total - transformed_total)),
            'conservation_verified': abs(original_total - transformed_total) < Decimal('0.01')
        }

    def transform_contracts(self):
        """Transform contracts for multi-agent collaboration"""
        df = pd.read_csv(f"{self.source_dir}/contracts.csv")
        
        def calculate_team_potential(row):
            # Complex contracts benefit more from teams
            complexity_map = {
                'simple': 1.2,
                'medium': 1.5,
                'complex': 1.944  # Target 194.4% synergy
            }
            
            return complexity_map.get(row.get('complexity', 'medium'), 1.5)
        
        def calculate_optimal_team_size(row):
            # Based on contract value and complexity
            if row['budget'] > 10000:
                return 5
            elif row['budget'] > 5000:
                return 3
            else:
                return 1
                
        df['synergy_potential'] = df.apply(calculate_team_potential, axis=1)
        df['optimal_team_size'] = df.apply(calculate_optimal_team_size, axis=1)
        df['team_eligible'] = df['optimal_team_size'] > 1
        
        # Convert budget to 5D format
        df['budget_5d'] = df['budget'].apply(lambda x: json.dumps({
            "economic": str(x),
            "quality": "1.0",
            "temporal": "0",
            "reliability": "0.5",
            "innovation": "0"
        }))
        
        df.to_csv(f"{self.target_dir}/contracts_genesis.csv", index=False)
        
        return {
            'table': 'contracts',
            'records_processed': len(df),
            'team_eligible_count': len(df[df['team_eligible']]),
            'avg_synergy_potential': df['synergy_potential'].mean()
        }
```

### Load into New System

```sql
-- Load transformed data into Genesis system
BEGIN;

-- Create Genesis schema alongside legacy
CREATE SCHEMA IF NOT EXISTS genesis;

-- Load organizations (no transformation needed)
COPY genesis.organisations FROM '/data/migration/transform/organisations.csv' WITH CSV HEADER;

-- Load transformed wallets
COPY genesis.wallets FROM '/data/migration/transform/wallets_genesis.csv' WITH CSV HEADER;

-- Verify value conservation
WITH value_check AS (
    SELECT 
        SUM((legacy.balance)::numeric) as legacy_total,
        SUM((genesis.balances_5d->>'economic')::numeric) as genesis_total
    FROM public.wallets legacy
    JOIN genesis.wallets genesis ON legacy.id = genesis.id
)
SELECT 
    legacy_total,
    genesis_total,
    legacy_total - genesis_total as difference,
    CASE 
        WHEN ABS(legacy_total - genesis_total) < 0.01 THEN 'PASSED'
        ELSE 'FAILED'
    END as conservation_check
FROM value_check;

-- Only commit if conservation check passes
COMMIT;
```

### Verification Procedures

```sql
-- Comprehensive verification queries

-- 1. Row count verification
SELECT 
    'wallets' as table_name,
    COUNT(*) as legacy_count,
    (SELECT COUNT(*) FROM genesis.wallets) as genesis_count,
    COUNT(*) - (SELECT COUNT(*) FROM genesis.wallets) as difference
FROM public.wallets
UNION ALL
SELECT 
    'contracts',
    COUNT(*),
    (SELECT COUNT(*) FROM genesis.contracts),
    COUNT(*) - (SELECT COUNT(*) FROM genesis.contracts)
FROM public.contracts;

-- 2. Value conservation verification
WITH currency_totals AS (
    SELECT 
        SUM(balance) as legacy_economic_total,
        SUM((balances_5d->>'economic')::numeric) as genesis_economic_total,
        SUM((balances_5d->>'quality')::numeric) as genesis_quality_total,
        SUM((balances_5d->>'reliability')::numeric) as genesis_reliability_total
    FROM genesis.wallets
)
SELECT 
    *,
    legacy_economic_total - genesis_economic_total as economic_difference,
    CASE 
        WHEN ABS(legacy_economic_total - genesis_economic_total) < 1.0 THEN 'VERIFIED'
        ELSE 'DISCREPANCY DETECTED'
    END as status
FROM currency_totals;

-- 3. Relationship integrity
SELECT 
    'Orphaned bids' as check_name,
    COUNT(*) as count
FROM genesis.bids b
WHERE NOT EXISTS (
    SELECT 1 FROM genesis.contracts c 
    WHERE c.id = b.contract_id
)
UNION ALL
SELECT 
    'Orphaned agent performance',
    COUNT(*)
FROM genesis.agent_performance ap
WHERE NOT EXISTS (
    SELECT 1 FROM genesis.agent_registry ar 
    WHERE ar.id = ap.agent_id
);
```

## Zero-Downtime Strategy

### Change Data Capture (CDC) Setup

```sql
-- Enable logical replication
ALTER SYSTEM SET wal_level = logical;
ALTER SYSTEM SET max_replication_slots = 10;
ALTER SYSTEM SET max_wal_senders = 10;

-- Create publication for CDC
CREATE PUBLICATION vibelaunch_cdc FOR TABLE 
    wallets, contracts, bids, tasks, agent_performance;

-- Create replication slot
SELECT pg_create_logical_replication_slot('genesis_sync', 'pgoutput');
```

### Dual-Write Implementation

```typescript
// dual-write-service.ts
import { Pool } from 'pg';
import { Redis } from 'ioredis';

export class DualWriteService {
    private legacyPool: Pool;
    private genesisPool: Pool;
    private redis: Redis;
    
    constructor() {
        this.legacyPool = new Pool({ /* legacy config */ });
        this.genesisPool = new Pool({ /* genesis config */ });
        this.redis = new Redis();
    }
    
    async writeWallet(userId: string, transaction: any) {
        const txId = crypto.randomUUID();
        
        try {
            // Write to legacy system
            const legacyResult = await this.legacyPool.query(
                'UPDATE wallets SET balance = balance + $1 WHERE user_id = $2',
                [transaction.amount, userId]
            );
            
            // Transform for Genesis
            const genesis5D = {
                economic: transaction.amount.toString(),
                quality: "1.0",
                temporal: "0",
                reliability: await this.calculateReliability(userId),
                innovation: "0"
            };
            
            // Write to Genesis system
            const genesisResult = await this.genesisPool.query(`
                UPDATE wallets 
                SET balances_5d = balances_5d || $1::jsonb
                WHERE user_id = $2
            `, [genesis5D, userId]);
            
            // Log dual write success
            await this.redis.zadd('dual_writes', Date.now(), JSON.stringify({
                txId,
                userId,
                status: 'success',
                legacy: legacyResult.rowCount,
                genesis: genesisResult.rowCount
            }));
            
            return { success: true, txId };
            
        } catch (error) {
            // Log failure for reconciliation
            await this.redis.zadd('dual_write_failures', Date.now(), JSON.stringify({
                txId,
                userId,
                error: error.message,
                timestamp: new Date().toISOString()
            }));
            
            throw error;
        }
    }
    
    async reconcile() {
        // Run every 5 minutes to catch and fix discrepancies
        const failures = await this.redis.zrange('dual_write_failures', 0, -1);
        
        for (const failure of failures) {
            const data = JSON.parse(failure);
            // Attempt to reconcile
            await this.attemptReconciliation(data);
        }
    }
}
```

### Synchronization Mechanisms

```yaml
# docker-compose.yml for sync services
version: '3.8'

services:
  debezium:
    image: debezium/postgres:latest
    environment:
      POSTGRES_DB: vibelaunch
      POSTGRES_USER: replicator
      POSTGRES_PASSWORD: ${REPLICATOR_PASSWORD}
    ports:
      - "5433:5432"
    volumes:
      - ./postgres-replication.conf:/etc/postgresql/postgresql.conf
      
  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      
  sync-service:
    build: ./sync-service
    environment:
      KAFKA_BROKERS: kafka:9092
      LEGACY_DB_URL: ${LEGACY_DB_URL}
      GENESIS_DB_URL: ${GENESIS_DB_URL}
    depends_on:
      - kafka
      - debezium
```

### Real-time Sync Monitoring

```sql
-- Monitoring views for sync status
CREATE VIEW sync_status AS
WITH lag_check AS (
    SELECT 
        MAX(legacy.updated_at) as legacy_latest,
        MAX(genesis.updated_at) as genesis_latest,
        EXTRACT(EPOCH FROM (
            MAX(legacy.updated_at) - MAX(genesis.updated_at)
        )) as lag_seconds
    FROM public.wallets legacy
    FULL OUTER JOIN genesis.wallets genesis ON legacy.id = genesis.id
)
SELECT 
    lag_seconds,
    CASE 
        WHEN lag_seconds < 1 THEN 'REAL_TIME'
        WHEN lag_seconds < 60 THEN 'NEAR_REAL_TIME'
        WHEN lag_seconds < 300 THEN 'DELAYED'
        ELSE 'CRITICAL_LAG'
    END as sync_status,
    legacy_latest,
    genesis_latest
FROM lag_check;

-- Alert on sync issues
CREATE OR REPLACE FUNCTION check_sync_health() RETURNS void AS $$
DECLARE
    v_lag_seconds numeric;
BEGIN
    SELECT lag_seconds INTO v_lag_seconds FROM sync_status;
    
    IF v_lag_seconds > 300 THEN
        PERFORM pg_notify('sync_alert', json_build_object(
            'severity', 'critical',
            'lag_seconds', v_lag_seconds,
            'message', 'Genesis sync lag exceeds 5 minutes'
        )::text);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Schedule health checks
SELECT cron.schedule('sync-health-check', '* * * * *', 'SELECT check_sync_health()');
```

## Migration Validation

### Pre-Migration Checklist
- [ ] Backup completed and verified
- [ ] Genesis infrastructure tested at 150% capacity
- [ ] CDC replication confirmed working
- [ ] Dual-write service deployed and tested
- [ ] Monitoring dashboards configured
- [ ] Rollback procedures validated

### During Migration Monitoring
- Real-time sync lag < 1 second
- Zero data discrepancies in reconciliation
- No failed dual writes
- Application performance maintained
- User experience unchanged

### Post-Migration Verification
- [ ] All records migrated (count verification)
- [ ] Value conservation verified (financial audit)
- [ ] Relationships intact (referential integrity)
- [ ] Performance metrics equal or better
- [ ] No user-reported issues

## Rollback Procedures

See `rollback-procedures.md` for detailed rollback steps.

## Conclusion

These migration procedures ensure a safe, verifiable transformation from VibeLaunch's current single-currency system to the revolutionary five-dimensional Genesis economy. Through careful ETL processes, real-time synchronization, and comprehensive validation, we guarantee zero data loss and continuous service availability during the migration.