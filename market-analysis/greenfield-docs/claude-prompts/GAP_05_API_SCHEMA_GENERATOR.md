# Gap-Filling Prompt 5: API Schema Generator

## 🎯 Your Mission
You are tasked with creating complete, production-ready API schemas for the VibeLaunch greenfield project. The `05-api-specifications/` directory has documentation but lacks actual schema files (OpenAPI, GraphQL, Proto). Your job is to generate comprehensive schemas that fully capture the revolutionary five-dimensional currency system and 95%+ efficiency marketplace.

## 📋 CRITICAL Context You Need to Understand

### Project Root and Working Directory
- **Project Root**: `/Users/<USER>/Documents/GitHub/V2/vibelaunch`
- **Your Working Directory**: `market-analysis/greenfield-docs/`
- **All paths in this prompt are relative to PROJECT ROOT**

### What Makes These APIs Special
The VibeLaunch APIs must support:
- **Five-dimensional currency** operations (₥◈⧗☆◊)
- **95%+ market efficiency** calculations
- **194.4% team synergy** computations  
- **Real-time market operations** at 10,000+ TPS
- **Complex economic laws** enforcement

### Current State
The `market-analysis/greenfield-docs/05-api-specifications/` directory has documentation but missing schemas:
```
05-api-specifications/
├── README.md
├── rest-api/
│   ├── README.md
│   ├── openapi.yaml [MISSING or incomplete]
│   ├── endpoint-documentation.md [exists]
│   └── authentication.md [exists]
├── graphql/
│   ├── README.md
│   ├── schema.graphql [MISSING or incomplete]
│   ├── resolver-specs.md [exists]
│   └── subscription-patterns.md [exists]
├── grpc/
│   └── genesis-services.proto [may exist]
└── websockets/
    ├── README.md
    ├── event-specifications.md [exists]
    └── connection-management.md [exists]
```

## 📚 Context Files You MUST Review

### API Documentation
**Location**: `market-analysis/greenfield-docs/05-api-specifications/`
- Read ALL existing documentation files
- Understand endpoint requirements
- Review authentication patterns
- Study event specifications

### Economic Foundation
**Location**: `market-analysis/greenfield-docs/03-economic-foundation/`
- Currency specifications for data types
- Economic formulas for calculations
- Market efficiency requirements

### Data Models
**Location**: `market-analysis/greenfield-docs/06-data-models/`
- Database schema for entity definitions
- Relationships for API design
- Validation rules

### Examples
**Location**: `market-analysis/greenfield-docs/09-examples/`
- API usage examples
- Expected request/response formats

## 📝 Your Specific Tasks

### Task 1: OpenAPI 3.0 Schema
**File**: `05-api-specifications/rest-api/openapi.yaml`

Create comprehensive OpenAPI specification:

```yaml
openapi: 3.0.3
info:
  title: VibeLaunch Genesis API
  description: Revolutionary AI-powered marketplace with 5D currency system
  version: 1.0.0
  contact:
    name: VibeLaunch Team
    email: <EMAIL>

servers:
  - url: https://api.vibelaunch.com/v1
    description: Production
  - url: https://staging-api.vibelaunch.com/v1
    description: Staging

components:
  schemas:
    Currency:
      type: object
      description: Five-dimensional currency value
      properties:
        economic:
          type: number
          description: Economic value (₥)
          minimum: 0
        quality:
          type: number
          description: Quality multiplier (◈) 0-2x range
          minimum: 0
          maximum: 2
        temporal:
          type: number
          description: Temporal value (⧗) with decay
          minimum: 0
        reliability:
          type: number
          description: Reliability score (☆) with yield
          minimum: 0
          maximum: 1
        innovation:
          type: number
          description: Innovation factor (◊) with appreciation
          minimum: 0
      required: [economic, quality, temporal, reliability, innovation]

    Contract:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        budget:
          $ref: '#/components/schemas/Currency'
        requirements:
          type: object
          properties:
            teamSize:
              type: integer
              minimum: 1
              maximum: 5
            capabilities:
              type: array
              items:
                type: string
        status:
          type: string
          enum: [draft, active, in_progress, completed, cancelled]
        efficiency:
          type: number
          description: Market efficiency percentage
          minimum: 0
          maximum: 100

paths:
  /contracts:
    post:
      summary: Create a new contract
      operationId: createContract
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Contract'
      responses:
        '201':
          description: Contract created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contract'

  /contracts/{contractId}/bids:
    post:
      summary: Submit a bid
      operationId: submitBid
      parameters:
        - name: contractId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                agentIds:
                  type: array
                  items:
                    type: string
                proposal:
                  $ref: '#/components/schemas/Currency'
                synergy:
                  type: number
                  description: Team synergy percentage
```

Continue building out:
- All endpoints from `endpoint-documentation.md`
- Security schemes from `authentication.md`
- Error responses
- Rate limiting headers
- Pagination parameters
- Filtering and sorting
- Webhook endpoints

### Task 2: GraphQL Schema
**File**: `05-api-specifications/graphql/schema.graphql`

Create complete GraphQL schema:

```graphql
# Five-dimensional currency system
type Currency {
  economic: Float!
  quality: Float!
  temporal: Float!
  reliability: Float!
  innovation: Float!
  totalValue: Float!
}

input CurrencyInput {
  economic: Float!
  quality: Float!
  temporal: Float!
  reliability: Float!
  innovation: Float!
}

# Contract management
type Contract {
  id: ID!
  title: String!
  description: String!
  budget: Currency!
  requirements: ContractRequirements!
  status: ContractStatus!
  efficiency: Float!
  createdAt: DateTime!
  updatedAt: DateTime!
  bids: [Bid!]!
  selectedBid: Bid
}

type ContractRequirements {
  teamSize: Int!
  capabilities: [String!]!
  deadline: DateTime
  qualityThreshold: Float
}

enum ContractStatus {
  DRAFT
  ACTIVE
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

# Agent system
type Agent {
  id: ID!
  name: String!
  capabilities: [String!]!
  performance: AgentPerformance!
  reliability: Float!
  innovation: Float!
  teams: [Team!]!
}

type AgentPerformance {
  totalContracts: Int!
  successRate: Float!
  averageQuality: Float!
  efficiencyScore: Float!
}

# Team formation with 194.4% synergy
type Team {
  id: ID!
  agents: [Agent!]!
  synergy: Float!
  capabilities: [String!]!
  performance: TeamPerformance!
}

# Bidding system
type Bid {
  id: ID!
  contract: Contract!
  team: Team!
  proposal: Currency!
  efficiency: Float!
  estimatedCompletion: DateTime!
  status: BidStatus!
}

# Market operations
type Market {
  efficiency: Float!
  totalVolume: Currency!
  activeContracts: Int!
  averageSynergy: Float!
}

# Queries
type Query {
  # Contract queries
  contract(id: ID!): Contract
  contracts(
    status: ContractStatus
    minEfficiency: Float
    first: Int
    after: String
  ): ContractConnection!
  
  # Agent queries
  agent(id: ID!): Agent
  agents(
    capabilities: [String!]
    minReliability: Float
    first: Int
    after: String
  ): AgentConnection!
  
  # Market queries
  marketStatus: Market!
  calculateSynergy(agentIds: [ID!]!): Float!
  estimateEfficiency(contractId: ID!, bidId: ID!): Float!
}

# Mutations
type Mutation {
  # Contract operations
  createContract(input: CreateContractInput!): Contract!
  updateContract(id: ID!, input: UpdateContractInput!): Contract!
  cancelContract(id: ID!): Contract!
  
  # Bidding operations
  submitBid(contractId: ID!, input: SubmitBidInput!): Bid!
  selectBid(contractId: ID!, bidId: ID!): Contract!
  
  # Agent operations
  registerAgent(input: RegisterAgentInput!): Agent!
  updateAgentCapabilities(id: ID!, capabilities: [String!]!): Agent!
  
  # Currency operations
  transferCurrency(input: TransferCurrencyInput!): Transaction!
  exchangeCurrency(input: ExchangeCurrencyInput!): Transaction!
}

# Subscriptions for real-time updates
type Subscription {
  # Contract events
  contractCreated: Contract!
  contractUpdated(id: ID!): Contract!
  
  # Bid events
  bidSubmitted(contractId: ID!): Bid!
  bidSelected(contractId: ID!): Bid!
  
  # Market events
  marketEfficiencyUpdate: Market!
  currencyExchangeRates: ExchangeRates!
}
```

Continue with:
- Input types for mutations
- Connection types for pagination
- Custom scalars (DateTime, Currency)
- Interfaces for common patterns
- Unions for polymorphic returns
- Directives for auth and validation

### Task 3: Protocol Buffers (gRPC)
**File**: `05-api-specifications/grpc/market-engine.proto`

Create gRPC service definitions:

```protobuf
syntax = "proto3";

package vibelaunch.market.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// Five-dimensional currency message
message Currency {
  double economic = 1;
  double quality = 2;
  double temporal = 3;
  double reliability = 4;
  double innovation = 5;
}

// Market efficiency service
service MarketEngine {
  // Calculate market efficiency
  rpc CalculateEfficiency(CalculateEfficiencyRequest) returns (CalculateEfficiencyResponse);
  
  // Stream market updates
  rpc StreamMarketUpdates(Empty) returns (stream MarketUpdate);
  
  // Process currency exchange
  rpc ExchangeCurrency(ExchangeRequest) returns (ExchangeResponse);
}

service TeamFormation {
  // Calculate team synergy
  rpc CalculateSynergy(TeamSynergyRequest) returns (TeamSynergyResponse);
  
  // Find optimal team
  rpc FindOptimalTeam(OptimalTeamRequest) returns (OptimalTeamResponse);
}
```

Include:
- All core services
- Message definitions
- Streaming RPCs
- Error handling
- Field validation rules

### Task 4: WebSocket Events
**File**: `05-api-specifications/websockets/events.json`

Create WebSocket event schemas:

```json
{
  "events": {
    "contract.created": {
      "description": "New contract created in marketplace",
      "payload": {
        "contractId": "string",
        "title": "string",
        "budget": {
          "economic": "number",
          "quality": "number",
          "temporal": "number",
          "reliability": "number",
          "innovation": "number"
        },
        "efficiency": "number",
        "timestamp": "string"
      }
    },
    "bid.submitted": {
      "description": "New bid submitted for contract",
      "payload": {
        "bidId": "string",
        "contractId": "string",
        "teamId": "string",
        "proposal": "Currency",
        "synergy": "number",
        "timestamp": "string"
      }
    },
    "market.efficiency.update": {
      "description": "Market efficiency changed",
      "payload": {
        "efficiency": "number",
        "delta": "number",
        "factors": {
          "information_symmetry": "number",
          "transaction_costs": "number",
          "liquidity": "number"
        },
        "timestamp": "string"
      }
    }
  }
}
```

Define all events from `event-specifications.md`.

### Task 5: API Documentation Enhancements
**File**: `05-api-specifications/rest-api/postman-collection.json`

Create Postman collection with:
- All endpoints
- Example requests
- Environment variables
- Pre-request scripts
- Tests

**File**: `05-api-specifications/api-sdk-spec.md`

Create SDK specification:
- TypeScript interfaces
- Python type hints
- Go structs
- Error handling patterns
- Retry logic

## 📋 Schema Standards

### Design Principles:
- **Type Safety**: Strong typing throughout
- **Validation**: Built-in constraints
- **Versioning**: Future-proof design
- **Documentation**: Inline descriptions
- **Consistency**: Naming conventions

### Include for All APIs:
- Authentication/Authorization
- Rate limiting
- Pagination
- Filtering and sorting
- Error responses
- Versioning strategy

### Special Considerations:
- Five-dimensional currency operations
- Atomic multi-currency transactions
- Real-time efficiency calculations
- Team synergy computations
- Economic law enforcement

## ✅ Success Criteria

### Completeness
- [ ] All endpoints documented in schemas
- [ ] All data types defined
- [ ] All operations specified
- [ ] All events documented

### Correctness
- [ ] Currency types handle 5 dimensions
- [ ] Efficiency calculations included
- [ ] Synergy operations defined
- [ ] Economic laws enforced

### Usability
- [ ] Schemas are valid
- [ ] Examples included
- [ ] Documentation clear
- [ ] SDK-friendly design

### Production Readiness
- [ ] Security implemented
- [ ] Performance considered
- [ ] Versioning planned
- [ ] Monitoring supported

## 🚨 Important Notes

### What to Focus On
- **Currency Precision**: Financial accuracy critical
- **Real-time Support**: WebSockets and streaming
- **Type Safety**: Prevent runtime errors
- **Performance**: Design for 10,000+ TPS

### What NOT to Do
- **Don't simplify currency**: All 5 dimensions required
- **Don't skip validation**: Enforce constraints
- **Don't ignore errors**: Define all error cases
- **Don't forget versioning**: Plan for evolution

### Integration Requirements
- Align with data models in `06-data-models/`
- Support examples in `09-examples/`
- Enable testing from `10-testing/`
- Match architecture in `04-target-architecture/`

## 📊 Estimated Time
**Total Time**: 2-3 hours
- OpenAPI Schema: 1 hour
- GraphQL Schema: 0.5 hours
- gRPC Definitions: 0.5 hours
- WebSocket Events: 0.5 hours
- Documentation: 0.5 hours

You are creating the contract between the revolutionary VibeLaunch system and the world. Make these schemas comprehensive, accurate, and developer-friendly!