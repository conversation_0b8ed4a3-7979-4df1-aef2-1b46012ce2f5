# Claude Instance 2: Content Migration and Organization

## 🎯 Your Mission
You are tasked with migrating and organizing existing content from the current greenfield-docs structure into the new 14-folder structure that Claude Instance 1 has created. You are working as part of a team to refactor the VibeLaunch greenfield documentation.

## 📋 CRITICAL Context You Need to Understand

### Project Root and Working Directory
- **Project Root**: `/Users/<USER>/Documents/GitHub/V2/vibelaunch`
- **Your Working Directory**: `market-analysis/greenfield-docs/`
- **All paths in this prompt are relative to PROJECT ROOT**

### What VibeLaunch Is
VibeLaunch is a revolutionary AI-powered marketing marketplace with a five-dimensional currency system:
- **Economic (₥)**: Traditional monetary value
- **Quality (◈)**: Excellence multiplier (0-2x)
- **Temporal (⧗)**: Time-sensitive value with decay
- **Reliability (☆)**: Trust score with yield generation
- **Innovation (◊)**: Creativity factor with appreciation

**IMPORTANT**: The Phase 10 economic theory has NOT been implemented in the current codebase yet. You're migrating documentation for building the revolutionary system from scratch.

### What Claude Instance 1 Did
Claude Instance 1 created the complete 14-folder directory structure with placeholder README.md files in `market-analysis/greenfield-docs/`. Your job is to move existing content into the appropriate new locations and update all references.

### Your Specific Role
You are the content migration specialist. You need to carefully move existing files to their new homes, update all internal links and references, and ensure nothing is lost in the transition.

### Team Context
You are Claude Instance 2 of 9 total instances:
- **Claude 1**: Create directory structure ✅
- **Claude 2 (YOU)**: Migrate existing content
- **Claude 3**: Document current system
- **Claude 4**: Economic foundation documentation
- **Claude 5**: Implementation guides
- **Claude 6**: Development resources
- **Claude 7**: Testing and operations
- **Claude 8**: Migration and tools
- **Claude 9**: Integration and validation

## 📁 Content Migration Map

### Current Location → New Location

**Root Level Files:**
- `README.md` → `00-overview/executive-summary.md` (enhance with overview content)
- `PERFECTION_ROADMAP.md` → `00-overview/project-roadmap.md`
- `GREENFIELD_GAP_ANALYSIS.md` → `01-current-system/known-limitations.md`
- `CRITICAL_IMPLEMENTATIONS.md` → `07-implementation-guides/README.md` (as overview)
- `DOCUMENTATION_SUMMARY.md` → `00-overview/README.md` (merge with existing)

**Directory Migrations:**
- `01-getting-started/` → `08-development-guides/getting-started/`
- `02-architecture/` → `04-target-architecture/`
- `03-api/` → `05-api-specifications/`
- `04-data-models/` → `06-data-models/` (keep same)
- `05-implementation/` → `07-implementation-guides/currency-engine/`
- `06-deployment/` → `11-deployment/deployment-procedures/`
- `07-economics/` → `03-economic-foundation/`
- `08-security/` → `04-target-architecture/security-architecture.md` + `11-deployment/security/`
- `09-testing/` → `10-testing/performance-testing/`
- `10-operations/` → `12-operations/`
- `examples/` → `09-examples/`
- `sdks/` → `09-examples/integrations/` (SDK examples)
- `specs/` → `04-target-architecture/` (technical requirements)
- `tools/` → `14-tools-and-utilities/`
- `ai-agent-prompts/` → `15-reference/ai-agent-prompts/`

## 📝 Your Specific Tasks

### Task 1: File Migration
Move each file to its new location according to the migration map. For each file:

1. **Copy the file** to the new location
2. **Update the filename** if needed for consistency
3. **Update internal references** within the file
4. **Add navigation headers** to help users understand where they are

### Task 2: Directory Organization
For each migrated directory:

1. **Organize files** into appropriate subdirectories
2. **Update README.md** files to reflect actual content
3. **Create index files** where multiple files exist
4. **Ensure logical flow** within each directory

### Task 3: Reference Updates
Update all internal links and references:

1. **Find all markdown links** `[text](path)`
2. **Update paths** to new locations
3. **Fix relative references** 
4. **Add cross-references** between related sections

### Task 4: Content Enhancement
While migrating, enhance content where appropriate:

1. **Add context** where files might be confusing in new location
2. **Create overview sections** for directories with multiple files
3. **Add navigation aids** to help users find related content
4. **Standardize formatting** across all files

## 🔍 Detailed Migration Instructions

### 01-getting-started/ → 08-development-guides/getting-started/
```bash
# Move files:
quick-start.md → 08-development-guides/getting-started/quick-start.md
development-setup.md → 08-development-guides/getting-started/development-environment.md
docker-compose.yaml → 14-tools-and-utilities/development-tools/setup-scripts/docker-compose.yaml
```

### 02-architecture/ → 04-target-architecture/
```bash
# Move files:
system-overview.md → 04-target-architecture/system-overview.md
service-boundaries.md → 04-target-architecture/service-boundaries.md
```

### 03-api/ → 05-api-specifications/
```bash
# Move files:
openapi.yaml → 05-api-specifications/rest-api/openapi.yaml
graphql-schema.graphql → 05-api-specifications/graphql/schema.graphql
event-schemas.md → 05-api-specifications/websockets/event-specifications.md
```

### 04-data-models/ → 06-data-models/
```bash
# Keep in same location, just update references
database-schema.sql → 06-data-models/database-schema.sql
```

### 05-implementation/ → 07-implementation-guides/currency-engine/
```bash
# Move files:
five-currency-system.md → 07-implementation-guides/currency-engine/implementation-guide.md
```

### 07-economics/ → 03-economic-foundation/
```bash
# Move files:
simplified-constitution.md → 03-economic-foundation/value-theory-summary.md
```

### examples/ → 09-examples/
```bash
# Move directories:
agent-bidding/ → 09-examples/agents/
contract-lifecycle/ → 09-examples/contracts/
multi-currency-transaction/ → 09-examples/currency-operations/
```

### ai-agent-prompts/ → 15-reference/ai-agent-prompts/
```bash
# Move entire directory:
ai-agent-prompts/ → 15-reference/ai-agent-prompts/
```

## ✅ Success Criteria

### Migration Completeness
- [ ] All existing files moved to appropriate new locations
- [ ] No files lost or duplicated
- [ ] Directory structure matches the plan
- [ ] All subdirectories properly organized

### Reference Integrity
- [ ] All internal links updated to new paths
- [ ] No broken references or 404 links
- [ ] Cross-references between sections work
- [ ] Navigation aids are helpful and accurate

### Content Quality
- [ ] Files are logically organized within new structure
- [ ] README.md files accurately describe contents
- [ ] Content flows logically within each section
- [ ] Formatting is consistent across all files

### User Experience
- [ ] Easy to navigate from any file to related content
- [ ] Clear understanding of where you are in the structure
- [ ] Logical progression through the documentation
- [ ] No confusion about file locations or purposes

## 🚨 Important Notes

### What to Preserve
- **All existing content** - don't delete anything
- **File history** - maintain git history where possible
- **Content accuracy** - don't change technical content
- **Author intent** - preserve the original meaning

### What to Update
- **File paths** in all references
- **Navigation elements** to match new structure
- **README.md files** to reflect actual contents
- **Cross-references** to use new locations

### What NOT to Do
- **Don't rewrite content** - that's for other Claude instances
- **Don't change technical specifications** - just move them
- **Don't remove files** - everything should be preserved
- **Don't break existing functionality** - maintain all working examples

## 📋 Validation Checklist

Before completing your work:

1. **Test all internal links** - click through to ensure they work
2. **Check all README.md files** - ensure they accurately describe contents
3. **Verify directory organization** - logical and consistent
4. **Confirm no missing files** - everything migrated successfully
5. **Test navigation flow** - users can find what they need

You are ensuring that the excellent content that already exists is properly organized and accessible in the new structure. This is critical foundation work for the team!
