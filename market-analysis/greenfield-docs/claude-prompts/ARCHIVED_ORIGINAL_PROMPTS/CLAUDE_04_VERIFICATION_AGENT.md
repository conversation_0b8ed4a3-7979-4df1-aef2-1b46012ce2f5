# VibeLaunch Greenfield Documentation - Verification & Enhancement Agent

## 🎯 Your Mission
You are an AI agent with fresh context, tasked with a critical mission: to audit, verify, and enhance the existing documentation of the VibeLaunch system. A previous agent has just completed a deep-dive analysis and rewritten the documentation, but a final, rigorous check is required to ensure it is 100% accurate, comprehensive, and ready for the greenfield development team. Your job is to be the ultimate quality assurance check.

## 📋 CRITICAL Context You Need to Understand

The VibeLaunch system is far more complex than it appears on the surface. A previous agent discovered several critical, undocumented architectural patterns. Your primary goal is to validate these findings and ensure they are perfectly documented.

### Key Architectural Discoveries to Verify:

1.  **Model Context Protocol (MCP):** The system uses a custom, event-driven protocol called MCP for most inter-service communication. This is not a standard REST API. It is implemented on top of **Supabase Realtime channels**.
    *   **Verification Point:** Examine `packages/agent/src/mcp-connector-improved.ts`. Does the documentation in `architecture-overview.md` and `api-documentation.md` accurately reflect how this connector works?

2.  **Dual Message Bus Architecture:** The system uses **two** separate message buses:
    *   **Supabase Realtime:** For synchronous, request/response style communication via the MCP.
    *   **Redis Streams:** For asynchronous, fire-and-forget events.
    *   **Verification Point:** The documentation claims this dual system exists. Verify this by checking for `ioredis` usage in the codebase (e.g., `packages/agent/`, `packages/redis-streams/`) and Supabase client usage for realtime events.

3.  **Incomplete Migration to Redis Streams:** The system is in a transitional state. It is migrating from a legacy database-polling webhook system to Redis Streams. This migration is not finished.
    *   **Verification Point:** The `packages/worker/package.json` shows it still uses the old webhook system (`webhookQueueWorker.ts`) and lacks a Redis dependency. The `packages/redis-streams/` package contains migration scripts. Does the documentation in `service-inventory.md` and `known-limitations.md` accurately capture this transitional state?

4.  **Duplicated LLM Logic:** There is significant code duplication for handling LLM calls.
    *   The `packages/llm/` service is intended to be a shared LLM layer.
    *   However, the `packages/agent/` service contains its own, separate LLM implementation (`packages/agent/src/lib/llm.ts`).
    *   **Verification Point:** Confirm this duplication exists. Does the documentation clearly call this out as a major source of technical debt in `known-limitations.md` and `evolution-opportunities.md`?

5.  **"Database as an API":** A significant amount of business logic is embedded directly in the database as PostgreSQL functions (e.g., `get_llm_config`).
    *   **Verification Point:** Examine the migration files in `supabase/migrations/`. Does the `database-schema.md` file correctly identify this pattern and its implications?

### Your Starting Point

The documentation you need to review is located in: `market-analysis/greenfield-docs/01-current-system/`. It consists of 8 markdown files.

## 📝 Your Specific Tasks

Your mission is to perform a comprehensive audit and enhancement of these 8 documents.

### Task 1: Deep Verification
For each of the 8 documents, you must:
1.  **Read the document.**
2.  **Read the relevant source code files** that the document refers to (or should refer to).
3.  **Compare the documentation to the code.** Is the documentation accurate? Is it complete? Does it miss any nuances?

**Example Workflow:**
1.  Read `architecture-overview.md`.
2.  The document mentions the MCP. To verify, `read_file` on `packages/agent/src/mcp-connector-improved.ts`.
3.  Does the explanation of the MCP in the documentation match the implementation in the code? If not, correct the documentation.

### Task 2: Enhancement
As you verify the documentation, look for opportunities to improve it.
-   **Add More Detail:** Can you add more specific code examples to illustrate a point?
-   **Improve Clarity:** Can you rephrase a section to make it easier to understand?
-   **Create Visuals:** Can you add more Mermaid diagrams (sequence diagrams, component diagrams) to clarify complex interactions? For example, a diagram showing the dual message bus architecture would be very valuable.
-   **Fill Gaps:** Did the previous agent miss anything? For example, a detailed analysis of the `grafana/` dashboards in the `packages/monitoring/` directory could be a valuable addition.

### Task 3: Cross-Referencing
Ensure that all 8 documents are consistent with each other. For example, if `architecture-overview.md` describes a service, the description should match the one in `service-inventory.md`.

### Task 4: Exploratory Analysis
Your final task is to go beyond the existing documentation and look for anything that might have been missed. You must systematically investigate the entire codebase.

**Key Directories to Investigate:**

-   **`packages/`**: The core application services. Verify the roles and responsibilities of each service.
-   **`railway/`**: Railway-specific deployment configurations and scripts. This is where the lightweight `master-agent` was discovered. Look for any other service definitions or deployment logic.
-   **`scripts/`**: A rich source of automation scripts for builds, migrations, and hotfixes. Analyze these to understand the operational history and complexity of the system.
-   **`config/`**: Contains high-level configuration files, including `docker-compose.p1.yml` and other deployment target configurations.
-   **`deployment/`**: Contains assets and documentation related to deployment, including monitoring configurations.
-   **`supabase/`**: The heart of the database configuration, including all migrations.
-   **`__tests__/`**: The testing suite. The structure of the tests can reveal assumptions about how the system is intended to work.
-   **`docs/`**: The old documentation. Review this for any historical context that might have been lost.

**Your Approach:**

-   **List files** in each directory to get a complete inventory.
-   **Read key files** (e.g., `README.md`, `package.json`, `.sh`, `.ps1`, `.yml`, `.js`, `.ts`, `.sql`) to understand their purpose.
-   **Search for keywords:** Perform broad searches for terms like `TODO`, `FIXME`, `HACK`, `XXX`, and `NOTE` to uncover any hidden issues or areas of technical debt.
-   **Question everything:** Do not assume the previous agent's analysis is complete. Your goal is to find what was missed.

## ✅ Success Criteria

Your work is complete when the documentation in `market-analysis/greenfield-docs/01-current-system/` meets the following criteria:

-   **100% Accuracy:** The documentation perfectly reflects the state of the codebase.
-   **Completeness:** There are no remaining gaps or undocumented features.
-   **Clarity:** The documentation is easy to understand for a new developer with zero prior context.
-   **Actionability:** The documentation provides a solid, reliable foundation for the greenfield development team to begin their work.

You have full authority to modify and improve the existing documentation. Be thorough, be critical, and leave no stone unturned. The success of the VibeLaunch greenfield project depends on the quality of your work.

Your final task is to go over the full codebase and make adjustments to the `market-analysis/greenfield-docs/01-current-system` as needed according to the highest standards and with full attention to detail.
