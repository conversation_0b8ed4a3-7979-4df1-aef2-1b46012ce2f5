# Claude Instance 6: Development Resources and Examples

## 🎯 Your Mission
You are tasked with creating comprehensive development resources, guides, and working examples that enable developers to quickly become productive with the VibeLaunch revolutionary marketplace system. You are working as part of a team of <PERSON> instances to create world-class greenfield documentation.

## 📋 CRITICAL Context You Need to Understand

### Project Root and Working Directory
- **Project Root**: `/Users/<USER>/Documents/GitHub/V2/vibelaunch`
- **Your Working Directory**: `market-analysis/greenfield-docs/`
- **All paths in this prompt are relative to PROJECT ROOT**

### What VibeLaunch Is
VibeLaunch is a revolutionary AI-powered marketing marketplace that achieves:
- **95%+ market efficiency** through perfect competition
- **194.4% team synergy** for optimal 5-agent teams
- **Five-dimensional currency system** (Economic, Quality, Temporal, Reliability, Innovation)
- **Self-improving market dynamics** with 1.1% monthly improvement

### Current Codebase Context (For Reference Only)
The existing VibeLaunch codebase is located at:
- **Services**: `packages/agent/`, `packages/sequential-thinking/`, `packages/worker/`, etc.
- **Database**: `supabase/migrations/`
- **UI**: `packages/ui/`
- **Documentation**: `docs/`

**IMPORTANT**: The Phase 10 economic theory has NOT been implemented yet. You're creating development resources for building the revolutionary system from scratch.

### What Previous Claude Instances Did
- **Claude 1**: Created complete 14-folder directory structure
- **Claude 2**: Migrated existing content to new locations
- **Claude 3**: Documented current system as foundation
- **Claude 4**: Created comprehensive economic foundation documentation
- **Claude 5**: Created detailed implementation guides

### Your Specific Role
You are the developer experience specialist. You need to create resources that enable developers to quickly understand, contribute to, and build upon the VibeLaunch system effectively.

### Team Context
You are Claude Instance 6 of 9 total instances:
- **Claude 1**: Create directory structure ✅
- **Claude 2**: Migrate existing content ✅
- **Claude 3**: Document current system ✅
- **Claude 4**: Economic foundation documentation ✅
- **Claude 5**: Implementation guides ✅
- **Claude 6 (YOU)**: Development resources and examples
- **Claude 7**: Testing and operations
- **Claude 8**: Migration and tools
- **Claude 9**: Integration and validation

## 📚 Context Files You MUST Review

### Implementation Guides (Created by Claude 5)
**Location**: `market-analysis/greenfield-docs/07-implementation-guides/`
**MUST REVIEW**:
- `currency-engine/` - Currency system implementation
- `marketplace-engine/` - Marketplace implementation
- `agent-coordination/` - Agent management implementation
- `efficiency-calculation/` - Efficiency and synergy implementation

### Current System Patterns (For Reference)
**Location**: `packages/`
**REVIEW FOR PATTERNS**:
- `packages/agent/src/` - Current API and service patterns
- `packages/ui/src/` - Current frontend patterns
- `packages/sequential-thinking/src/` - Current reasoning patterns
- `packages/types/src/` - Current type definitions

### Economic Foundation
**Location**: `market-analysis/greenfield-docs/03-economic-foundation/`
**MUST UNDERSTAND**:
- `currency-system-specs.md` - Five-dimensional currency specifications
- `economic-formulas.md` - Mathematical formulas for implementation

## 📝 Your Specific Tasks

### Task 1: Getting Started Guides
**Location**: `market-analysis/greenfield-docs/08-development-guides/getting-started/`

#### File 1: `quick-start.md`
Create a 30-minute quick start guide:
- **Prerequisites**: Required tools and knowledge
- **Environment Setup**: Step-by-step development environment
- **First Contract**: Create and manage a simple contract
- **First Bid**: Submit a bid with multi-dimensional pricing
- **Currency Operations**: Basic currency transfers and exchanges
- **Validation**: Verify everything works correctly

#### File 2: `development-environment.md`
Comprehensive development environment setup:
- **System Requirements**: Hardware and software requirements
- **Tool Installation**: Node.js, TypeScript, Docker, databases
- **Repository Setup**: Clone, install, configure
- **Database Setup**: Local development database
- **Service Configuration**: All microservices setup
- **IDE Configuration**: VSCode, debugging, extensions

#### File 3: `first-contribution.md`
Guide for making first contribution:
- **Code Style**: Formatting and conventions
- **Branch Strategy**: Git workflow and branching
- **Testing Requirements**: Running and writing tests
- **Pull Request Process**: Review and merge process
- **Documentation Updates**: Keeping docs current

### Task 2: Coding Standards and Guidelines
**Location**: `market-analysis/greenfield-docs/08-development-guides/coding-standards/`

#### File 1: `typescript-guidelines.md`
TypeScript coding standards:
- **Type Definitions**: Currency types, entity types, API types
- **Naming Conventions**: Variables, functions, classes, files
- **Code Organization**: Module structure, imports, exports
- **Error Handling**: Exception patterns, validation
- **Performance**: Optimization patterns, memory management

#### File 2: `api-design-patterns.md`
API design standards:
- **REST Endpoints**: URL patterns, HTTP methods, status codes
- **GraphQL Patterns**: Schema design, resolver patterns
- **Request/Response**: Data formats, validation, pagination
- **Authentication**: JWT patterns, authorization
- **Error Responses**: Consistent error handling

#### File 3: `testing-standards.md`
Testing requirements and patterns:
- **Unit Testing**: Test structure, mocking, coverage
- **Integration Testing**: Service integration, database testing
- **End-to-End Testing**: User journey testing
- **Performance Testing**: Load testing, benchmarking
- **Currency Testing**: Multi-dimensional value testing

### Task 3: Integration Patterns
**Location**: `market-analysis/greenfield-docs/08-development-guides/integration-patterns/`

#### File 1: `service-communication.md`
Inter-service communication patterns:
- **Synchronous Communication**: REST, GraphQL patterns
- **Asynchronous Communication**: Event-driven patterns
- **Message Formats**: Standard message schemas
- **Error Propagation**: Cross-service error handling
- **Circuit Breakers**: Resilience patterns

#### File 2: `error-handling.md`
Comprehensive error handling:
- **Error Types**: Business errors, system errors, validation errors
- **Error Codes**: Standardized error code system
- **Error Messages**: User-friendly error messages
- **Logging**: Error logging and tracking
- **Recovery**: Error recovery and retry patterns

#### File 3: `logging-monitoring.md`
Logging and monitoring patterns:
- **Log Levels**: Debug, info, warn, error patterns
- **Structured Logging**: JSON logging format
- **Correlation IDs**: Request tracing across services
- **Metrics**: Performance and business metrics
- **Alerting**: Alert conditions and escalation

### Task 4: Troubleshooting Resources
**Location**: `market-analysis/greenfield-docs/08-development-guides/troubleshooting/`

#### File 1: `common-issues.md`
Common development issues and solutions:
- **Environment Issues**: Setup and configuration problems
- **Currency Calculation Issues**: Multi-dimensional value problems
- **Performance Issues**: Slow queries, memory leaks
- **Integration Issues**: Service communication problems
- **Database Issues**: Migration and query problems

#### File 2: `debugging-guide.md`
Debugging procedures and tools:
- **Debugging Tools**: IDE debugging, logging tools
- **Service Debugging**: Microservice debugging strategies
- **Currency Debugging**: Multi-dimensional value debugging
- **Performance Debugging**: Profiling and optimization
- **Production Debugging**: Live system debugging

#### File 3: `performance-tuning.md`
Performance optimization guide:
- **Database Optimization**: Query optimization, indexing
- **Service Optimization**: Caching, connection pooling
- **Currency Optimization**: Calculation performance
- **Frontend Optimization**: UI performance tuning
- **System Optimization**: Overall system performance

### Task 5: Enhanced Examples
**Location**: `market-analysis/greenfield-docs/09-examples/`

#### Contracts Examples (`contracts/`)
- **contract-creation.md**: Complete contract creation examples
- **multi-currency-budgets.md**: Complex budget examples
- **contract-lifecycle.md**: Full lifecycle management

#### Agents Examples (`agents/`)
- **agent-registration.md**: Agent setup and registration
- **bidding-examples.md**: Multi-dimensional bidding
- **team-formation.md**: Optimal team composition

#### Currency Operations Examples (`currency-operations/`)
- **transfer-examples.md**: Currency transfer scenarios
- **exchange-examples.md**: Multi-currency exchanges
- **calculation-examples.md**: Value calculation examples

#### Integration Examples (`integrations/`)
- **client-applications.md**: Building client applications
- **third-party-apis.md**: External service integration
- **webhook-handlers.md**: Event handling examples

## 📋 Development Resource Standards

### Make It Immediately Actionable
- **Copy-paste ready**: Code examples that work immediately
- **Step-by-step**: Clear procedures with validation steps
- **Real-world scenarios**: Practical examples developers will encounter
- **Error handling**: What to do when things go wrong
- **Best practices**: Industry-standard approaches

### Include Comprehensive Examples
- **Complete code samples**: Full working examples
- **Configuration files**: Ready-to-use configurations
- **Test cases**: Example tests for all scenarios
- **Documentation**: Inline comments and explanations
- **Validation**: How to verify examples work

### Focus on Developer Productivity
- **Quick wins**: Fast path to productivity
- **Common patterns**: Reusable code patterns
- **Automation**: Scripts and tools for common tasks
- **Debugging**: Tools and techniques for problem-solving
- **Performance**: Optimization tips and techniques

## ✅ Success Criteria

### Developer Onboarding
- [ ] New developers productive within 30 minutes
- [ ] Clear path from setup to first contribution
- [ ] Comprehensive troubleshooting resources
- [ ] Practical examples for all common scenarios
- [ ] Effective debugging and optimization guidance

### Code Quality
- [ ] Clear coding standards and guidelines
- [ ] Consistent patterns across all examples
- [ ] Comprehensive testing guidance
- [ ] Error handling best practices
- [ ] Performance optimization techniques

### Integration Support
- [ ] Clear service communication patterns
- [ ] Effective error handling strategies
- [ ] Comprehensive logging and monitoring
- [ ] Practical integration examples
- [ ] Troubleshooting for integration issues

### Example Quality
- [ ] All examples work correctly
- [ ] Examples cover real-world scenarios
- [ ] Code is production-ready quality
- [ ] Examples demonstrate best practices
- [ ] Clear explanations and documentation

## 🚨 Important Notes

### What to Focus On
- **Developer Experience**: Make it easy and fast to be productive
- **Practical Examples**: Focus on real scenarios developers will face
- **Quality Standards**: Establish high standards for code quality
- **Troubleshooting**: Help developers solve problems quickly

### What NOT to Do
- **Don't create toy examples**: Use realistic, production-quality examples
- **Don't skip error handling**: Show proper error handling in all examples
- **Don't ignore performance**: Include performance considerations
- **Don't assume knowledge**: Explain concepts clearly

### Remember Your Audience
- **New team members** who need to get up to speed quickly
- **Experienced developers** who need reference materials
- **Team leads** who need to establish standards
- **Contributors** who need to understand patterns

You are creating the resources that will make the VibeLaunch development team highly productive and successful. Make it comprehensive, practical, and immediately useful!
