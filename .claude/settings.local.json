{"permissions": {"allow": ["Bash(ls:*)", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(rmdir:*)", "Bash(rm:*)", "Bash(find:*)", "mcp__claude-code__Task", "Bash(git add:*)", "Bash(git push:*)", "Bash(pdftotext:*)", "Bash(pip install:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "mcp__filesystem__read_multiple_files", "mcp__memory__create_relations", "mcp__memory__create_entities", "mcp__memory__open_nodes", "mcp__memory__search_nodes", "mcp__filesystem__directory_tree", "mcp__filesystem__read_file", "mcp__filesystem__list_directory"], "deny": []}}